{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=template&id=5888aa98&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}