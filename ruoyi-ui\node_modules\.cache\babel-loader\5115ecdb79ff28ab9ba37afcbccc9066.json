{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_QuestionCard", "_interopRequireDefault", "require", "_QuestionForm", "_question", "name", "components", "QuestionCard", "QuestionForm", "data", "bankId", "bankName", "statistics", "total", "singleChoice", "multipleChoice", "judgment", "questionList", "queryParams", "pageNum", "pageSize", "questionType", "difficulty", "questionContent", "expandAll", "expandedQuestions", "selectedQuestions", "isAllSelected", "questionFormVisible", "currentQuestionType", "currentQuestionData", "importDrawerVisible", "documentContent", "documentHtmlContent", "parsedQuestions", "parseErrors", "allExpanded", "isSettingFromBackend", "last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentImportDialogVisible", "rulesDialogVisible", "activeRuleTab", "isUploading", "isParsing", "importingQuestions", "importProgress", "importOptions", "reverse", "allowDuplicate", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "uploadHeaders", "Authorization", "$store", "getters", "token", "uploadData", "rich<PERSON><PERSON><PERSON>", "editorInitialized", "watch", "$route", "to", "from", "query", "initPage", "handler", "newVal", "length", "stripHtmlTagsKeepImages", "trim", "debounceParseDocument", "immediate", "_this", "clearImportContent", "$nextTick", "initRichEditor", "destroy", "created", "debounce", "parseDocument", "debounceEditorContentChange", "handleEditorContentChangeDebounced", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "cancel", "methods", "_this$$route$query", "$message", "error", "goBack", "getQuestionList", "getStatistics", "$router", "push", "_this2", "params", "convertQueryParams", "listQuestion", "then", "response", "rows", "catch", "convertedParams", "_objectSpread2", "default", "typeMap", "difficultyMap", "Object", "keys", "for<PERSON>ach", "key", "undefined", "_this3", "getQuestionStatistics", "handleBatchImportClick", "handleAddQuestion", "type", "toggleExpandAll", "handleExportQuestions", "_this4", "$confirm", "concat", "confirmButtonText", "cancelButtonText", "loading", "$loading", "lock", "text", "spinner", "background", "exportQuestionsToWord", "close", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "success", "console", "handleToggleSelectAll", "map", "q", "questionId", "handleBatchDelete", "_this5", "warning", "deleteCount", "confirmMessage", "dangerouslyUseHTMLString", "performBatchDelete", "info", "_this6", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "questionIds", "startTime", "endTime", "duration", "errorMessage", "_t", "w", "_context", "n", "p", "join", "Date", "now", "delQuestion", "toFixed", "v", "msg", "message", "a", "handleQuestionSelect", "selected", "includes", "index", "indexOf", "splice", "handleToggleExpand", "_this7", "question", "handleEditQuestion", "handleCopyQuestion", "copiedQuestion", "createTime", "updateTime", "createBy", "updateBy", "convertQuestionTypeToString", "handleDeleteQuestion", "_this8", "replace", "displayContent", "substring", "handleQuestionFormSuccess", "handleDrawerClose", "done", "_this9", "<PERSON><PERSON><PERSON><PERSON>", "hasParsedQuestions", "showDocumentImportDialog", "_this0", "uploadComponent", "$refs", "documentUpload", "clearFiles", "showRulesDialog", "copyExampleToEditor", "_this1", "htmlTemplate", "setData", "downloadWordTemplate", "beforeUpload", "file", "isValidType", "endsWith", "isLt10M", "size", "handleUploadSuccess", "_this10", "code", "setTimeout", "questions", "collapsed", "errors", "errorCount", "originalContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleUploadError", "toggleQuestion", "$set", "toggleAllQuestions", "_this11", "confirmImport", "_this12", "optionMessages", "importQuestions", "_this13", "_callee2", "questionsToImport", "importData", "result", "successCount", "failCount", "skippedCount", "resultMessage", "skippedErrors", "_t2", "_context2", "_toConsumableArray2", "batchImportQuestions", "warn", "filter", "Error", "f", "formatProgress", "percentage", "_this14", "CKEDITOR", "fallbackToTextarea", "<PERSON><PERSON><PERSON><PERSON>", "getElementById", "innerHTML", "showFallbackEditor", "_defineProperty2", "height", "toolbar", "items", "removeButtons", "language", "removePlugins", "resize_enabled", "extraPlugins", "<PERSON><PERSON><PERSON><PERSON>", "fontSize_sizes", "fontSize_defaultLabel", "colorButton_enableMore", "colorButton_colors", "filebrowserUploadUrl", "image_previewText", "baseHref", "instanceReady", "evt", "editor", "on", "dialog", "getName", "checkInterval", "setInterval", "urlField", "getContentElement", "getValue", "startsWith", "clearInterval", "selectPage", "e", "rawContent", "getData", "contentWithRelativeUrls", "convertUrlsToRelative", "preserveRichTextFormatting", "_this15", "textarea", "className", "placeholder", "value", "style", "cssText", "addEventListener", "target", "content", "func", "wait", "timeout", "debounced", "executedFunction", "_this16", "_len", "arguments", "args", "Array", "_key", "later", "clearTimeout", "apply", "<PERSON><PERSON><PERSON><PERSON>", "location", "origin", "urlRegex", "RegExp", "parseResult", "parseQuestionContent", "textContent", "lines", "split", "line", "currentQuestionLines", "questionNumber", "i", "isQuestionStart", "isQuestionStartLine", "isQuestionTypeStart", "questionText", "parsedQuestion", "parseQuestionFromLines", "test", "contentStartIndex", "typeMatch", "match", "typeText", "remainingContent", "isOptionLine", "isAnswerLine", "isExplanationLine", "isDifficultyLine", "cleanLine", "finalQ<PERSON>ionContent", "removeQuestionNumber", "typeName", "getTypeDisplayName", "explanation", "options", "<PERSON><PERSON><PERSON><PERSON>", "optionResult", "parseOptionsFromLines", "parseQuestionMetaFromLines", "removeQuestionType", "optionKey", "toUpperCase", "optionContent", "excludePatterns", "isDescriptiveText", "some", "pattern", "processImagePaths", "processedContent", "before", "src", "after", "fullSrc", "_this17", "images", "imageIndex", "contentWithPlaceholders", "finalContent", "img", "startIndex", "isArray", "optionMatch", "label", "multipleOptionsMatch", "singleOptions", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "singleOption", "err", "answerMatch", "parseAnswerValue", "explanationMatch", "difficultyMatch", "extractAnswerFromQuestionContent", "patterns", "_i3", "_patterns", "matches", "lastMatch", "answer", "answerText", "trimmedAnswer", "getFormattedQuestionContent", "htmlContent", "extractQuestionFromHtml", "getQuestionTypeName", "plainContent", "plainText", "paragraphs", "_iterator2", "_step2", "paragraph", "paragraphText", "cleanParagraphText", "handleSearch", "resetSearch"], "sources": ["src/views/biz/questionBank/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"handleBatchImportClick\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n                :loading=\"importingQuestions\"\n              >\n                <i class=\"el-icon-upload2\"></i>\n                {{ importingQuestions ? '正在导入...' : '导入题目' }}\n              </el-button>\n\n              <div class=\"import-options\">\n                <el-checkbox\n                  v-model=\"importOptions.reverse\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后将按题目顺序倒序导入，即最后一题先导入\" placement=\"top\">\n                    <span>按题目顺序倒序导入</span>\n                  </el-tooltip>\n                </el-checkbox>\n\n                <el-checkbox\n                  v-model=\"importOptions.allowDuplicate\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后允许导入重复的题目内容，否则会跳过重复题目\" placement=\"top\">\n                    <span>允许题目重复</span>\n                  </el-tooltip>\n                </el-checkbox>\n              </div>\n\n              <div v-if=\"importingQuestions\" class=\"import-progress\">\n                <el-progress\n                  :percentage=\"importProgress\"\n                  :show-text=\"true\"\n                  :format=\"formatProgress\"\n                  status=\"success\"\n                  :stroke-width=\"6\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <!-- <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button> -->\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载Word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1. 建议使用新版Office或WPS软件编辑题目文件，仅支持上传.docx格式的文件<br>\n          2. 题目数量过多、题目文件过大等情况建议分批导入<br>\n          3. 需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport { listQuestion, delQuestion, getQuestionStatistics, batchImportQuestions, exportQuestionsToWord } from '@/api/biz/question'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      expandedQuestions: [],\n      // 选择状态\n      selectedQuestions: [],\n      isAllSelected: false,\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '',\n      parsedQuestions: [],\n      parseErrors: [],\n      allExpanded: true,\n      isSettingFromBackend: false,\n      lastParsedContent: '', // 记录上次解析的内容，避免重复解析\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importingQuestions: false,\n      importProgress: 0,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听路由变化，当路由参数改变时重新初始化页面\n    '$route'(to, from) {\n      // 只有当路由参数中的bankId发生变化时才重新初始化\n      if (to.query.bankId !== from.query.bankId) {\n        this.initPage()\n      }\n    },\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n        // 如果已经有解析结果且内容没有实质性变化，不重新解析\n        if (this.parsedQuestions.length > 0 && this.lastParsedContent &&\n            this.stripHtmlTagsKeepImages(newVal) === this.stripHtmlTagsKeepImages(this.lastParsedContent)) {\n          return\n        }\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时清空所有内容并初始化编辑器\n          this.clearImportContent()\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数 - 增加延时到2秒，减少卡顿\n    this.debounceParseDocument = this.debounce(this.parseDocument, 2000)\n    // 创建编辑器内容变化的防抖函数 - 延时1.5秒\n    this.debounceEditorContentChange = this.debounce(this.handleEditorContentChangeDebounced, 1500)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n    // 取消所有防抖函数\n    if (this.debounceParseDocument && this.debounceParseDocument.cancel) {\n      this.debounceParseDocument.cancel()\n    }\n    if (this.debounceEditorContentChange && this.debounceEditorContentChange.cancel) {\n      this.debounceEditorContentChange.cancel()\n    }\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n\n      // 重置查询参数，确保分页从第一页开始\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: bankId,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      }\n\n      // 重置其他状态\n      this.questionList = []\n      this.total = 0\n      this.expandedQuestions = []\n      this.selectedQuestions = []\n      this.expandAll = false\n\n      // 重新获取数据\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      // 直接跳转到题库管理页面，而不是使用浏览器的后退功能\n      this.$router.push('/biz/questionBank')\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(() => {\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(() => {\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n\n\n    // 处理批量导题按钮点击\n    handleBatchImportClick() {\n      this.importDrawerVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      // 确认导出\n      this.$confirm(`确认导出题库\"${this.bankName}\"中的所有题目吗？`, '导出确认', {\n        confirmButtonText: '确定导出',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        const loading = this.$loading({\n          lock: true,\n          text: `正在导出题库中的所有题目...`,\n          spinner: 'el-icon-loading',\n          background: 'rgba(0, 0, 0, 0.7)'\n        })\n\n        // 调用导出API - 导出当前题库的所有题目\n        exportQuestionsToWord({\n          bankId: this.bankId,\n          bankName: this.bankName\n        }).then(response => {\n          loading.close()\n\n          // 创建下载链接\n          const blob = new Blob([response], {\n            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n          })\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n          link.download = `${this.bankName}.docx`\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          this.$message.success(`成功导出题库\"${this.bankName}\"`)\n        }).catch(error => {\n          loading.close()\n          console.error('导出失败:', error)\n          this.$message.error('导出失败，请重试')\n        })\n      }).catch(() => {\n        // 用户取消导出\n      })\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除（优化版本）\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      const deleteCount = this.selectedQuestions.length\n      let confirmMessage = `确认删除选中的 ${deleteCount} 道题目吗？`\n\n      if (deleteCount > 20) {\n        confirmMessage += '\\n\\n注意：题目较多，删除可能需要一些时间，请耐心等待。'\n      }\n\n      this.$confirm(confirmMessage, '批量删除', {\n        confirmButtonText: '确定删除',\n        cancelButtonText: '取消',\n        type: 'warning',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.performBatchDelete()\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 执行批量删除\n    async performBatchDelete() {\n      const deleteCount = this.selectedQuestions.length\n      const loading = this.$loading({\n        lock: true,\n        text: `正在删除 ${deleteCount} 道题目，请稍候...`,\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      })\n\n      try {\n        // 使用真正的批量删除API\n        const questionIds = this.selectedQuestions.join(',')\n        const startTime = Date.now()\n\n        await delQuestion(questionIds) // 调用批量删除API\n\n        const endTime = Date.now()\n        const duration = ((endTime - startTime) / 1000).toFixed(1)\n\n        loading.close()\n        this.$message.success(`成功删除 ${deleteCount} 道题目 (耗时 ${duration}s)`)\n\n        // 清理选择状态\n        this.selectedQuestions = []\n        this.isAllSelected = false\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        loading.close()\n        console.error('批量删除失败:', error)\n\n        let errorMessage = '批量删除失败'\n        if (error.response && error.response.data && error.response.data.msg) {\n          errorMessage = error.response.data.msg\n        } else if (error.message) {\n          errorMessage = error.message\n        }\n\n        this.$message.error(errorMessage)\n      }\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        // 收起题目\n        this.expandedQuestions.splice(index, 1)\n        // 如果当前是\"展开所有\"状态，则取消\"展开所有\"状态\n        if (this.expandAll) {\n          this.expandAll = false\n          // 将其他题目添加到expandedQuestions数组中，除了当前要收起的题目\n          this.questionList.forEach(question => {\n            if (question.questionId !== questionId && !this.expandedQuestions.includes(question.questionId)) {\n              this.expandedQuestions.push(question.questionId)\n            }\n          })\n        }\n      } else {\n        // 展开题目\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(() => {\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      // 检查是否有未保存的内容\n      const hasContent = this.documentContent && this.documentContent.trim().length > 0\n      const hasParsedQuestions = this.parsedQuestions && this.parsedQuestions.length > 0\n\n      if (hasContent || hasParsedQuestions) {\n        let message = '关闭后将丢失当前编辑的内容，确认关闭吗？'\n        if (hasParsedQuestions) {\n          message = `当前已解析出 ${this.parsedQuestions.length} 道题目，关闭后将丢失所有内容，确认关闭吗？`\n        }\n\n        this.$confirm(message, '确认关闭', {\n          confirmButtonText: '确定关闭',\n          cancelButtonText: '继续编辑',\n          type: 'warning'\n        }).then(() => {\n          // 清空内容\n          this.clearImportContent()\n          done()\n        }).catch(() => {\n          // 取消关闭，继续编辑\n        })\n      } else {\n        // 没有内容直接关闭\n        done()\n      }\n    },\n\n    // 清空导入内容\n    clearImportContent() {\n      // 清空文档内容\n      this.documentContent = ''\n      this.documentHtmlContent = ''\n\n      // 清空解析结果\n      this.parsedQuestions = []\n      this.parseErrors = []\n\n      // 重置解析状态\n      this.allExpanded = true\n      this.isSettingFromBackend = false\n      this.lastParsedContent = '' // 清空上次解析的内容记录\n\n      // 重置上传状态\n      this.isUploading = false\n      this.isParsing = false\n      this.importingQuestions = false\n      this.importProgress = 0\n\n      // 重置导入选项\n      this.importOptions = {\n        reverse: false,\n        allowDuplicate: false\n      }\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response) {\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n          this.lastParsedContent = response.originalContent // 记录已解析的内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 5000) // 延长到5秒，确保编辑器内容稳定\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError() {\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      // 构建确认信息\n      let confirmMessage = `确认导入 ${this.parsedQuestions.length} 道题目吗？`\n      let optionMessages = []\n\n      if (this.importOptions.reverse) {\n        optionMessages.push('将按倒序导入')\n      }\n      if (this.importOptions.allowDuplicate) {\n        optionMessages.push('允许重复题目')\n      }\n\n      if (optionMessages.length > 0) {\n        confirmMessage += `\\n\\n导入选项：${optionMessages.join('，')}`\n      }\n\n      this.$confirm(confirmMessage, '确认导入', {\n        confirmButtonText: '确定导入',\n        cancelButtonText: '取消',\n        type: 'info',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      this.importingQuestions = true\n      this.importProgress = 0\n\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n          this.$message.info('已按倒序排列题目')\n        }\n\n        // 模拟进度更新\n        this.importProgress = 10\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate,\n          reverse: this.importOptions.reverse\n        }\n\n        this.importProgress = 30\n\n        const response = await batchImportQuestions(importData)\n\n        this.importProgress = 80\n\n        if (response.code === 200) {\n          this.importProgress = 100\n\n          // 显示详细的导入结果\n          const result = response.data || {}\n          const successCount = result.successCount || 0\n          const failCount = result.failCount || 0\n          const skippedCount = result.skippedCount || 0\n\n          // 构建结果消息\n          let resultMessage = `导入完成：成功 ${successCount} 道`\n\n          if (failCount > 0) {\n            resultMessage += `，失败 ${failCount} 道`\n          }\n\n          if (skippedCount > 0) {\n            resultMessage += `，跳过重复 ${skippedCount} 道`\n          }\n\n          resultMessage += ' 题目'\n\n          // 根据结果类型显示不同的消息\n          if (failCount > 0 || skippedCount > 0) {\n            this.$message.warning(resultMessage)\n          } else {\n            this.$message.success(resultMessage)\n          }\n\n          // 如果有错误信息，显示详情\n          if (result.errors && result.errors.length > 0) {\n            console.warn('导入详情:', result.errors)\n\n            // 如果有跳过的题目，可以显示更详细的信息\n            if (skippedCount > 0) {\n              const skippedErrors = result.errors.filter(error => error.includes('重复跳过'))\n              if (skippedErrors.length > 0) {\n                console.info('跳过的重复题目:', skippedErrors)\n              }\n            }\n          }\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n\n        // 清理状态并关闭抽屉\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        console.error('导入题目失败:', error)\n        this.$message.error('导入失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.importingQuestions = false\n        this.importProgress = 0\n      }\n    },\n\n    // 格式化进度显示\n    formatProgress(percentage) {\n      if (percentage === 100) {\n        return '导入完成'\n      } else if (percentage >= 80) {\n        return '正在保存...'\n      } else if (percentage >= 30) {\n        return '正在处理...'\n      } else {\n        return '准备中...'\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              on: {\n                instanceReady: function(evt) {\n                  const editor = evt.editor\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                }\n              }\n            })\n          } catch (error) {\n            this.fallbackToTextarea()\n            return\n          }\n\n          // 监听内容变化 - 使用防抖优化性能\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('key', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('instanceReady', () => {\n              this.editorInitialized = true\n              this.richEditor.setData('')\n            })\n          }\n        })\n\n      } catch (error) {\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 处理编辑器内容变化（防抖后执行）\n    handleEditorContentChangeDebounced() {\n      if (!this.richEditor || !this.editorInitialized) {\n        return\n      }\n\n      try {\n        const rawContent = this.richEditor.getData()\n        const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n        this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n        this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n      } catch (error) {\n        console.warn('编辑器内容处理失败:', error)\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = '' // 确保文本框为空\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化 - 使用防抖优化性能\n        textarea.addEventListener('input', (e) => {\n          // 立即更新内容，但防抖解析\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        this.documentContent = content\n        this.documentHtmlContent = content\n      }\n    },\n\n\n\n    // 防抖函数 - 优化版本，支持取消\n    debounce(func, wait) {\n      let timeout\n      const debounced = function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          timeout = null\n          func.apply(this, args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n\n      // 添加取消方法\n      debounced.cancel = function() {\n        clearTimeout(timeout)\n        timeout = null\n      }\n\n      return debounced\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n        // 记录解析成功的内容，避免重复解析\n        this.lastParsedContent = this.documentContent\n      } catch (error) {\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n        if (lines.length === 0) {\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号和题型标识\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.questionContent = this.removeQuestionType(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      // 严格验证：避免误将题目内容中的字母+符号识别为选项\n      if (!line || line.length > 200) {\n        return false\n      }\n\n      const match = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n      if (match) {\n        const optionKey = match[1].toUpperCase()\n        const optionContent = match[2] ? match[2].trim() : ''\n\n        // 严格验证条件：\n        // 1. 选项字母必须是A-Z单个字母\n        // 2. 选项内容长度合理（1-100字符）\n        // 3. 排除明显的题目内容描述（如包含\"表示\"、\"数据\"等词汇的长句）\n        if (/^[A-Z]$/.test(optionKey) && optionContent.length > 0 && optionContent.length <= 100) {\n          // 排除明显的题目内容描述\n          const excludePatterns = [\n            /表示.*?数据/,     // 排除\"表示...数据\"这类描述\n            /一般用.*?或/,      // 排除\"一般用...或\"这类描述\n            /通常.*?来/,       // 排除\"通常...来\"这类描述\n            /可以.*?进行/,     // 排除\"可以...进行\"这类描述\n            /.*?坐标.*?表示/   // 排除\"坐标...表示\"这类描述\n          ]\n\n          const isDescriptiveText = excludePatterns.some(pattern => pattern.test(optionContent))\n          return !isDescriptiveText\n        }\n      }\n      return false\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          return `<img${before}src=\"${fullSrc}\"${after}>`\n        })\n\n        return processedContent\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')\n          .replace(/<\\/p>/gi, '\\n')\n          .replace(/<p[^>]*>/gi, '\\n')\n          .replace(/<[^>]*>/g, '')\n          .replace(/\\n\\s*\\n/g, '\\n')\n          // 处理HTML实体字符\n          .replace(/&nbsp;/g, ' ')      // 非断行空格\n          .replace(/&amp;/g, '&')       // &符号\n          .replace(/&lt;/g, '<')        // 小于号\n          .replace(/&gt;/g, '>')        // 大于号\n          .replace(/&quot;/g, '\"')      // 双引号\n          .replace(/&#39;/g, \"'\")       // 单引号\n          .replace(/&hellip;/g, '...')  // 省略号\n          .replace(/&mdash;/g, '—')     // 长破折号\n          .replace(/&ndash;/g, '–')     // 短破折号\n          .replace(/&ldquo;/g, '\"')     // 左双引号\n          .replace(/&rdquo;/g, '\"')     // 右双引号\n          .replace(/&lsquo;/g, \"'\")     // 左单引号\n          .replace(/&rsquo;/g, \"'\")     // 右单引号\n          .replace(/\\s+/g, ' ')         // 多个空白字符替换为单个空格\n\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 使用严格的选项行验证逻辑\n          if (this.isOptionLine(line)) {\n            const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n            if (optionMatch) {\n              const optionKey = optionMatch[1].toUpperCase()\n              const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n              if (optionKey && optionContent) {\n                options.push({\n                  optionKey: optionKey,\n                  label: optionKey,\n                  optionContent: optionContent,\n                  content: optionContent\n                })\n              }\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            // 但是要避免误将题目内容中的字母+符号识别为选项\n            // 只有当行长度较短且不包含描述性文字时才尝试解析多选项\n            if (line.length < 50 && !/表示|数据|一般|通常|可以/.test(line)) {\n              const multipleOptionsMatch = line.match(/([A-Z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Z][.:：．、]\\s*[^\\s]+)*)/g)\n              if (multipleOptionsMatch) {\n                // 处理同一行多个选项的情况\n                const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n                for (const singleOption of singleOptions) {\n                  if (!singleOption) continue\n\n                  // 使用严格的选项验证逻辑\n                  if (this.isOptionLine(singleOption)) {\n                    const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                    if (match) {\n                      const optionKey = match[1].toUpperCase()\n                      const optionContent = match[2] ? match[2].trim() : ''\n\n                      if (optionKey && optionContent) {\n                        options.push({\n                          optionKey: optionKey,\n                          label: optionKey,\n                          optionContent: optionContent,\n                          content: optionContent\n                        })\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        // 忽略错误\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n          // 忽略错误\n        }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n          return answerText || ''\n        }\n    },\n\n\n\n\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      // 清理题型标识：移除题目内容开头的[题型]标识\n      content = this.removeQuestionType(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 清理题目内容中的题型标识\n    removeQuestionType(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，清理标签内的题型标识\n        return content.replace(/<p[^>]*>(\\s*\\[.*?题\\]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\[.*?题\\]\\s*)/, '') // 清理开头的题型标识\n                     .replace(/>\\s*\\[.*?题\\]\\s*/g, '>') // 清理标签后的题型标识\n      } else {\n        // 对于纯文本内容，清理开头的题型标识\n        return content.replace(/^\\s*\\[.*?题\\]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        return plainContent\n      } catch (error) {\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 导入选项样式 */\n.import-options {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  margin-top: 15px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.import-options .el-checkbox {\n  margin-right: 0;\n  margin-bottom: 0;\n}\n\n.import-options .el-checkbox__label {\n  font-size: 14px;\n  color: #333;\n  font-weight: 500;\n}\n\n.import-options .el-tooltip {\n  cursor: help;\n}\n\n.import-progress {\n  margin-top: 20px;\n  padding: 15px;\n  background: #fff;\n  border-radius: 6px;\n  border: 1px solid #e1f5fe;\n}\n\n.import-progress .el-progress {\n  margin-bottom: 0;\n}\n\n.import-progress .el-progress__text {\n  font-size: 14px !important;\n  font-weight: 500;\n  color: #409eff;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuhBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,YAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,MAAA;MACAC,QAAA;MACA;MACAC,UAAA;QACAC,KAAA;QACAC,YAAA;QACAC,cAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAJ,KAAA;MACAK,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACAC,iBAAA;MACA;MACAC,iBAAA;MACAC,aAAA;MACA;MACAC,mBAAA;MACAC,mBAAA;MACAC,mBAAA;MACA;MACAC,mBAAA;MACA;MACAC,eAAA;MACAC,mBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,iBAAA;MAAA;MACAC,2BAAA;MACAC,kBAAA;MACAC,aAAA;MACA;MACAC,WAAA;MACAC,SAAA;MACAC,kBAAA;MACAC,cAAA;MACAC,aAAA;QACAC,OAAA;QACAC,cAAA;MACA;MACA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA;QACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;MACA;MACAC,UAAA;MACA;MACAC,UAAA;MACAC,iBAAA;IACA;EACA;EAEAC,KAAA;IACA;IACA,mBAAAC,OAAAC,EAAA,EAAAC,IAAA;MACA;MACA,IAAAD,EAAA,CAAAE,KAAA,CAAAvD,MAAA,KAAAsD,IAAA,CAAAC,KAAA,CAAAvD,MAAA;QACA,KAAAwD,QAAA;MACA;IACA;IACA;IACAlC,eAAA;MACAmC,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,SAAA/B,oBAAA;UACA;QACA;;QAEA;QACA,SAAAH,eAAA,CAAAmC,MAAA,aAAA/B,iBAAA,IACA,KAAAgC,uBAAA,CAAAF,MAAA,WAAAE,uBAAA,MAAAhC,iBAAA;UACA;QACA;QAEA,IAAA8B,MAAA,IAAAA,MAAA,CAAAG,IAAA;UACA,KAAAC,qBAAA;QACA;UACA,KAAAtC,eAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAsC,SAAA;IACA;IACA;IACA1C,mBAAA;MACAoC,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAM,KAAA;QACA,IAAAN,MAAA;UACA;UACA,KAAAO,kBAAA;UACA,KAAAC,SAAA;YACAF,KAAA,CAAAG,cAAA;UACA;QACA;UACA;UACA,SAAAlB,UAAA;YACA,KAAAA,UAAA,CAAAmB,OAAA;YACA,KAAAnB,UAAA;YACA,KAAAC,iBAAA;UACA;QACA;MACA;MACAa,SAAA;IACA;EACA;EAEAM,OAAA,WAAAA,QAAA;IACA,KAAAb,QAAA;IACA;IACA,KAAAM,qBAAA,QAAAQ,QAAA,MAAAC,aAAA;IACA;IACA,KAAAC,2BAAA,QAAAF,QAAA,MAAAG,kCAAA;IACA;IACA,KAAAzB,UAAA;MACAhD,MAAA,OAAAA;IACA;IACA,KAAA2C,aAAA;MACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA;EACA;EAEA2B,OAAA,WAAAA,QAAA;IACA;EAAA,CAEA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAAb,qBAAA,SAAAA,qBAAA,CAAAc,MAAA;MACA,KAAAd,qBAAA,CAAAc,MAAA;IACA;IACA,SAAAJ,2BAAA,SAAAA,2BAAA,CAAAI,MAAA;MACA,KAAAJ,2BAAA,CAAAI,MAAA;IACA;;IAEA;IACA,SAAA3B,UAAA;MACA,KAAAA,UAAA,CAAAmB,OAAA;MACA,KAAAnB,UAAA;IACA;EACA;EACA4B,OAAA;IACA;IACArB,QAAA,WAAAA,SAAA;MACA,IAAAsB,kBAAA,QAAA1B,MAAA,CAAAG,KAAA;QAAAvD,MAAA,GAAA8E,kBAAA,CAAA9E,MAAA;QAAAC,QAAA,GAAA6E,kBAAA,CAAA7E,QAAA;MACA,KAAAD,MAAA;QACA,KAAA+E,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MACA,KAAAjF,MAAA,GAAAA,MAAA;MACA,KAAAC,QAAA,GAAAA,QAAA;;MAEA;MACA,KAAAO,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA,EAAAA,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;;MAEA;MACA,KAAAN,YAAA;MACA,KAAAJ,KAAA;MACA,KAAAY,iBAAA;MACA,KAAAC,iBAAA;MACA,KAAAF,SAAA;;MAEA;MACA,KAAAoE,eAAA;MACA,KAAAC,aAAA;IACA;IACA;IACAF,MAAA,WAAAA,OAAA;MACA;MACA,KAAAG,OAAA,CAAAC,IAAA;IACA;IACA;IACAH,eAAA,WAAAA,gBAAA;MAAA,IAAAI,MAAA;MACA;MACA,IAAAC,MAAA,QAAAC,kBAAA,MAAAhF,WAAA;MACA,IAAAiF,sBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,MAAA,CAAA/E,YAAA,GAAAoF,QAAA,CAAAC,IAAA;QACAN,MAAA,CAAAnF,KAAA,GAAAwF,QAAA,CAAAxF,KAAA;MACA,GAAA0F,KAAA;QACAP,MAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAAD,MAAA;MACA,IAAAO,eAAA,OAAAC,cAAA,CAAAC,OAAA,MAAAT,MAAA;;MAEA;MACA,IAAAO,eAAA,CAAAnF,YAAA;QACA,IAAAsF,OAAA;UACA;UACA;UACA;QACA;QACAH,eAAA,CAAAnF,YAAA,GAAAsF,OAAA,CAAAH,eAAA,CAAAnF,YAAA,KAAAmF,eAAA,CAAAnF,YAAA;MACA;;MAEA;MACA,IAAAmF,eAAA,CAAAlF,UAAA;QACA,IAAAsF,aAAA;UACA;UACA;UACA;QACA;QACAJ,eAAA,CAAAlF,UAAA,GAAAsF,aAAA,CAAAJ,eAAA,CAAAlF,UAAA,KAAAkF,eAAA,CAAAlF,UAAA;MACA;;MAEA;MACAuF,MAAA,CAAAC,IAAA,CAAAN,eAAA,EAAAO,OAAA,WAAAC,GAAA;QACA,IAAAR,eAAA,CAAAQ,GAAA,YAAAR,eAAA,CAAAQ,GAAA,cAAAR,eAAA,CAAAQ,GAAA,MAAAC,SAAA;UACA,OAAAT,eAAA,CAAAQ,GAAA;QACA;MACA;MAEA,OAAAR,eAAA;IACA;IACA;IACAX,aAAA,WAAAA,cAAA;MAAA,IAAAqB,MAAA;MACA,IAAAC,+BAAA,OAAAzG,MAAA,EAAA0F,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAAtG,UAAA,GAAAyF,QAAA,CAAA5F,IAAA;MACA,GAAA8F,KAAA;QACA;QACAW,MAAA,CAAAtG,UAAA;UACAC,KAAA;UACAC,YAAA;UACAC,cAAA;UACAC,QAAA;QACA;MACA;IACA;IAGA;IACAoG,sBAAA,WAAAA,uBAAA;MACA,KAAArF,mBAAA;IACA;IACA;IACAsF,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAzF,mBAAA,GAAAyF,IAAA;MACA,KAAAxF,mBAAA;MACA,KAAAF,mBAAA;IACA;IACA;IACA2F,eAAA,WAAAA,gBAAA;MACA,KAAA/F,SAAA,SAAAA,SAAA;MACA,UAAAA,SAAA;QACA,KAAAC,iBAAA;MACA;IACA;IAIA;IACA+F,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAC,QAAA,0CAAAC,MAAA,MAAAhH,QAAA;QACAiH,iBAAA;QACAC,gBAAA;QACAP,IAAA;MACA,GAAAlB,IAAA;QACA,IAAA0B,OAAA,GAAAL,MAAA,CAAAM,QAAA;UACAC,IAAA;UACAC,IAAA;UACAC,OAAA;UACAC,UAAA;QACA;;QAEA;QACA,IAAAC,+BAAA;UACA1H,MAAA,EAAA+G,MAAA,CAAA/G,MAAA;UACAC,QAAA,EAAA8G,MAAA,CAAA9G;QACA,GAAAyF,IAAA,WAAAC,QAAA;UACAyB,OAAA,CAAAO,KAAA;;UAEA;UACA,IAAAC,IAAA,OAAAC,IAAA,EAAAlC,QAAA;YACAiB,IAAA;UACA;UACA,IAAAkB,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;UACA,IAAAM,IAAA,GAAAC,QAAA,CAAAC,aAAA;UACAF,IAAA,CAAAG,IAAA,GAAAP,GAAA;UACAI,IAAA,CAAAI,QAAA,MAAArB,MAAA,CAAAF,MAAA,CAAA9G,QAAA;UACAkI,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,IAAA;UACAA,IAAA,CAAAO,KAAA;UACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,IAAA;UACAH,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAb,GAAA;UAEAf,MAAA,CAAAhC,QAAA,CAAA6D,OAAA,0CAAA3B,MAAA,CAAAF,MAAA,CAAA9G,QAAA;QACA,GAAA4F,KAAA,WAAAb,KAAA;UACAoC,OAAA,CAAAO,KAAA;UACAkB,OAAA,CAAA7D,KAAA,UAAAA,KAAA;UACA+B,MAAA,CAAAhC,QAAA,CAAAC,KAAA;QACA;MACA,GAAAa,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAiD,qBAAA,WAAAA,sBAAA;MACA,KAAA7H,aAAA,SAAAA,aAAA;MACA,SAAAA,aAAA;QACA;QACA,KAAAD,iBAAA,QAAAT,YAAA,CAAAwI,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,UAAA;QAAA;QACA,KAAAlE,QAAA,CAAA6D,OAAA,uBAAA3B,MAAA,MAAAjG,iBAAA,CAAA2C,MAAA;MACA;QACA;QACA,KAAA3C,iBAAA;QACA,KAAA+D,QAAA,CAAA6D,OAAA;MACA;IACA;IAIA;IACAM,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAAnI,iBAAA,CAAA2C,MAAA;QACA,KAAAoB,QAAA,CAAAqE,OAAA;QACA;MACA;MAEA,IAAAC,WAAA,QAAArI,iBAAA,CAAA2C,MAAA;MACA,IAAA2F,cAAA,iDAAArC,MAAA,CAAAoC,WAAA;MAEA,IAAAA,WAAA;QACAC,cAAA;MACA;MAEA,KAAAtC,QAAA,CAAAsC,cAAA;QACApC,iBAAA;QACAC,gBAAA;QACAP,IAAA;QACA2C,wBAAA;MACA,GAAA7D,IAAA;QACAyD,MAAA,CAAAK,kBAAA;MACA,GAAA3D,KAAA;QACAsD,MAAA,CAAApE,QAAA,CAAA0E,IAAA;MACA;IACA;IAEA;IACAD,kBAAA,WAAAA,mBAAA;MAAA,IAAAE,MAAA;MAAA,WAAAC,kBAAA,CAAA3D,OAAA,mBAAA4D,aAAA,CAAA5D,OAAA,IAAA6D,CAAA,UAAAC,QAAA;QAAA,IAAAT,WAAA,EAAAjC,OAAA,EAAA2C,WAAA,EAAAC,SAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,EAAA;QAAA,WAAAR,aAAA,CAAA5D,OAAA,IAAAqE,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAlB,WAAA,GAAAK,MAAA,CAAA1I,iBAAA,CAAA2C,MAAA;cACAyD,OAAA,GAAAsC,MAAA,CAAArC,QAAA;gBACAC,IAAA;gBACAC,IAAA,8BAAAN,MAAA,CAAAoC,WAAA;gBACA7B,OAAA;gBACAC,UAAA;cACA;cAAA6C,QAAA,CAAAE,CAAA;cAGA;cACAT,WAAA,GAAAL,MAAA,CAAA1I,iBAAA,CAAAyJ,IAAA;cACAT,SAAA,GAAAU,IAAA,CAAAC,GAAA;cAAAL,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAAK,qBAAA,EAAAb,WAAA;YAAA;cAAA;cAEAE,OAAA,GAAAS,IAAA,CAAAC,GAAA;cACAT,QAAA,KAAAD,OAAA,GAAAD,SAAA,UAAAa,OAAA;cAEAzD,OAAA,CAAAO,KAAA;cACA+B,MAAA,CAAA3E,QAAA,CAAA6D,OAAA,6BAAA3B,MAAA,CAAAoC,WAAA,wCAAApC,MAAA,CAAAiD,QAAA;;cAEA;cACAR,MAAA,CAAA1I,iBAAA;cACA0I,MAAA,CAAAzI,aAAA;;cAEA;cACAyI,MAAA,CAAAxE,eAAA;cACAwE,MAAA,CAAAvE,aAAA;cAAAmF,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAQ,CAAA;cAGA1D,OAAA,CAAAO,KAAA;cACAkB,OAAA,CAAA7D,KAAA,YAAAoF,EAAA;cAEAD,YAAA;cACA,IAAAC,EAAA,CAAAzE,QAAA,IAAAyE,EAAA,CAAAzE,QAAA,CAAA5F,IAAA,IAAAqK,EAAA,CAAAzE,QAAA,CAAA5F,IAAA,CAAAgL,GAAA;gBACAZ,YAAA,GAAAC,EAAA,CAAAzE,QAAA,CAAA5F,IAAA,CAAAgL,GAAA;cACA,WAAAX,EAAA,CAAAY,OAAA;gBACAb,YAAA,GAAAC,EAAA,CAAAY,OAAA;cACA;cAEAtB,MAAA,CAAA3E,QAAA,CAAAC,KAAA,CAAAmF,YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAW,CAAA;UAAA;QAAA,GAAAnB,OAAA;MAAA;IAEA;IAEA;IACAoB,oBAAA,WAAAA,qBAAAjC,UAAA,EAAAkC,QAAA;MACA,IAAAA,QAAA;QACA,UAAAnK,iBAAA,CAAAoK,QAAA,CAAAnC,UAAA;UACA,KAAAjI,iBAAA,CAAAqE,IAAA,CAAA4D,UAAA;QACA;MACA;QACA,IAAAoC,KAAA,QAAArK,iBAAA,CAAAsK,OAAA,CAAArC,UAAA;QACA,IAAAoC,KAAA;UACA,KAAArK,iBAAA,CAAAuK,MAAA,CAAAF,KAAA;QACA;MACA;;MAEA;MACA,KAAApK,aAAA,QAAAD,iBAAA,CAAA2C,MAAA,UAAApD,YAAA,CAAAoD,MAAA;IACA;IACA;IACA6H,kBAAA,WAAAA,mBAAAvC,UAAA;MAAA,IAAAwC,MAAA;MACA,IAAAJ,KAAA,QAAAtK,iBAAA,CAAAuK,OAAA,CAAArC,UAAA;MACA,IAAAoC,KAAA;QACA;QACA,KAAAtK,iBAAA,CAAAwK,MAAA,CAAAF,KAAA;QACA;QACA,SAAAvK,SAAA;UACA,KAAAA,SAAA;UACA;UACA,KAAAP,YAAA,CAAA8F,OAAA,WAAAqF,QAAA;YACA,IAAAA,QAAA,CAAAzC,UAAA,KAAAA,UAAA,KAAAwC,MAAA,CAAA1K,iBAAA,CAAAqK,QAAA,CAAAM,QAAA,CAAAzC,UAAA;cACAwC,MAAA,CAAA1K,iBAAA,CAAAsE,IAAA,CAAAqG,QAAA,CAAAzC,UAAA;YACA;UACA;QACA;MACA;QACA;QACA,KAAAlI,iBAAA,CAAAsE,IAAA,CAAA4D,UAAA;MACA;IACA;IACA;IACA0C,kBAAA,WAAAA,mBAAAD,QAAA;MACA,KAAAtK,mBAAA,GAAAsK,QAAA;MACA,KAAAvK,mBAAA,GAAAuK,QAAA,CAAA/K,YAAA;MACA,KAAAO,mBAAA;IACA;IACA;IACA0K,kBAAA,WAAAA,mBAAAF,QAAA;MACA;MACA,IAAAG,cAAA,OAAA9F,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA0F,QAAA;QACAzC,UAAA;QAAA;QACA6C,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;MAAA,EACA;;MAEA;MACA,KAAA7K,mBAAA,GAAAyK,cAAA;MACA,KAAA1K,mBAAA,QAAA+K,2BAAA,CAAAR,QAAA,CAAA/K,YAAA;MACA,KAAAO,mBAAA;IACA;IAEA;IACAgL,2BAAA,WAAAA,4BAAAtF,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA,KAAAA,IAAA;IACA;IACA;IACAuF,oBAAA,WAAAA,qBAAAT,QAAA;MAAA,IAAAU,MAAA;MACA,IAAAvL,eAAA,GAAA6K,QAAA,CAAA7K,eAAA,CAAAwL,OAAA;MACA,IAAAC,cAAA,GAAAzL,eAAA,CAAA8C,MAAA,QAAA9C,eAAA,CAAA0L,SAAA,kBAAA1L,eAAA;MACA,KAAAmG,QAAA,0CAAAC,MAAA,CAAAqF,cAAA;QACApF,iBAAA;QACAC,gBAAA;QACAP,IAAA;MACA,GAAAlB,IAAA;QACA,IAAAkF,qBAAA,EAAAc,QAAA,CAAAzC,UAAA,EAAAvD,IAAA;UACA0G,MAAA,CAAArH,QAAA,CAAA6D,OAAA;UACAwD,MAAA,CAAAlH,eAAA;UACAkH,MAAA,CAAAjH,aAAA;QACA,GAAAU,KAAA;UACAuG,MAAA,CAAArH,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA;IACAwH,yBAAA,WAAAA,0BAAA;MACA,KAAAtL,mBAAA;MACA,KAAAgE,eAAA;MACA,KAAAC,aAAA;IACA;IAIA;IACAsH,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,UAAA,QAAAtL,eAAA,SAAAA,eAAA,CAAAuC,IAAA,GAAAF,MAAA;MACA,IAAAkJ,kBAAA,QAAArL,eAAA,SAAAA,eAAA,CAAAmC,MAAA;MAEA,IAAAiJ,UAAA,IAAAC,kBAAA;QACA,IAAA7B,OAAA;QACA,IAAA6B,kBAAA;UACA7B,OAAA,2CAAA/D,MAAA,MAAAzF,eAAA,CAAAmC,MAAA;QACA;QAEA,KAAAqD,QAAA,CAAAgE,OAAA;UACA9D,iBAAA;UACAC,gBAAA;UACAP,IAAA;QACA,GAAAlB,IAAA;UACA;UACAiH,MAAA,CAAA1I,kBAAA;UACAyI,IAAA;QACA,GAAA7G,KAAA;UACA;QAAA,CACA;MACA;QACA;QACA6G,IAAA;MACA;IACA;IAEA;IACAzI,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAA3C,eAAA;MACA,KAAAC,mBAAA;;MAEA;MACA,KAAAC,eAAA;MACA,KAAAC,WAAA;;MAEA;MACA,KAAAC,WAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,iBAAA;;MAEA;MACA,KAAAI,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,cAAA;;MAEA;MACA,KAAAC,aAAA;QACAC,OAAA;QACAC,cAAA;MACA;IACA;IAEA;IACAwK,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAA/K,WAAA;MACA,KAAAC,SAAA;;MAEA;MACA,KAAAiC,SAAA;QACA,IAAA8I,eAAA,GAAAD,MAAA,CAAAE,KAAA,CAAAC,cAAA;QACA,IAAAF,eAAA;UACAA,eAAA,CAAAG,UAAA;QACA;MACA;MAEA,KAAAtL,2BAAA;IAEA;IAEA;IACAuL,eAAA,WAAAA,gBAAA;MACA,KAAArL,aAAA;MACA,KAAAD,kBAAA;IACA;IAEA;IACAuL,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,YAAA,muDAuBA1J,IAAA;;MAEA;MACA,SAAAZ,UAAA,SAAAC,iBAAA;QACA,KAAAD,UAAA,CAAAuK,OAAA,CAAAD,YAAA;MAEA;QACA;QACA,KAAArJ,SAAA;UACA,IAAAoJ,MAAA,CAAArK,UAAA,IAAAqK,MAAA,CAAApK,iBAAA;YACAoK,MAAA,CAAArK,UAAA,CAAAuK,OAAA,CAAAD,YAAA;UAEA;QACA;MACA;;MAEA;MACA,KAAAzL,kBAAA;;MAEA;MACA,KAAAiD,QAAA,CAAA6D,OAAA;IAGA;IAIA;IACA6E,oBAAA,WAAAA,qBAAA;MACA,KAAAnF,QAAA;IACA;IAEA;IACAoF,YAAA,WAAAA,aAAAC,IAAA;MAGA,IAAAC,WAAA,GAAAD,IAAA,CAAA/G,IAAA,kFACA+G,IAAA,CAAA/G,IAAA,4EACA+G,IAAA,CAAAhO,IAAA,CAAAkO,QAAA,aAAAF,IAAA,CAAAhO,IAAA,CAAAkO,QAAA;MACA,IAAAC,OAAA,GAAAH,IAAA,CAAAI,IAAA;MAEA,KAAAH,WAAA;QACA,KAAA7I,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAA8I,OAAA;QACA,KAAA/I,QAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,KAAAhC,UAAA,CAAAhD,MAAA,QAAAA,MAAA;;MAEA;MACA,KAAAgC,WAAA;MACA,KAAAC,SAAA;MAIA;IACA;IAEA;IACA+L,mBAAA,WAAAA,oBAAArI,QAAA;MAAA,IAAAsI,OAAA;MACA,IAAAtI,QAAA,CAAAuI,IAAA;QACA;QACA,KAAAlM,WAAA;QACA,KAAAC,SAAA;;QAIA;QACA,KAAAT,eAAA;QACA,KAAAC,WAAA;;QAEA;QACA0M,UAAA;UACAF,OAAA,CAAApM,2BAAA;UACAoM,OAAA,CAAAhM,SAAA;QACA;;QAEA;QACA,KAAAN,oBAAA;;QAEA;QACA,IAAAgE,QAAA,CAAAyI,SAAA,IAAAzI,QAAA,CAAAyI,SAAA,CAAAzK,MAAA;UACA,KAAAnC,eAAA,GAAAmE,QAAA,CAAAyI,SAAA,CAAArF,GAAA,WAAA2C,QAAA;YAAA,WAAA3F,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA0F,QAAA;cACA2C,SAAA;YAAA;UAAA,CACA;UACA;UACA,KAAA3M,WAAA;UACA,KAAAD,WAAA,GAAAkE,QAAA,CAAA2I,MAAA;;UAEA;UACA,IAAAC,UAAA,GAAA5I,QAAA,CAAA2I,MAAA,GAAA3I,QAAA,CAAA2I,MAAA,CAAA3K,MAAA;UACA,IAAA4K,UAAA;YACA,KAAAxJ,QAAA,CAAA6D,OAAA,mCAAA3B,MAAA,CAAAtB,QAAA,CAAAyI,SAAA,CAAAzK,MAAA,sCAAAsD,MAAA,CAAAsH,UAAA;UACA;YACA,KAAAxJ,QAAA,CAAA6D,OAAA,mCAAA3B,MAAA,CAAAtB,QAAA,CAAAyI,SAAA,CAAAzK,MAAA;UACA;QAGA;UACA,KAAAoB,QAAA,CAAAC,KAAA;UACA,KAAAxD,eAAA;UACA,KAAAC,WAAA,GAAAkE,QAAA,CAAA2I,MAAA;QAGA;;QAEA;QACA,IAAA3I,QAAA,CAAA6I,eAAA;UACA,KAAAC,gBAAA,CAAA9I,QAAA,CAAA6I,eAAA;UACA,KAAAlN,eAAA,GAAAqE,QAAA,CAAA6I,eAAA;UACA,KAAAjN,mBAAA,GAAAoE,QAAA,CAAA6I,eAAA;UACA,KAAA5M,iBAAA,GAAA+D,QAAA,CAAA6I,eAAA;QAEA;;QAEA;QACAL,UAAA;UACAF,OAAA,CAAAtM,oBAAA;QACA;MACA;QAEA,KAAAoD,QAAA,CAAAC,KAAA,CAAAW,QAAA,CAAAoF,GAAA;QACA;QACA,KAAA/I,WAAA;QACA,KAAAC,SAAA;MACA;IACA;IAEA;IACAyM,iBAAA,WAAAA,kBAAA;MACA,KAAA3J,QAAA,CAAAC,KAAA;;MAEA;MACA,KAAAhD,WAAA;MACA,KAAAC,SAAA;IACA;IAIA;IACA0M,cAAA,WAAAA,eAAAtD,KAAA;MACA,IAAAK,QAAA,QAAAlK,eAAA,CAAA6J,KAAA;MACA,KAAAuD,IAAA,CAAAlD,QAAA,gBAAAA,QAAA,CAAA2C,SAAA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAApN,WAAA,SAAAA,WAAA;MACA,KAAAF,eAAA,CAAA6E,OAAA,WAAAqF,QAAA;QACAoD,OAAA,CAAAF,IAAA,CAAAlD,QAAA,gBAAAoD,OAAA,CAAApN,WAAA;MACA;IAEA;IAEA;IACAqN,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,SAAAxN,eAAA,CAAAmC,MAAA;QACA,KAAAoB,QAAA,CAAAqE,OAAA;QACA;MACA;;MAEA;MACA,IAAAE,cAAA,+BAAArC,MAAA,MAAAzF,eAAA,CAAAmC,MAAA;MACA,IAAAsL,cAAA;MAEA,SAAA7M,aAAA,CAAAC,OAAA;QACA4M,cAAA,CAAA5J,IAAA;MACA;MACA,SAAAjD,aAAA,CAAAE,cAAA;QACA2M,cAAA,CAAA5J,IAAA;MACA;MAEA,IAAA4J,cAAA,CAAAtL,MAAA;QACA2F,cAAA,yCAAArC,MAAA,CAAAgI,cAAA,CAAAxE,IAAA;MACA;MAEA,KAAAzD,QAAA,CAAAsC,cAAA;QACApC,iBAAA;QACAC,gBAAA;QACAP,IAAA;QACA2C,wBAAA;MACA,GAAA7D,IAAA;QACAsJ,OAAA,CAAAE,eAAA;MACA,GAAArJ,KAAA;IACA;IAEA;IACAqJ,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAxF,kBAAA,CAAA3D,OAAA,mBAAA4D,aAAA,CAAA5D,OAAA,IAAA6D,CAAA,UAAAuF,SAAA;QAAA,IAAAC,iBAAA,EAAAC,UAAA,EAAA3J,QAAA,EAAA4J,MAAA,EAAAC,YAAA,EAAAC,SAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,GAAA;QAAA,WAAAjG,aAAA,CAAA5D,OAAA,IAAAqE,CAAA,WAAAyF,SAAA;UAAA,kBAAAA,SAAA,CAAAvF,CAAA;YAAA;cACA4E,OAAA,CAAAjN,kBAAA;cACAiN,OAAA,CAAAhN,cAAA;cAAA2N,SAAA,CAAAtF,CAAA;cAGA;cACA6E,iBAAA,OAAAU,mBAAA,CAAA/J,OAAA,EAAAmJ,OAAA,CAAA3N,eAAA;cAEA,IAAA2N,OAAA,CAAA/M,aAAA,CAAAC,OAAA;gBACAgN,iBAAA,CAAAhN,OAAA;gBACA8M,OAAA,CAAApK,QAAA,CAAA0E,IAAA;cACA;;cAEA;cACA0F,OAAA,CAAAhN,cAAA;;cAEA;cACAmN,UAAA;gBACAtP,MAAA,EAAAmP,OAAA,CAAAnP,MAAA;gBACAoO,SAAA,EAAAiB,iBAAA;gBACA/M,cAAA,EAAA6M,OAAA,CAAA/M,aAAA,CAAAE,cAAA;gBACAD,OAAA,EAAA8M,OAAA,CAAA/M,aAAA,CAAAC;cACA;cAEA8M,OAAA,CAAAhN,cAAA;cAAA2N,SAAA,CAAAvF,CAAA;cAAA,OAEA,IAAAyF,8BAAA,EAAAV,UAAA;YAAA;cAAA3J,QAAA,GAAAmK,SAAA,CAAAhF,CAAA;cAEAqE,OAAA,CAAAhN,cAAA;cAAA,MAEAwD,QAAA,CAAAuI,IAAA;gBAAA4B,SAAA,CAAAvF,CAAA;gBAAA;cAAA;cACA4E,OAAA,CAAAhN,cAAA;;cAEA;cACAoN,MAAA,GAAA5J,QAAA,CAAA5F,IAAA;cACAyP,YAAA,GAAAD,MAAA,CAAAC,YAAA;cACAC,SAAA,GAAAF,MAAA,CAAAE,SAAA;cACAC,YAAA,GAAAH,MAAA,CAAAG,YAAA,OAEA;cACAC,aAAA,iDAAA1I,MAAA,CAAAuI,YAAA;cAEA,IAAAC,SAAA;gBACAE,aAAA,0BAAA1I,MAAA,CAAAwI,SAAA;cACA;cAEA,IAAAC,YAAA;gBACAC,aAAA,sCAAA1I,MAAA,CAAAyI,YAAA;cACA;cAEAC,aAAA;;cAEA;cACA,IAAAF,SAAA,QAAAC,YAAA;gBACAP,OAAA,CAAApK,QAAA,CAAAqE,OAAA,CAAAuG,aAAA;cACA;gBACAR,OAAA,CAAApK,QAAA,CAAA6D,OAAA,CAAA+G,aAAA;cACA;;cAEA;cACA,IAAAJ,MAAA,CAAAjB,MAAA,IAAAiB,MAAA,CAAAjB,MAAA,CAAA3K,MAAA;gBACAkF,OAAA,CAAAoH,IAAA,UAAAV,MAAA,CAAAjB,MAAA;;gBAEA;gBACA,IAAAoB,YAAA;kBACAE,aAAA,GAAAL,MAAA,CAAAjB,MAAA,CAAA4B,MAAA,WAAAlL,KAAA;oBAAA,OAAAA,KAAA,CAAAoG,QAAA;kBAAA;kBACA,IAAAwE,aAAA,CAAAjM,MAAA;oBACAkF,OAAA,CAAAY,IAAA,aAAAmG,aAAA;kBACA;gBACA;cACA;cAAAE,SAAA,CAAAvF,CAAA;cAAA;YAAA;cAAA,MAEA,IAAA4F,KAAA,CAAAxK,QAAA,CAAAoF,GAAA;YAAA;cAGA;cACAoE,OAAA,CAAA9N,mBAAA;cACA8N,OAAA,CAAA7N,eAAA;cACA6N,OAAA,CAAA5N,mBAAA;cACA4N,OAAA,CAAA3N,eAAA;cACA2N,OAAA,CAAA1N,WAAA;;cAEA;cACA0N,OAAA,CAAAjK,eAAA;cACAiK,OAAA,CAAAhK,aAAA;cAAA2K,SAAA,CAAAvF,CAAA;cAAA;YAAA;cAAAuF,SAAA,CAAAtF,CAAA;cAAAqF,GAAA,GAAAC,SAAA,CAAAhF,CAAA;cAGAjC,OAAA,CAAA7D,KAAA,YAAA6K,GAAA;cACAV,OAAA,CAAApK,QAAA,CAAAC,KAAA,aAAA6K,GAAA,CAAA7E,OAAA;YAAA;cAAA8E,SAAA,CAAAtF,CAAA;cAEA2E,OAAA,CAAAjN,kBAAA;cACAiN,OAAA,CAAAhN,cAAA;cAAA,OAAA2N,SAAA,CAAAM,CAAA;YAAA;cAAA,OAAAN,SAAA,CAAA7E,CAAA;UAAA;QAAA,GAAAmE,QAAA;MAAA;IAEA;IAEA;IACAiB,cAAA,WAAAA,eAAAC,UAAA;MACA,IAAAA,UAAA;QACA;MACA,WAAAA,UAAA;QACA;MACA,WAAAA,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAnM,cAAA,WAAAA,eAAA;MAAA,IAAAoM,OAAA;MACA,SAAArN,iBAAA;QACA;MACA;;MAEA;MACA,KAAA6E,MAAA,CAAAyI,QAAA;QACA,KAAAC,kBAAA;QACA;MACA;MAEA;QACA;QACA,SAAAxN,UAAA;UACA,KAAAA,UAAA,CAAAmB,OAAA;UACA,KAAAnB,UAAA;QACA;;QAEA;QACA,IAAAyN,eAAA,GAAAvI,QAAA,CAAAwI,cAAA;QACA,KAAAD,eAAA;UACA;QACA;;QAEA;QACAA,eAAA,CAAAE,SAAA;;QAEA;QACA,KAAA1M,SAAA;UACA;UACA,KAAA6D,MAAA,CAAAyI,QAAA,KAAAzI,MAAA,CAAAyI,QAAA,CAAAnE,OAAA;YAEAkE,OAAA,CAAAM,kBAAA;YACA;UACA;UAEA;YACA;YACAN,OAAA,CAAAtN,UAAA,GAAA8E,MAAA,CAAAyI,QAAA,CAAAnE,OAAA,6BAAAyE,gBAAA,CAAA9K,OAAA,MAAA8K,gBAAA,CAAA9K,OAAA,MAAA8K,gBAAA,CAAA9K,OAAA,MAAA8K,gBAAA,CAAA9K,OAAA;cACA+K,MAAA;cAAA;cACAC,OAAA,GACA;gBAAArR,IAAA;gBAAAsR,KAAA;cAAA,GACA;gBAAAtR,IAAA;gBAAAsR,KAAA;cAAA,GACA;gBAAAtR,IAAA;gBAAAsR,KAAA;cAAA,GACA;gBAAAtR,IAAA;gBAAAsR,KAAA;cAAA,GACA;gBAAAtR,IAAA;gBAAAsR,KAAA;cAAA,GACA;gBAAAtR,IAAA;gBAAAsR,KAAA;cAAA,GACA;gBAAAtR,IAAA;gBAAAsR,KAAA;cAAA,GACA;gBAAAtR,IAAA;gBAAAsR,KAAA;cAAA,GACA;gBAAAtR,IAAA;gBAAAsR,KAAA;cAAA,EACA;cACAC,aAAA;cACAC,QAAA;cACAC,aAAA;cACAC,cAAA;cACAC,YAAA;cACAC,cAAA;cACA;cACAC,cAAA;cACAC,qBAAA;cACA;cACAC,sBAAA;cACAC,kBAAA;cACA;cACAC,oBAAA,EAAApP,OAAA,CAAAC,GAAA,CAAAC,gBAAA;cACAmP,iBAAA;cACA;cACAC,QAAA;YAAA,wBAEA,uCACA,2BAEA,oCACA;cACAC,aAAA,WAAAA,cAAAC,GAAA;gBACA,IAAAC,MAAA,GAAAD,GAAA,CAAAC,MAAA;gBACAA,MAAA,CAAAC,EAAA,yBAAAF,GAAA;kBACA,IAAAG,MAAA,GAAAH,GAAA,CAAAjS,IAAA;kBACA,IAAAoS,MAAA,CAAAC,OAAA;oBACAjE,UAAA;sBACA,IAAAkE,aAAA,GAAAC,WAAA;wBACA;0BACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;0BACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;4BACAC,aAAA,CAAAN,aAAA;4BACAF,MAAA,CAAAS,UAAA;0BACA;wBACA,SAAAC,CAAA;0BACA;wBAAA;sBAEA;sBACA1E,UAAA;wBAAA,OAAAwE,aAAA,CAAAN,aAAA;sBAAA;oBACA;kBACA;gBACA;cACA;YACA,EACA;UACA,SAAArN,KAAA;YACAuL,OAAA,CAAAE,kBAAA;YACA;UACA;;UAEA;UACA,IAAAF,OAAA,CAAAtN,UAAA,IAAAsN,OAAA,CAAAtN,UAAA,CAAAiP,EAAA;YACA3B,OAAA,CAAAtN,UAAA,CAAAiP,EAAA;cACA3B,OAAA,CAAA/L,2BAAA;YACA;YAEA+L,OAAA,CAAAtN,UAAA,CAAAiP,EAAA;cACA3B,OAAA,CAAA/L,2BAAA;YACA;YAEA+L,OAAA,CAAAtN,UAAA,CAAAiP,EAAA;cACA3B,OAAA,CAAArN,iBAAA;cACAqN,OAAA,CAAAtN,UAAA,CAAAuK,OAAA;YACA;UACA;QACA;MAEA,SAAAxI,KAAA;QACA,KAAAyL,kBAAA;MACA;IACA;IAEA;IACAhM,kCAAA,WAAAA,mCAAA;MACA,UAAAxB,UAAA,UAAAC,iBAAA;QACA;MACA;MAEA;QACA,IAAA4P,UAAA,QAAA7P,UAAA,CAAA8P,OAAA;QACA,IAAAC,uBAAA,QAAAC,qBAAA,CAAAH,UAAA;QACA,KAAAvR,mBAAA,QAAA2R,0BAAA,CAAAF,uBAAA;QACA,KAAA1R,eAAA,QAAAsC,uBAAA,CAAAoP,uBAAA;MACA,SAAAhO,KAAA;QACA6D,OAAA,CAAAoH,IAAA,eAAAjL,KAAA;MACA;IACA;IAEA;IACAyL,kBAAA,WAAAA,mBAAA;MAAA,IAAA0C,OAAA;MACA,IAAAzC,eAAA,GAAAvI,QAAA,CAAAwI,cAAA;MACA,IAAAD,eAAA;QACA,IAAA0C,QAAA,GAAAjL,QAAA,CAAAC,aAAA;QACAgL,QAAA,CAAAC,SAAA;QACAD,QAAA,CAAAE,WAAA;QACAF,QAAA,CAAAG,KAAA;QACAH,QAAA,CAAAI,KAAA,CAAAC,OAAA;;QAEA;QACAL,QAAA,CAAAM,gBAAA,oBAAAb,CAAA;UACA;UACAM,OAAA,CAAA7R,eAAA,GAAAuR,CAAA,CAAAc,MAAA,CAAAJ,KAAA;UACAJ,OAAA,CAAA5R,mBAAA,GAAAsR,CAAA,CAAAc,MAAA,CAAAJ,KAAA;QACA;QAEA7C,eAAA,CAAAE,SAAA;QACAF,eAAA,CAAAlI,WAAA,CAAA4K,QAAA;QACA,KAAAlQ,iBAAA;MACA;IACA;IAIA;IACAuL,gBAAA,WAAAA,iBAAAmF,OAAA;MACA,SAAA3Q,UAAA,SAAAC,iBAAA;QACA,KAAAD,UAAA,CAAAuK,OAAA,CAAAoG,OAAA;MACA;QACA,KAAAtS,eAAA,GAAAsS,OAAA;QACA,KAAArS,mBAAA,GAAAqS,OAAA;MACA;IACA;IAIA;IACAtP,QAAA,WAAAA,SAAAuP,IAAA,EAAAC,IAAA;MACA,IAAAC,OAAA;MACA,IAAAC,SAAA,YAAAC,iBAAA;QAAA,IAAAC,OAAA;QAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAzQ,MAAA,EAAA0Q,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;UAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;QAAA;QACA,IAAAC,KAAA,YAAAA,MAAA;UACAC,YAAA,CAAAV,OAAA;UACAA,OAAA;UACAF,IAAA,CAAAa,KAAA,CAAAR,OAAA,EAAAG,IAAA;QACA;QACAI,YAAA,CAAAV,OAAA;QACAA,OAAA,GAAA5F,UAAA,CAAAqG,KAAA,EAAAV,IAAA;MACA;;MAEA;MACAE,SAAA,CAAApP,MAAA;QACA6P,YAAA,CAAAV,OAAA;QACAA,OAAA;MACA;MAEA,OAAAC,SAAA;IACA;IAEA;IACAf,qBAAA,WAAAA,sBAAAW,OAAA;MACA,KAAAA,OAAA,SAAAA,OAAA;;MAEA;MACA,IAAAe,aAAA,GAAA5M,MAAA,CAAA6M,QAAA,CAAAC,MAAA;MACA,IAAAC,QAAA,OAAAC,MAAA,CAAAJ,aAAA,CAAAtI,OAAA;MAEA,OAAAuH,OAAA,CAAAvH,OAAA,CAAAyI,QAAA;IACA;IAEA;IACAvQ,aAAA,WAAAA,cAAA;MACA,UAAAjD,eAAA,CAAAuC,IAAA;QACA,KAAArC,eAAA;QACA,KAAAC,WAAA;QACA;MACA;MAEA;QACA,IAAAuT,WAAA,QAAAC,oBAAA,MAAA3T,eAAA;QACA,KAAAE,eAAA,GAAAwT,WAAA,CAAA5G,SAAA,CAAArF,GAAA,WAAA2C,QAAA;UAAA,WAAA3F,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA0F,QAAA;YACA2C,SAAA;UAAA;QAAA,CACA;QACA,KAAA5M,WAAA,GAAAuT,WAAA,CAAA1G,MAAA;QACA;QACA,KAAA1M,iBAAA,QAAAN,eAAA;MACA,SAAA0D,KAAA;QACA,KAAAvD,WAAA,cAAAuD,KAAA,CAAAgG,OAAA;QACA,KAAAxJ,eAAA;MACA;IACA;IAEA;IACAyT,oBAAA,WAAAA,qBAAArB,OAAA;MACA,IAAAxF,SAAA;MACA,IAAAE,MAAA;MAEA,KAAAsF,OAAA,WAAAA,OAAA;QAEA;UAAAxF,SAAA,EAAAA,SAAA;UAAAE,MAAA;QAAA;MACA;MAEA;QAGA,IAAA4G,WAAA,QAAAtR,uBAAA,CAAAgQ,OAAA;QAEA,KAAAsB,WAAA,IAAAA,WAAA,CAAArR,IAAA,GAAAF,MAAA;UACA;YAAAyK,SAAA,EAAAA,SAAA;YAAAE,MAAA;UAAA;QACA;QAEA,IAAA6G,KAAA,GAAAD,WAAA,CAAAE,KAAA,OAAArM,GAAA,WAAAsM,IAAA;UAAA,OAAAA,IAAA,CAAAxR,IAAA;QAAA,GAAAqM,MAAA,WAAAmF,IAAA;UAAA,OAAAA,IAAA,CAAA1R,MAAA;QAAA;QAEA,IAAAwR,KAAA,CAAAxR,MAAA;UACA;YAAAyK,SAAA,EAAAA,SAAA;YAAAE,MAAA;UAAA;QACA;QAIA,IAAAgH,oBAAA;QACA,IAAAC,cAAA;QAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAAxR,MAAA,EAAA6R,CAAA;UACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;;UAEA;UACA,IAAAC,eAAA,QAAAC,mBAAA,CAAAL,IAAA,UAAAM,mBAAA,CAAAN,IAAA;UAEA,IAAAI,eAAA;YACA;YACA,IAAAH,oBAAA,CAAA3R,MAAA;cACA;gBACA,IAAAiS,YAAA,GAAAN,oBAAA,CAAA7K,IAAA;gBACA,IAAAoL,cAAA,QAAAC,sBAAA,CAAAF,YAAA,EAAAL,cAAA;gBACA,IAAAM,cAAA;kBACAzH,SAAA,CAAA/I,IAAA,CAAAwQ,cAAA;gBACA;cACA,SAAA7Q,KAAA;gBACAsJ,MAAA,CAAAjJ,IAAA,WAAA4B,MAAA,CAAAsO,cAAA,uCAAAtO,MAAA,CAAAjC,KAAA,CAAAgG,OAAA;cACA;YACA;;YAEA;YACAsK,oBAAA,IAAAD,IAAA;YACAE,cAAA;UACA;YACA;YACA,IAAAD,oBAAA,CAAA3R,MAAA;cACA2R,oBAAA,CAAAjQ,IAAA,CAAAgQ,IAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAC,oBAAA,CAAA3R,MAAA;UACA;YACA,IAAAiS,aAAA,GAAAN,oBAAA,CAAA7K,IAAA;YACA,IAAAoL,eAAA,QAAAC,sBAAA,CAAAF,aAAA,EAAAL,cAAA;YACA,IAAAM,eAAA;cACAzH,SAAA,CAAA/I,IAAA,CAAAwQ,eAAA;YACA;UACA,SAAA7Q,KAAA;YACAsJ,MAAA,CAAAjJ,IAAA,WAAA4B,MAAA,CAAAsO,cAAA,uCAAAtO,MAAA,CAAAjC,KAAA,CAAAgG,OAAA;UACA;QACA;MAEA,SAAAhG,KAAA;QACAsJ,MAAA,CAAAjJ,IAAA,0CAAA4B,MAAA,CAAAjC,KAAA,CAAAgG,OAAA;MACA;MAEA;QAAAoD,SAAA,EAAAA,SAAA;QAAAE,MAAA,EAAAA;MAAA;IACA;IAEA;IACAoH,mBAAA,WAAAA,oBAAAL,IAAA;MACA;MACA;MACA;MACA,wBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAM,mBAAA,WAAAA,oBAAAN,IAAA;MACA;MACA;MACA,mBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAS,sBAAA,WAAAA,uBAAAF,YAAA;MACA,IAAAT,KAAA,GAAAS,YAAA,CAAAR,KAAA,OAAArM,GAAA,WAAAsM,IAAA;QAAA,OAAAA,IAAA,CAAAxR,IAAA;MAAA,GAAAqM,MAAA,WAAAmF,IAAA;QAAA,OAAAA,IAAA,CAAA1R,MAAA;MAAA;MAEA,IAAAwR,KAAA,CAAAxR,MAAA;QACA,UAAAwM,KAAA;MACA;MAEA,IAAAxP,YAAA;MACA,IAAAE,eAAA;MACA,IAAAmV,iBAAA;;MAEA;MACA,SAAAR,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAAxR,MAAA,EAAA6R,CAAA;QACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;QACA,IAAAS,SAAA,GAAAZ,IAAA,CAAAa,KAAA;QACA,IAAAD,SAAA;UACA,IAAAE,QAAA,GAAAF,SAAA;;UAEA;UACA,IAAAE,QAAA,CAAA/K,QAAA;YACAzK,YAAA;UACA,WAAAwV,QAAA,CAAA/K,QAAA;YACAzK,YAAA;UACA,WAAAwV,QAAA,CAAA/K,QAAA;YACAzK,YAAA;UACA,WAAAwV,QAAA,CAAA/K,QAAA;YACAzK,YAAA;UACA,WAAAwV,QAAA,CAAA/K,QAAA;YACAzK,YAAA;UACA;;UAEA;UACA,IAAAyV,gBAAA,GAAAf,IAAA,CAAAhJ,OAAA,iBAAAxI,IAAA;UACA,IAAAuS,gBAAA;YACAvV,eAAA,GAAAuV,gBAAA;YACAJ,iBAAA,GAAAR,CAAA;UACA;YACAQ,iBAAA,GAAAR,CAAA;UACA;UACA;QACA;MACA;;MAEA;MACA,IAAAQ,iBAAA;QACAA,iBAAA;MACA;;MAEA;MACA,SAAAR,EAAA,GAAAQ,iBAAA,EAAAR,EAAA,GAAAL,KAAA,CAAAxR,MAAA,EAAA6R,EAAA;QACA,IAAAH,KAAA,GAAAF,KAAA,CAAAK,EAAA;;QAEA;QACA,SAAAE,mBAAA,CAAAL,KAAA;UACA;UACAxU,eAAA,GAAAwU,KAAA,CAAAhJ,OAAA,uBAAAxI,IAAA;UACAmS,iBAAA,GAAAR,EAAA;UACA;QACA,YAAA3U,eAAA;UACA;UACAA,eAAA,GAAAwU,KAAA;UACAW,iBAAA,GAAAR,EAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAA,GAAA,GAAAQ,iBAAA,EAAAR,GAAA,GAAAL,KAAA,CAAAxR,MAAA,EAAA6R,GAAA;QACA,IAAAH,MAAA,GAAAF,KAAA,CAAAK,GAAA;;QAEA;QACA,SAAAa,YAAA,CAAAhB,MAAA,UAAAiB,YAAA,CAAAjB,MAAA,KACA,KAAAkB,iBAAA,CAAAlB,MAAA,UAAAmB,gBAAA,CAAAnB,MAAA;UACA;QACA;;QAEA;QACA,IAAAoB,SAAA,GAAApB,MAAA;QACA;QACA,SAAAK,mBAAA,CAAAL,MAAA;UACAoB,SAAA,GAAApB,MAAA,CAAAhJ,OAAA,uBAAAxI,IAAA;QACA;QAEA,IAAA4S,SAAA;UACA,IAAA5V,eAAA;YACAA,eAAA,WAAA4V,SAAA;UACA;YACA5V,eAAA,GAAA4V,SAAA;UACA;QACA;MACA;MAEA,KAAA5V,eAAA;QACA,UAAAsP,KAAA;MACA;;MAEA;MACA,IAAAuG,oBAAA,GAAA7V,eAAA,CAAAgD,IAAA;MACA;MACA,wBAAAkS,IAAA,CAAAW,oBAAA;QACAA,oBAAA,GAAAA,oBAAA,CAAArK,OAAA,0BAAAxI,IAAA;MACA;;MAEA;MACA,IAAA6S,oBAAA,CAAAtL,QAAA;QACAsL,oBAAA,QAAAC,oBAAA,CAAAD,oBAAA;MACA;MAEA,IAAAhL,QAAA;QACA/K,YAAA,EAAAA,YAAA;QACAiG,IAAA,EAAAjG,YAAA;QACAiW,QAAA,OAAAC,kBAAA,CAAAlW,YAAA;QACAE,eAAA,EAAA6V,oBAAA;QACA9C,OAAA,EAAA8C,oBAAA;QACA9V,UAAA;QAAA;QACAkW,WAAA;QACAC,OAAA;QACAC,aAAA;QACA3I,SAAA;MACA;;MAEA;MACA,IAAA4I,YAAA,QAAAC,qBAAA,CAAA/B,KAAA;MACAzJ,QAAA,CAAAqL,OAAA,GAAAE,YAAA,CAAAF,OAAA;;MAEA;MACA,IAAApW,YAAA,mBAAA+K,QAAA,CAAAqL,OAAA,CAAApT,MAAA;QACA;QACAhD,YAAA;QACA+K,QAAA,CAAA/K,YAAA,GAAAA,YAAA;QACA+K,QAAA,CAAA9E,IAAA,GAAAjG,YAAA;QACA+K,QAAA,CAAAkL,QAAA,QAAAC,kBAAA,CAAAlW,YAAA;MACA;;MAEA;MACA,KAAAwW,0BAAA,CAAAhC,KAAA,EAAAzJ,QAAA;;MAEA;MACA,IAAA/K,YAAA,iBAAA+K,QAAA,CAAAsL,aAAA,IAAAtL,QAAA,CAAAsL,aAAA,CAAArT,MAAA;QACA;QACA,kBAAAoS,IAAA,CAAArK,QAAA,CAAAsL,aAAA;UACArW,YAAA;UACA+K,QAAA,CAAA/K,YAAA,GAAAA,YAAA;UACA+K,QAAA,CAAA9E,IAAA,GAAAjG,YAAA;UACA+K,QAAA,CAAAkL,QAAA,QAAAC,kBAAA,CAAAlW,YAAA;QACA;MACA;;MAEA;MACA+K,QAAA,CAAA7K,eAAA,QAAA8V,oBAAA,CAAAjL,QAAA,CAAA7K,eAAA;MACA6K,QAAA,CAAA7K,eAAA,QAAAuW,kBAAA,CAAA1L,QAAA,CAAA7K,eAAA;MACA6K,QAAA,CAAAkI,OAAA,GAAAlI,QAAA,CAAA7K,eAAA;MAEA,OAAA6K,QAAA;IACA;IAEA;IACA2K,YAAA,WAAAA,aAAAhB,IAAA;MACA;MACA;MACA,KAAAA,IAAA,IAAAA,IAAA,CAAA1R,MAAA;QACA;MACA;MAEA,IAAAuS,KAAA,GAAAb,IAAA,CAAAa,KAAA;MACA,IAAAA,KAAA;QACA,IAAAmB,SAAA,GAAAnB,KAAA,IAAAoB,WAAA;QACA,IAAAC,aAAA,GAAArB,KAAA,MAAAA,KAAA,IAAArS,IAAA;;QAEA;QACA;QACA;QACA;QACA,cAAAkS,IAAA,CAAAsB,SAAA,KAAAE,aAAA,CAAA5T,MAAA,QAAA4T,aAAA,CAAA5T,MAAA;UACA;UACA,IAAA6T,eAAA,IACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;UAAA,CACA;UAEA,IAAAC,iBAAA,GAAAD,eAAA,CAAAE,IAAA,WAAAC,OAAA;YAAA,OAAAA,OAAA,CAAA5B,IAAA,CAAAwB,aAAA;UAAA;UACA,QAAAE,iBAAA;QACA;MACA;MACA;IACA;IAEA;IACAnB,YAAA,WAAAA,aAAAjB,IAAA;MACA;MACA,sBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAkB,iBAAA,WAAAA,kBAAAlB,IAAA;MACA;MACA,sBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAmB,gBAAA,WAAAA,iBAAAnB,IAAA;MACA;MACA,sBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAwB,kBAAA,WAAAA,mBAAAjQ,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA;IACA;IAEA;IACAgR,iBAAA,WAAAA,kBAAAhE,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA,IAAAiE,gBAAA,GAAAjE,OAAA,CAAAvH,OAAA,mDAAA6J,KAAA,EAAA4B,MAAA,EAAAC,GAAA,EAAAC,KAAA;UACA,KAAAD,GAAA,SAAA7B,KAAA;UAEA,IAAA6B,GAAA,CAAArF,UAAA,eAAAqF,GAAA,CAAArF,UAAA,gBAAAqF,GAAA,CAAArF,UAAA;YACA,OAAAwD,KAAA;UACA;UAEA,IAAA+B,OAAA,8BAAAF,GAAA,CAAArF,UAAA,QAAAqF,GAAA,SAAAA,GAAA;UACA,cAAA9Q,MAAA,CAAA6Q,MAAA,YAAA7Q,MAAA,CAAAgR,OAAA,QAAAhR,MAAA,CAAA+Q,KAAA;QACA;QAEA,OAAAH,gBAAA;MACA,SAAA7S,KAAA;QACA,OAAA4O,OAAA;MACA;IACA;IAEA;IACAV,0BAAA,WAAAA,2BAAAU,OAAA;MAAA,IAAAsE,OAAA;MACA,KAAAtE,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA;QACA,IAAAiE,gBAAA,GAAAjE;QACA;QAAA,CACAvH,OAAA,oDAAA6J,KAAA,EAAA4B,MAAA,EAAAC,GAAA,EAAAC,KAAA;UACA,KAAAD,GAAA,CAAArF,UAAA,aAAAqF,GAAA,CAAArF,UAAA;YACA,IAAAuF,OAAA,GAAAC,OAAA,CAAAN,iBAAA,CAAAG,GAAA;YACA,cAAA9Q,MAAA,CAAA6Q,MAAA,YAAA7Q,MAAA,CAAAgR,OAAA,QAAAhR,MAAA,CAAA+Q,KAAA;UACA;UACA,OAAA9B,KAAA;QACA;QACA;QAAA,CACA7J,OAAA,sBACAA,OAAA;QACA;QAAA,CACAA,OAAA;QACA;QAAA,CACAA,OAAA,sBACAA,OAAA;QAEA,OAAAwL,gBAAA,CAAAhU,IAAA;MACA,SAAAmB,KAAA;QACA,OAAA4O,OAAA;MACA;IACA;IAEA;IACAhQ,uBAAA,WAAAA,wBAAAgQ,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA,IAAAuE,MAAA;QACA,IAAAC,UAAA;QACA,IAAAC,uBAAA,GAAAzE,OAAA,CAAAvH,OAAA,2BAAA6J,KAAA;UACAiC,MAAA,CAAA9S,IAAA,CAAA6Q,KAAA;UACA,gCAAAjP,MAAA,CAAAmR,UAAA;QACA;QAEA,IAAAlD,WAAA,GAAAmD,uBAAA,CACAhM,OAAA,uBACAA,OAAA,kBACAA,OAAA,qBACAA,OAAA,iBACAA,OAAA;QACA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;;QAEA,IAAAiM,YAAA,GAAApD,WAAA;QACAiD,MAAA,CAAA9R,OAAA,WAAAkS,GAAA,EAAAlN,KAAA;UACA,IAAAiI,WAAA,0BAAArM,MAAA,CAAAoE,KAAA;UACA,IAAAiN,YAAA,CAAAlN,QAAA,CAAAkI,WAAA;YACAgF,YAAA,GAAAA,YAAA,CAAAjM,OAAA,CAAAiH,WAAA,EAAAiF,GAAA;UACA;QACA;QAEA,OAAAD,YAAA,CAAAzU,IAAA;MACA,SAAAmB,KAAA;QACA,OAAA4O,OAAA;MACA;IACA;IAEA;IACAsD,qBAAA,WAAAA,sBAAA/B,KAAA,EAAAqD,UAAA;MACA,IAAAzB,OAAA;MAEA,KAAAzC,KAAA,CAAAmE,OAAA,CAAAtD,KAAA,KAAAqD,UAAA,QAAAA,UAAA,IAAArD,KAAA,CAAAxR,MAAA;QACA;UAAAoT,OAAA,EAAAA;QAAA;MACA;MAEA;QACA,SAAAvB,CAAA,GAAAgD,UAAA,EAAAhD,CAAA,GAAAL,KAAA,CAAAxR,MAAA,EAAA6R,CAAA;UACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;UAEA,KAAAH,IAAA,WAAAA,IAAA;YACA;UACA;;UAEA;UACA,SAAAgB,YAAA,CAAAhB,IAAA;YACA,IAAAqD,WAAA,GAAArD,IAAA,CAAAa,KAAA;YACA,IAAAwC,WAAA;cACA,IAAArB,SAAA,GAAAqB,WAAA,IAAApB,WAAA;cACA,IAAAC,aAAA,GAAAmB,WAAA,MAAAA,WAAA,IAAA7U,IAAA;cAEA,IAAAwT,SAAA,IAAAE,aAAA;gBACAR,OAAA,CAAA1R,IAAA;kBACAgS,SAAA,EAAAA,SAAA;kBACAsB,KAAA,EAAAtB,SAAA;kBACAE,aAAA,EAAAA,aAAA;kBACA3D,OAAA,EAAA2D;gBACA;cACA;YACA;UACA,gBAAAjB,YAAA,CAAAjB,IAAA,UAAAkB,iBAAA,CAAAlB,IAAA,UAAAmB,gBAAA,CAAAnB,IAAA;YACA;YACA;UACA;YACA;YACA;YACA;YACA;YACA,IAAAA,IAAA,CAAA1R,MAAA,2BAAAoS,IAAA,CAAAV,IAAA;cACA,IAAAuD,oBAAA,GAAAvD,IAAA,CAAAa,KAAA;cACA,IAAA0C,oBAAA;gBACA;gBACA,IAAAC,aAAA,GAAAxD,IAAA,CAAAD,KAAA;gBAAA,IAAA0D,SAAA,OAAAC,2BAAA,CAAA/S,OAAA,EACA6S,aAAA;kBAAAG,KAAA;gBAAA;kBAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAvO,CAAA,IAAAmC,IAAA;oBAAA,IAAAwM,YAAA,GAAAF,KAAA,CAAAzF,KAAA;oBACA,KAAA2F,YAAA;;oBAEA;oBACA,SAAA7C,YAAA,CAAA6C,YAAA;sBACA,IAAAhD,KAAA,GAAAgD,YAAA,CAAAhD,KAAA;sBACA,IAAAA,KAAA;wBACA,IAAAmB,UAAA,GAAAnB,KAAA,IAAAoB,WAAA;wBACA,IAAAC,cAAA,GAAArB,KAAA,MAAAA,KAAA,IAAArS,IAAA;wBAEA,IAAAwT,UAAA,IAAAE,cAAA;0BACAR,OAAA,CAAA1R,IAAA;4BACAgS,SAAA,EAAAA,UAAA;4BACAsB,KAAA,EAAAtB,UAAA;4BACAE,aAAA,EAAAA,cAAA;4BACA3D,OAAA,EAAA2D;0BACA;wBACA;sBACA;oBACA;kBACA;gBAAA,SAAA4B,GAAA;kBAAAL,SAAA,CAAAjG,CAAA,CAAAsG,GAAA;gBAAA;kBAAAL,SAAA,CAAA1I,CAAA;gBAAA;cACA;YACA;UACA;QACA;MACA,SAAApL,KAAA;QACA;MAAA;MAGA;QAAA+R,OAAA,EAAAA;MAAA;IACA;IAEA;IACAI,0BAAA,WAAAA,2BAAAhC,KAAA,EAAAzJ,QAAA;MACA,SAAA8J,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAAxR,MAAA,EAAA6R,CAAA;QACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;;QAEA;QACA,IAAA4D,WAAA,GAAA/D,IAAA,CAAAa,KAAA;QACA,IAAAkD,WAAA;UACA1N,QAAA,CAAAsL,aAAA,QAAAqC,gBAAA,CAAAD,WAAA,KAAA1N,QAAA,CAAA/K,YAAA;UACA;QACA;;QAEA;QACA,IAAA2Y,gBAAA,GAAAjE,IAAA,CAAAa,KAAA;QACA,IAAAoD,gBAAA;UACA5N,QAAA,CAAAoL,WAAA,GAAAwC,gBAAA,IAAAzV,IAAA;UACA;QACA;;QAEA;QACA,IAAA0V,eAAA,GAAAlE,IAAA,CAAAa,KAAA;QACA,IAAAqD,eAAA;UACA,IAAA3Y,UAAA,GAAA2Y,eAAA;UACA;UACA,IAAA3Y,UAAA;YACAA,UAAA;UACA;UACA;UACA,uBAAAwK,QAAA,CAAAxK,UAAA;YACA8K,QAAA,CAAA9K,UAAA,GAAAA,UAAA;UACA;UACA;QACA;MACA;;MAEA;MACA;MACA,KAAA8K,QAAA,CAAAsL,aAAA;QACAtL,QAAA,CAAAsL,aAAA,QAAAwC,gCAAA,CAAA9N,QAAA,CAAA7K,eAAA,EAAA6K,QAAA,CAAA/K,YAAA;MACA;IACA;IAEA;IACA6Y,gCAAA,WAAAA,iCAAA3Y,eAAA,EAAAF,YAAA;MACA,KAAAE,eAAA,WAAAA,eAAA;QACA;MACA;MAEA;QACA;QACA,IAAA4Y,QAAA,IACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA,CACA;QAEA,SAAAC,GAAA,MAAAC,SAAA,GAAAF,QAAA,EAAAC,GAAA,GAAAC,SAAA,CAAAhW,MAAA,EAAA+V,GAAA;UAAA,IAAA/B,OAAA,GAAAgC,SAAA,CAAAD,GAAA;UACA,IAAAE,OAAA,GAAA/Y,eAAA,CAAAqV,KAAA,CAAAyB,OAAA;UACA,IAAAiC,OAAA,IAAAA,OAAA,CAAAjW,MAAA;YACA;YACA,IAAAkW,SAAA,GAAAD,OAAA,CAAAA,OAAA,CAAAjW,MAAA;YACA,IAAAmW,MAAA,GAAAD,SAAA,CAAAxN,OAAA,sBAAAxI,IAAA;YAEA,IAAAiW,MAAA;cACA,YAAAT,gBAAA,CAAAS,MAAA,EAAAnZ,YAAA;YACA;UACA;QACA;MACA,SAAAqE,KAAA;QACA;MAAA;MAGA;IACA;IAEA;IACAqU,gBAAA,WAAAA,iBAAAU,UAAA,EAAApZ,YAAA;MACA,KAAAoZ,UAAA,WAAAA,UAAA;QACA;MACA;MAEA;QACA,IAAAC,aAAA,GAAAD,UAAA,CAAAlW,IAAA;QAEA,KAAAmW,aAAA;UACA;QACA;QAEA,IAAArZ,YAAA;UACA;UACA,OAAAqZ,aAAA;QACA;UACA;UACA,OAAAA,aAAA,CAAA1C,WAAA;QACA;MACA,SAAAtS,KAAA;QACA,OAAA+U,UAAA;MACA;IACA;IAMA;IACAE,2BAAA,WAAAA,4BAAAvO,QAAA;MACA,KAAAA,QAAA,KAAAA,QAAA,CAAA7K,eAAA;QACA;MACA;MAEA,IAAA+S,OAAA,GAAAlI,QAAA,CAAA7K,eAAA;;MAEA;MACA,SAAAU,mBAAA,SAAAA,mBAAA,CAAA6J,QAAA;QACA;QACA,IAAA8O,WAAA,QAAAC,uBAAA,CAAAzO,QAAA,CAAA7K,eAAA,OAAAU,mBAAA;QACA,IAAA2Y,WAAA;UACAtG,OAAA,GAAAsG,WAAA;QACA;MACA;;MAEA;MACAtG,OAAA,QAAA+C,oBAAA,CAAA/C,OAAA;;MAEA;MACAA,OAAA,QAAAwD,kBAAA,CAAAxD,OAAA;MAEA,YAAAgE,iBAAA,CAAAhE,OAAA;IACA;IAEA;IACAwG,mBAAA,WAAAA,oBAAAxT,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA;IACA;IAEA;IACA+P,oBAAA,WAAAA,qBAAA/C,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA,OAAAA,OAAA;MACA;;MAEA;MACA,IAAAA,OAAA,CAAAxI,QAAA;QACA;QACA,OAAAwI,OAAA,CAAAvH,OAAA,wDACAA,OAAA;QAAA,CACAA,OAAA;MACA;QACA;QACA,OAAAuH,OAAA,CAAAvH,OAAA,0BAAAxI,IAAA;MACA;IACA;IAEA;IACAuT,kBAAA,WAAAA,mBAAAxD,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA,OAAAA,OAAA;MACA;;MAEA;MACA,IAAAA,OAAA,CAAAxI,QAAA;QACA;QACA,OAAAwI,OAAA,CAAAvH,OAAA,sDACAA,OAAA;QAAA,CACAA,OAAA;MACA;QACA;QACA,OAAAuH,OAAA,CAAAvH,OAAA,wBAAAxI,IAAA;MACA;IACA;IAEA;IACAsW,uBAAA,WAAAA,wBAAAE,YAAA,EAAAH,WAAA;MACA,KAAAG,YAAA,KAAAH,WAAA;QACA,OAAAG,YAAA;MACA;MAEA;QACA;QACA,IAAAC,SAAA,GAAAD,YAAA,CAAAhO,OAAA,uBAAAxI,IAAA;;QAEA;QACA,IAAA0W,UAAA,GAAAL,WAAA,CAAAhE,KAAA;QAAA,IAAAsE,UAAA,OAAAzB,2BAAA,CAAA/S,OAAA,EAEAuU,UAAA;UAAAE,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAvB,CAAA,MAAAwB,MAAA,GAAAD,UAAA,CAAAjQ,CAAA,IAAAmC,IAAA;YAAA,IAAAgO,SAAA,GAAAD,MAAA,CAAAlH,KAAA;YACA,IAAAoH,aAAA,GAAAD,SAAA,CAAArO,OAAA,iBAAAxI,IAAA;YACA;YACA,IAAA+W,kBAAA,GAAAD,aAAA,CAAAtO,OAAA,0BAAAxI,IAAA;YACA,IAAA+W,kBAAA,CAAAxP,QAAA,CAAAkP,SAAA,CAAA/N,SAAA;cACA;cACA,YAAAoK,oBAAA,CAAA+D,SAAA;YACA;UACA;QAAA,SAAAvB,GAAA;UAAAqB,UAAA,CAAA3H,CAAA,CAAAsG,GAAA;QAAA;UAAAqB,UAAA,CAAApK,CAAA;QAAA;QAEA,OAAAiK,YAAA;MACA,SAAArV,KAAA;QACA,OAAAqV,YAAA;MACA;IACA;IAGA;IACAQ,YAAA,WAAAA,aAAA;MACA,KAAAra,WAAA,CAAAC,OAAA;MACA,KAAAyE,eAAA;IACA;IACA;IACA4V,WAAA,WAAAA,YAAA;MACA,KAAAta,WAAA,CAAAG,YAAA;MACA,KAAAH,WAAA,CAAAI,UAAA;MACA,KAAAJ,WAAA,CAAAK,eAAA;MACA,KAAAL,WAAA,CAAAC,OAAA;MACA,KAAAyE,eAAA;IACA;EACA;AACA", "ignoreList": []}]}