{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_QuestionCard", "_interopRequireDefault", "require", "_QuestionForm", "_question", "name", "components", "QuestionCard", "QuestionForm", "data", "bankId", "bankName", "statistics", "total", "singleChoice", "multipleChoice", "judgment", "questionList", "queryParams", "pageNum", "pageSize", "questionType", "difficulty", "questionContent", "expandAll", "expandedQuestions", "selectedQuestions", "isAllSelected", "questionFormVisible", "currentQuestionType", "currentQuestionData", "importDrawerVisible", "documentContent", "documentHtmlContent", "parsedQuestions", "parseErrors", "allExpanded", "isSettingFromBackend", "last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentImportDialogVisible", "rulesDialogVisible", "activeRuleTab", "isUploading", "isParsing", "importingQuestions", "importProgress", "importOptions", "reverse", "allowDuplicate", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "uploadHeaders", "Authorization", "$store", "getters", "token", "uploadData", "rich<PERSON><PERSON><PERSON>", "editorInitialized", "watch", "handler", "newVal", "length", "stripHtmlTagsKeepImages", "trim", "debounceParseDocument", "immediate", "_this", "clearImportContent", "$nextTick", "initRichEditor", "destroy", "created", "initPage", "debounce", "parseDocument", "debounceEditorContentChange", "handleEditorContentChangeDebounced", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "cancel", "methods", "_this$$route$query", "$route", "query", "$message", "error", "goBack", "getQuestionList", "getStatistics", "$router", "back", "_this2", "params", "convertQueryParams", "listQuestion", "then", "response", "rows", "catch", "convertedParams", "_objectSpread2", "default", "typeMap", "difficultyMap", "Object", "keys", "for<PERSON>ach", "key", "undefined", "_this3", "getQuestionStatistics", "handleBatchImportClick", "handleAddQuestion", "type", "toggleExpandAll", "handleExportQuestions", "_this4", "$confirm", "concat", "confirmButtonText", "cancelButtonText", "loading", "$loading", "lock", "text", "spinner", "background", "exportQuestionsToWord", "close", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "success", "console", "handleToggleSelectAll", "map", "q", "questionId", "handleBatchDelete", "_this5", "warning", "deleteCount", "confirmMessage", "dangerouslyUseHTMLString", "performBatchDelete", "info", "_this6", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "questionIds", "startTime", "endTime", "duration", "errorMessage", "_t", "w", "_context", "n", "p", "join", "Date", "now", "delQuestion", "toFixed", "v", "msg", "message", "a", "handleQuestionSelect", "selected", "includes", "push", "index", "indexOf", "splice", "handleToggleExpand", "_this7", "question", "handleEditQuestion", "handleCopyQuestion", "copiedQuestion", "createTime", "updateTime", "createBy", "updateBy", "convertQuestionTypeToString", "handleDeleteQuestion", "_this8", "replace", "displayContent", "substring", "handleQuestionFormSuccess", "handleDrawerClose", "done", "_this9", "<PERSON><PERSON><PERSON><PERSON>", "hasParsedQuestions", "showDocumentImportDialog", "_this0", "uploadComponent", "$refs", "documentUpload", "clearFiles", "showRulesDialog", "copyExampleToEditor", "_this1", "htmlTemplate", "setData", "downloadWordTemplate", "beforeUpload", "file", "isValidType", "endsWith", "isLt10M", "size", "handleUploadSuccess", "_this10", "code", "setTimeout", "questions", "collapsed", "errors", "errorCount", "originalContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleUploadError", "toggleQuestion", "$set", "toggleAllQuestions", "_this11", "confirmImport", "_this12", "optionMessages", "importQuestions", "_this13", "_callee2", "questionsToImport", "importData", "result", "successCount", "failCount", "skippedCount", "resultMessage", "skippedErrors", "_t2", "_context2", "_toConsumableArray2", "batchImportQuestions", "warn", "filter", "Error", "f", "formatProgress", "percentage", "_this14", "CKEDITOR", "fallbackToTextarea", "<PERSON><PERSON><PERSON><PERSON>", "getElementById", "innerHTML", "showFallbackEditor", "_defineProperty2", "height", "toolbar", "items", "removeButtons", "language", "removePlugins", "resize_enabled", "extraPlugins", "<PERSON><PERSON><PERSON><PERSON>", "fontSize_sizes", "fontSize_defaultLabel", "colorButton_enableMore", "colorButton_colors", "filebrowserUploadUrl", "image_previewText", "baseHref", "instanceReady", "evt", "editor", "on", "dialog", "getName", "checkInterval", "setInterval", "urlField", "getContentElement", "getValue", "startsWith", "clearInterval", "selectPage", "e", "rawContent", "getData", "contentWithRelativeUrls", "convertUrlsToRelative", "preserveRichTextFormatting", "_this15", "textarea", "className", "placeholder", "value", "style", "cssText", "addEventListener", "target", "content", "func", "wait", "timeout", "debounced", "executedFunction", "_this16", "_len", "arguments", "args", "Array", "_key", "later", "clearTimeout", "apply", "<PERSON><PERSON><PERSON><PERSON>", "location", "origin", "urlRegex", "RegExp", "parseResult", "parseQuestionContent", "textContent", "lines", "split", "line", "currentQuestionLines", "questionNumber", "i", "isQuestionStart", "isQuestionStartLine", "isQuestionTypeStart", "questionText", "parsedQuestion", "parseQuestionFromLines", "test", "contentStartIndex", "typeMatch", "match", "typeText", "remainingContent", "isOptionLine", "isAnswerLine", "isExplanationLine", "isDifficultyLine", "cleanLine", "finalQ<PERSON>ionContent", "removeQuestionNumber", "typeName", "getTypeDisplayName", "explanation", "options", "<PERSON><PERSON><PERSON><PERSON>", "optionResult", "parseOptionsFromLines", "parseQuestionMetaFromLines", "removeQuestionType", "optionKey", "toUpperCase", "optionContent", "excludePatterns", "isDescriptiveText", "some", "pattern", "processImagePaths", "processedContent", "before", "src", "after", "fullSrc", "_this17", "images", "imageIndex", "contentWithPlaceholders", "finalContent", "img", "startIndex", "isArray", "optionMatch", "label", "multipleOptionsMatch", "singleOptions", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "singleOption", "err", "answerMatch", "parseAnswerValue", "explanationMatch", "difficultyMatch", "extractAnswerFromQuestionContent", "patterns", "_i3", "_patterns", "matches", "lastMatch", "answer", "answerText", "trimmedAnswer", "getFormattedQuestionContent", "htmlContent", "extractQuestionFromHtml", "getQuestionTypeName", "plainContent", "plainText", "paragraphs", "_iterator2", "_step2", "paragraph", "paragraphText", "cleanParagraphText", "handleSearch", "resetSearch"], "sources": ["src/views/biz/questionBank/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"handleBatchImportClick\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n                :loading=\"importingQuestions\"\n              >\n                <i class=\"el-icon-upload2\"></i>\n                {{ importingQuestions ? '正在导入...' : '导入题目' }}\n              </el-button>\n\n              <div class=\"import-options\">\n                <el-checkbox\n                  v-model=\"importOptions.reverse\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后将按题目顺序倒序导入，即最后一题先导入\" placement=\"top\">\n                    <span>按题目顺序倒序导入</span>\n                  </el-tooltip>\n                </el-checkbox>\n\n                <el-checkbox\n                  v-model=\"importOptions.allowDuplicate\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后允许导入重复的题目内容，否则会跳过重复题目\" placement=\"top\">\n                    <span>允许题目重复</span>\n                  </el-tooltip>\n                </el-checkbox>\n              </div>\n\n              <div v-if=\"importingQuestions\" class=\"import-progress\">\n                <el-progress\n                  :percentage=\"importProgress\"\n                  :show-text=\"true\"\n                  :format=\"formatProgress\"\n                  status=\"success\"\n                  :stroke-width=\"6\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <!-- <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button> -->\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载Word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1. 建议使用新版Office或WPS软件编辑题目文件，仅支持上传.docx格式的文件<br>\n          2. 题目数量过多、题目文件过大等情况建议分批导入<br>\n          3. 需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport { listQuestion, delQuestion, getQuestionStatistics, batchImportQuestions, exportQuestionsToWord } from '@/api/biz/question'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      expandedQuestions: [],\n      // 选择状态\n      selectedQuestions: [],\n      isAllSelected: false,\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '',\n      parsedQuestions: [],\n      parseErrors: [],\n      allExpanded: true,\n      isSettingFromBackend: false,\n      lastParsedContent: '', // 记录上次解析的内容，避免重复解析\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importingQuestions: false,\n      importProgress: 0,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n        // 如果已经有解析结果且内容没有实质性变化，不重新解析\n        if (this.parsedQuestions.length > 0 && this.lastParsedContent &&\n            this.stripHtmlTagsKeepImages(newVal) === this.stripHtmlTagsKeepImages(this.lastParsedContent)) {\n          return\n        }\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时清空所有内容并初始化编辑器\n          this.clearImportContent()\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数 - 增加延时到2秒，减少卡顿\n    this.debounceParseDocument = this.debounce(this.parseDocument, 2000)\n    // 创建编辑器内容变化的防抖函数 - 延时1.5秒\n    this.debounceEditorContentChange = this.debounce(this.handleEditorContentChangeDebounced, 1500)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n    // 取消所有防抖函数\n    if (this.debounceParseDocument && this.debounceParseDocument.cancel) {\n      this.debounceParseDocument.cancel()\n    }\n    if (this.debounceEditorContentChange && this.debounceEditorContentChange.cancel) {\n      this.debounceEditorContentChange.cancel()\n    }\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(() => {\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(() => {\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n\n\n    // 处理批量导题按钮点击\n    handleBatchImportClick() {\n      this.importDrawerVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      // 确认导出\n      this.$confirm(`确认导出题库\"${this.bankName}\"中的所有题目吗？`, '导出确认', {\n        confirmButtonText: '确定导出',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        const loading = this.$loading({\n          lock: true,\n          text: `正在导出题库中的所有题目...`,\n          spinner: 'el-icon-loading',\n          background: 'rgba(0, 0, 0, 0.7)'\n        })\n\n        // 调用导出API - 导出当前题库的所有题目\n        exportQuestionsToWord({\n          bankId: this.bankId,\n          bankName: this.bankName\n        }).then(response => {\n          loading.close()\n\n          // 创建下载链接\n          const blob = new Blob([response], {\n            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n          })\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n          link.download = `${this.bankName}.docx`\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          this.$message.success(`成功导出题库\"${this.bankName}\"`)\n        }).catch(error => {\n          loading.close()\n          console.error('导出失败:', error)\n          this.$message.error('导出失败，请重试')\n        })\n      }).catch(() => {\n        // 用户取消导出\n      })\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除（优化版本）\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      const deleteCount = this.selectedQuestions.length\n      let confirmMessage = `确认删除选中的 ${deleteCount} 道题目吗？`\n\n      if (deleteCount > 20) {\n        confirmMessage += '\\n\\n注意：题目较多，删除可能需要一些时间，请耐心等待。'\n      }\n\n      this.$confirm(confirmMessage, '批量删除', {\n        confirmButtonText: '确定删除',\n        cancelButtonText: '取消',\n        type: 'warning',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.performBatchDelete()\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 执行批量删除\n    async performBatchDelete() {\n      const deleteCount = this.selectedQuestions.length\n      const loading = this.$loading({\n        lock: true,\n        text: `正在删除 ${deleteCount} 道题目，请稍候...`,\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      })\n\n      try {\n        // 使用真正的批量删除API\n        const questionIds = this.selectedQuestions.join(',')\n        const startTime = Date.now()\n\n        await delQuestion(questionIds) // 调用批量删除API\n\n        const endTime = Date.now()\n        const duration = ((endTime - startTime) / 1000).toFixed(1)\n\n        loading.close()\n        this.$message.success(`成功删除 ${deleteCount} 道题目 (耗时 ${duration}s)`)\n\n        // 清理选择状态\n        this.selectedQuestions = []\n        this.isAllSelected = false\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        loading.close()\n        console.error('批量删除失败:', error)\n\n        let errorMessage = '批量删除失败'\n        if (error.response && error.response.data && error.response.data.msg) {\n          errorMessage = error.response.data.msg\n        } else if (error.message) {\n          errorMessage = error.message\n        }\n\n        this.$message.error(errorMessage)\n      }\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        // 收起题目\n        this.expandedQuestions.splice(index, 1)\n        // 如果当前是\"展开所有\"状态，则取消\"展开所有\"状态\n        if (this.expandAll) {\n          this.expandAll = false\n          // 将其他题目添加到expandedQuestions数组中，除了当前要收起的题目\n          this.questionList.forEach(question => {\n            if (question.questionId !== questionId && !this.expandedQuestions.includes(question.questionId)) {\n              this.expandedQuestions.push(question.questionId)\n            }\n          })\n        }\n      } else {\n        // 展开题目\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(() => {\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      // 检查是否有未保存的内容\n      const hasContent = this.documentContent && this.documentContent.trim().length > 0\n      const hasParsedQuestions = this.parsedQuestions && this.parsedQuestions.length > 0\n\n      if (hasContent || hasParsedQuestions) {\n        let message = '关闭后将丢失当前编辑的内容，确认关闭吗？'\n        if (hasParsedQuestions) {\n          message = `当前已解析出 ${this.parsedQuestions.length} 道题目，关闭后将丢失所有内容，确认关闭吗？`\n        }\n\n        this.$confirm(message, '确认关闭', {\n          confirmButtonText: '确定关闭',\n          cancelButtonText: '继续编辑',\n          type: 'warning'\n        }).then(() => {\n          // 清空内容\n          this.clearImportContent()\n          done()\n        }).catch(() => {\n          // 取消关闭，继续编辑\n        })\n      } else {\n        // 没有内容直接关闭\n        done()\n      }\n    },\n\n    // 清空导入内容\n    clearImportContent() {\n      // 清空文档内容\n      this.documentContent = ''\n      this.documentHtmlContent = ''\n\n      // 清空解析结果\n      this.parsedQuestions = []\n      this.parseErrors = []\n\n      // 重置解析状态\n      this.allExpanded = true\n      this.isSettingFromBackend = false\n      this.lastParsedContent = '' // 清空上次解析的内容记录\n\n      // 重置上传状态\n      this.isUploading = false\n      this.isParsing = false\n      this.importingQuestions = false\n      this.importProgress = 0\n\n      // 重置导入选项\n      this.importOptions = {\n        reverse: false,\n        allowDuplicate: false\n      }\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response) {\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n          this.lastParsedContent = response.originalContent // 记录已解析的内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 5000) // 延长到5秒，确保编辑器内容稳定\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError() {\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      // 构建确认信息\n      let confirmMessage = `确认导入 ${this.parsedQuestions.length} 道题目吗？`\n      let optionMessages = []\n\n      if (this.importOptions.reverse) {\n        optionMessages.push('将按倒序导入')\n      }\n      if (this.importOptions.allowDuplicate) {\n        optionMessages.push('允许重复题目')\n      }\n\n      if (optionMessages.length > 0) {\n        confirmMessage += `\\n\\n导入选项：${optionMessages.join('，')}`\n      }\n\n      this.$confirm(confirmMessage, '确认导入', {\n        confirmButtonText: '确定导入',\n        cancelButtonText: '取消',\n        type: 'info',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      this.importingQuestions = true\n      this.importProgress = 0\n\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n          this.$message.info('已按倒序排列题目')\n        }\n\n        // 模拟进度更新\n        this.importProgress = 10\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate,\n          reverse: this.importOptions.reverse\n        }\n\n        this.importProgress = 30\n\n        const response = await batchImportQuestions(importData)\n\n        this.importProgress = 80\n\n        if (response.code === 200) {\n          this.importProgress = 100\n\n          // 显示详细的导入结果\n          const result = response.data || {}\n          const successCount = result.successCount || 0\n          const failCount = result.failCount || 0\n          const skippedCount = result.skippedCount || 0\n\n          // 构建结果消息\n          let resultMessage = `导入完成：成功 ${successCount} 道`\n\n          if (failCount > 0) {\n            resultMessage += `，失败 ${failCount} 道`\n          }\n\n          if (skippedCount > 0) {\n            resultMessage += `，跳过重复 ${skippedCount} 道`\n          }\n\n          resultMessage += ' 题目'\n\n          // 根据结果类型显示不同的消息\n          if (failCount > 0 || skippedCount > 0) {\n            this.$message.warning(resultMessage)\n          } else {\n            this.$message.success(resultMessage)\n          }\n\n          // 如果有错误信息，显示详情\n          if (result.errors && result.errors.length > 0) {\n            console.warn('导入详情:', result.errors)\n\n            // 如果有跳过的题目，可以显示更详细的信息\n            if (skippedCount > 0) {\n              const skippedErrors = result.errors.filter(error => error.includes('重复跳过'))\n              if (skippedErrors.length > 0) {\n                console.info('跳过的重复题目:', skippedErrors)\n              }\n            }\n          }\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n\n        // 清理状态并关闭抽屉\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        console.error('导入题目失败:', error)\n        this.$message.error('导入失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.importingQuestions = false\n        this.importProgress = 0\n      }\n    },\n\n    // 格式化进度显示\n    formatProgress(percentage) {\n      if (percentage === 100) {\n        return '导入完成'\n      } else if (percentage >= 80) {\n        return '正在保存...'\n      } else if (percentage >= 30) {\n        return '正在处理...'\n      } else {\n        return '准备中...'\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              on: {\n                instanceReady: function(evt) {\n                  const editor = evt.editor\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                }\n              }\n            })\n          } catch (error) {\n            this.fallbackToTextarea()\n            return\n          }\n\n          // 监听内容变化 - 使用防抖优化性能\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('key', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('instanceReady', () => {\n              this.editorInitialized = true\n              this.richEditor.setData('')\n            })\n          }\n        })\n\n      } catch (error) {\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 处理编辑器内容变化（防抖后执行）\n    handleEditorContentChangeDebounced() {\n      if (!this.richEditor || !this.editorInitialized) {\n        return\n      }\n\n      try {\n        const rawContent = this.richEditor.getData()\n        const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n        this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n        this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n      } catch (error) {\n        console.warn('编辑器内容处理失败:', error)\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = '' // 确保文本框为空\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化 - 使用防抖优化性能\n        textarea.addEventListener('input', (e) => {\n          // 立即更新内容，但防抖解析\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        this.documentContent = content\n        this.documentHtmlContent = content\n      }\n    },\n\n\n\n    // 防抖函数 - 优化版本，支持取消\n    debounce(func, wait) {\n      let timeout\n      const debounced = function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          timeout = null\n          func.apply(this, args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n\n      // 添加取消方法\n      debounced.cancel = function() {\n        clearTimeout(timeout)\n        timeout = null\n      }\n\n      return debounced\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n        // 记录解析成功的内容，避免重复解析\n        this.lastParsedContent = this.documentContent\n      } catch (error) {\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n        if (lines.length === 0) {\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号和题型标识\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.questionContent = this.removeQuestionType(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      // 严格验证：避免误将题目内容中的字母+符号识别为选项\n      if (!line || line.length > 200) {\n        return false\n      }\n\n      const match = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n      if (match) {\n        const optionKey = match[1].toUpperCase()\n        const optionContent = match[2] ? match[2].trim() : ''\n\n        // 严格验证条件：\n        // 1. 选项字母必须是A-Z单个字母\n        // 2. 选项内容长度合理（1-100字符）\n        // 3. 排除明显的题目内容描述（如包含\"表示\"、\"数据\"等词汇的长句）\n        if (/^[A-Z]$/.test(optionKey) && optionContent.length > 0 && optionContent.length <= 100) {\n          // 排除明显的题目内容描述\n          const excludePatterns = [\n            /表示.*?数据/,     // 排除\"表示...数据\"这类描述\n            /一般用.*?或/,      // 排除\"一般用...或\"这类描述\n            /通常.*?来/,       // 排除\"通常...来\"这类描述\n            /可以.*?进行/,     // 排除\"可以...进行\"这类描述\n            /.*?坐标.*?表示/   // 排除\"坐标...表示\"这类描述\n          ]\n\n          const isDescriptiveText = excludePatterns.some(pattern => pattern.test(optionContent))\n          return !isDescriptiveText\n        }\n      }\n      return false\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          return `<img${before}src=\"${fullSrc}\"${after}>`\n        })\n\n        return processedContent\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')\n          .replace(/<\\/p>/gi, '\\n')\n          .replace(/<p[^>]*>/gi, '\\n')\n          .replace(/<[^>]*>/g, '')\n          .replace(/\\n\\s*\\n/g, '\\n')\n          // 处理HTML实体字符\n          .replace(/&nbsp;/g, ' ')      // 非断行空格\n          .replace(/&amp;/g, '&')       // &符号\n          .replace(/&lt;/g, '<')        // 小于号\n          .replace(/&gt;/g, '>')        // 大于号\n          .replace(/&quot;/g, '\"')      // 双引号\n          .replace(/&#39;/g, \"'\")       // 单引号\n          .replace(/&hellip;/g, '...')  // 省略号\n          .replace(/&mdash;/g, '—')     // 长破折号\n          .replace(/&ndash;/g, '–')     // 短破折号\n          .replace(/&ldquo;/g, '\"')     // 左双引号\n          .replace(/&rdquo;/g, '\"')     // 右双引号\n          .replace(/&lsquo;/g, \"'\")     // 左单引号\n          .replace(/&rsquo;/g, \"'\")     // 右单引号\n          .replace(/\\s+/g, ' ')         // 多个空白字符替换为单个空格\n\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 使用严格的选项行验证逻辑\n          if (this.isOptionLine(line)) {\n            const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n            if (optionMatch) {\n              const optionKey = optionMatch[1].toUpperCase()\n              const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n              if (optionKey && optionContent) {\n                options.push({\n                  optionKey: optionKey,\n                  label: optionKey,\n                  optionContent: optionContent,\n                  content: optionContent\n                })\n              }\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            // 但是要避免误将题目内容中的字母+符号识别为选项\n            // 只有当行长度较短且不包含描述性文字时才尝试解析多选项\n            if (line.length < 50 && !/表示|数据|一般|通常|可以/.test(line)) {\n              const multipleOptionsMatch = line.match(/([A-Z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Z][.:：．、]\\s*[^\\s]+)*)/g)\n              if (multipleOptionsMatch) {\n                // 处理同一行多个选项的情况\n                const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n                for (const singleOption of singleOptions) {\n                  if (!singleOption) continue\n\n                  // 使用严格的选项验证逻辑\n                  if (this.isOptionLine(singleOption)) {\n                    const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                    if (match) {\n                      const optionKey = match[1].toUpperCase()\n                      const optionContent = match[2] ? match[2].trim() : ''\n\n                      if (optionKey && optionContent) {\n                        options.push({\n                          optionKey: optionKey,\n                          label: optionKey,\n                          optionContent: optionContent,\n                          content: optionContent\n                        })\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        // 忽略错误\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n          // 忽略错误\n        }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n          return answerText || ''\n        }\n    },\n\n\n\n\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      // 清理题型标识：移除题目内容开头的[题型]标识\n      content = this.removeQuestionType(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 清理题目内容中的题型标识\n    removeQuestionType(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，清理标签内的题型标识\n        return content.replace(/<p[^>]*>(\\s*\\[.*?题\\]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\[.*?题\\]\\s*)/, '') // 清理开头的题型标识\n                     .replace(/>\\s*\\[.*?题\\]\\s*/g, '>') // 清理标签后的题型标识\n      } else {\n        // 对于纯文本内容，清理开头的题型标识\n        return content.replace(/^\\s*\\[.*?题\\]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        return plainContent\n      } catch (error) {\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 导入选项样式 */\n.import-options {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  margin-top: 15px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.import-options .el-checkbox {\n  margin-right: 0;\n  margin-bottom: 0;\n}\n\n.import-options .el-checkbox__label {\n  font-size: 14px;\n  color: #333;\n  font-weight: 500;\n}\n\n.import-options .el-tooltip {\n  cursor: help;\n}\n\n.import-progress {\n  margin-top: 20px;\n  padding: 15px;\n  background: #fff;\n  border-radius: 6px;\n  border: 1px solid #e1f5fe;\n}\n\n.import-progress .el-progress {\n  margin-bottom: 0;\n}\n\n.import-progress .el-progress__text {\n  font-size: 14px !important;\n  font-weight: 500;\n  color: #409eff;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuhBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,YAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,MAAA;MACAC,QAAA;MACA;MACAC,UAAA;QACAC,KAAA;QACAC,YAAA;QACAC,cAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAJ,KAAA;MACAK,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACAC,iBAAA;MACA;MACAC,iBAAA;MACAC,aAAA;MACA;MACAC,mBAAA;MACAC,mBAAA;MACAC,mBAAA;MACA;MACAC,mBAAA;MACA;MACAC,eAAA;MACAC,mBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,iBAAA;MAAA;MACAC,2BAAA;MACAC,kBAAA;MACAC,aAAA;MACA;MACAC,WAAA;MACAC,SAAA;MACAC,kBAAA;MACAC,cAAA;MACAC,aAAA;QACAC,OAAA;QACAC,cAAA;MACA;MACA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA;QACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;MACA;MACAC,UAAA;MACA;MACAC,UAAA;MACAC,iBAAA;IACA;EACA;EAEAC,KAAA;IACA;IACA7B,eAAA;MACA8B,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,SAAA1B,oBAAA;UACA;QACA;;QAEA;QACA,SAAAH,eAAA,CAAA8B,MAAA,aAAA1B,iBAAA,IACA,KAAA2B,uBAAA,CAAAF,MAAA,WAAAE,uBAAA,MAAA3B,iBAAA;UACA;QACA;QAEA,IAAAyB,MAAA,IAAAA,MAAA,CAAAG,IAAA;UACA,KAAAC,qBAAA;QACA;UACA,KAAAjC,eAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAiC,SAAA;IACA;IACA;IACArC,mBAAA;MACA+B,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAM,KAAA;QACA,IAAAN,MAAA;UACA;UACA,KAAAO,kBAAA;UACA,KAAAC,SAAA;YACAF,KAAA,CAAAG,cAAA;UACA;QACA;UACA;UACA,SAAAb,UAAA;YACA,KAAAA,UAAA,CAAAc,OAAA;YACA,KAAAd,UAAA;YACA,KAAAC,iBAAA;UACA;QACA;MACA;MACAQ,SAAA;IACA;EACA;EAEAM,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA;IACA,KAAAR,qBAAA,QAAAS,QAAA,MAAAC,aAAA;IACA;IACA,KAAAC,2BAAA,QAAAF,QAAA,MAAAG,kCAAA;IACA;IACA,KAAArB,UAAA;MACAhD,MAAA,OAAAA;IACA;IACA,KAAA2C,aAAA;MACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA;EACA;EAEAuB,OAAA,WAAAA,QAAA;IACA;EAAA,CAEA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAAd,qBAAA,SAAAA,qBAAA,CAAAe,MAAA;MACA,KAAAf,qBAAA,CAAAe,MAAA;IACA;IACA,SAAAJ,2BAAA,SAAAA,2BAAA,CAAAI,MAAA;MACA,KAAAJ,2BAAA,CAAAI,MAAA;IACA;;IAEA;IACA,SAAAvB,UAAA;MACA,KAAAA,UAAA,CAAAc,OAAA;MACA,KAAAd,UAAA;IACA;EACA;EACAwB,OAAA;IACA;IACAR,QAAA,WAAAA,SAAA;MACA,IAAAS,kBAAA,QAAAC,MAAA,CAAAC,KAAA;QAAA5E,MAAA,GAAA0E,kBAAA,CAAA1E,MAAA;QAAAC,QAAA,GAAAyE,kBAAA,CAAAzE,QAAA;MACA,KAAAD,MAAA;QACA,KAAA6E,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MACA,KAAA/E,MAAA,GAAAA,MAAA;MACA,KAAAC,QAAA,GAAAA,QAAA;MACA,KAAAO,WAAA,CAAAR,MAAA,GAAAA,MAAA;MACA,KAAAgF,eAAA;MACA,KAAAC,aAAA;IACA;IACA;IACAF,MAAA,WAAAA,OAAA;MACA,KAAAG,OAAA,CAAAC,IAAA;IACA;IACA;IACAH,eAAA,WAAAA,gBAAA;MAAA,IAAAI,MAAA;MACA;MACA,IAAAC,MAAA,QAAAC,kBAAA,MAAA9E,WAAA;MACA,IAAA+E,sBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,MAAA,CAAA7E,YAAA,GAAAkF,QAAA,CAAAC,IAAA;QACAN,MAAA,CAAAjF,KAAA,GAAAsF,QAAA,CAAAtF,KAAA;MACA,GAAAwF,KAAA;QACAP,MAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAAD,MAAA;MACA,IAAAO,eAAA,OAAAC,cAAA,CAAAC,OAAA,MAAAT,MAAA;;MAEA;MACA,IAAAO,eAAA,CAAAjF,YAAA;QACA,IAAAoF,OAAA;UACA;UACA;UACA;QACA;QACAH,eAAA,CAAAjF,YAAA,GAAAoF,OAAA,CAAAH,eAAA,CAAAjF,YAAA,KAAAiF,eAAA,CAAAjF,YAAA;MACA;;MAEA;MACA,IAAAiF,eAAA,CAAAhF,UAAA;QACA,IAAAoF,aAAA;UACA;UACA;UACA;QACA;QACAJ,eAAA,CAAAhF,UAAA,GAAAoF,aAAA,CAAAJ,eAAA,CAAAhF,UAAA,KAAAgF,eAAA,CAAAhF,UAAA;MACA;;MAEA;MACAqF,MAAA,CAAAC,IAAA,CAAAN,eAAA,EAAAO,OAAA,WAAAC,GAAA;QACA,IAAAR,eAAA,CAAAQ,GAAA,YAAAR,eAAA,CAAAQ,GAAA,cAAAR,eAAA,CAAAQ,GAAA,MAAAC,SAAA;UACA,OAAAT,eAAA,CAAAQ,GAAA;QACA;MACA;MAEA,OAAAR,eAAA;IACA;IACA;IACAX,aAAA,WAAAA,cAAA;MAAA,IAAAqB,MAAA;MACA,IAAAC,+BAAA,OAAAvG,MAAA,EAAAwF,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAApG,UAAA,GAAAuF,QAAA,CAAA1F,IAAA;MACA,GAAA4F,KAAA;QACA;QACAW,MAAA,CAAApG,UAAA;UACAC,KAAA;UACAC,YAAA;UACAC,cAAA;UACAC,QAAA;QACA;MACA;IACA;IAGA;IACAkG,sBAAA,WAAAA,uBAAA;MACA,KAAAnF,mBAAA;IACA;IACA;IACAoF,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAvF,mBAAA,GAAAuF,IAAA;MACA,KAAAtF,mBAAA;MACA,KAAAF,mBAAA;IACA;IACA;IACAyF,eAAA,WAAAA,gBAAA;MACA,KAAA7F,SAAA,SAAAA,SAAA;MACA,UAAAA,SAAA;QACA,KAAAC,iBAAA;MACA;IACA;IAIA;IACA6F,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAC,QAAA,0CAAAC,MAAA,MAAA9G,QAAA;QACA+G,iBAAA;QACAC,gBAAA;QACAP,IAAA;MACA,GAAAlB,IAAA;QACA,IAAA0B,OAAA,GAAAL,MAAA,CAAAM,QAAA;UACAC,IAAA;UACAC,IAAA;UACAC,OAAA;UACAC,UAAA;QACA;;QAEA;QACA,IAAAC,+BAAA;UACAxH,MAAA,EAAA6G,MAAA,CAAA7G,MAAA;UACAC,QAAA,EAAA4G,MAAA,CAAA5G;QACA,GAAAuF,IAAA,WAAAC,QAAA;UACAyB,OAAA,CAAAO,KAAA;;UAEA;UACA,IAAAC,IAAA,OAAAC,IAAA,EAAAlC,QAAA;YACAiB,IAAA;UACA;UACA,IAAAkB,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;UACA,IAAAM,IAAA,GAAAC,QAAA,CAAAC,aAAA;UACAF,IAAA,CAAAG,IAAA,GAAAP,GAAA;UACAI,IAAA,CAAAI,QAAA,MAAArB,MAAA,CAAAF,MAAA,CAAA5G,QAAA;UACAgI,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,IAAA;UACAA,IAAA,CAAAO,KAAA;UACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,IAAA;UACAH,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAb,GAAA;UAEAf,MAAA,CAAAhC,QAAA,CAAA6D,OAAA,0CAAA3B,MAAA,CAAAF,MAAA,CAAA5G,QAAA;QACA,GAAA0F,KAAA,WAAAb,KAAA;UACAoC,OAAA,CAAAO,KAAA;UACAkB,OAAA,CAAA7D,KAAA,UAAAA,KAAA;UACA+B,MAAA,CAAAhC,QAAA,CAAAC,KAAA;QACA;MACA,GAAAa,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAiD,qBAAA,WAAAA,sBAAA;MACA,KAAA3H,aAAA,SAAAA,aAAA;MACA,SAAAA,aAAA;QACA;QACA,KAAAD,iBAAA,QAAAT,YAAA,CAAAsI,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,UAAA;QAAA;QACA,KAAAlE,QAAA,CAAA6D,OAAA,uBAAA3B,MAAA,MAAA/F,iBAAA,CAAAsC,MAAA;MACA;QACA;QACA,KAAAtC,iBAAA;QACA,KAAA6D,QAAA,CAAA6D,OAAA;MACA;IACA;IAIA;IACAM,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAAjI,iBAAA,CAAAsC,MAAA;QACA,KAAAuB,QAAA,CAAAqE,OAAA;QACA;MACA;MAEA,IAAAC,WAAA,QAAAnI,iBAAA,CAAAsC,MAAA;MACA,IAAA8F,cAAA,iDAAArC,MAAA,CAAAoC,WAAA;MAEA,IAAAA,WAAA;QACAC,cAAA;MACA;MAEA,KAAAtC,QAAA,CAAAsC,cAAA;QACApC,iBAAA;QACAC,gBAAA;QACAP,IAAA;QACA2C,wBAAA;MACA,GAAA7D,IAAA;QACAyD,MAAA,CAAAK,kBAAA;MACA,GAAA3D,KAAA;QACAsD,MAAA,CAAApE,QAAA,CAAA0E,IAAA;MACA;IACA;IAEA;IACAD,kBAAA,WAAAA,mBAAA;MAAA,IAAAE,MAAA;MAAA,WAAAC,kBAAA,CAAA3D,OAAA,mBAAA4D,aAAA,CAAA5D,OAAA,IAAA6D,CAAA,UAAAC,QAAA;QAAA,IAAAT,WAAA,EAAAjC,OAAA,EAAA2C,WAAA,EAAAC,SAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,EAAA;QAAA,WAAAR,aAAA,CAAA5D,OAAA,IAAAqE,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAlB,WAAA,GAAAK,MAAA,CAAAxI,iBAAA,CAAAsC,MAAA;cACA4D,OAAA,GAAAsC,MAAA,CAAArC,QAAA;gBACAC,IAAA;gBACAC,IAAA,8BAAAN,MAAA,CAAAoC,WAAA;gBACA7B,OAAA;gBACAC,UAAA;cACA;cAAA6C,QAAA,CAAAE,CAAA;cAGA;cACAT,WAAA,GAAAL,MAAA,CAAAxI,iBAAA,CAAAuJ,IAAA;cACAT,SAAA,GAAAU,IAAA,CAAAC,GAAA;cAAAL,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAAK,qBAAA,EAAAb,WAAA;YAAA;cAAA;cAEAE,OAAA,GAAAS,IAAA,CAAAC,GAAA;cACAT,QAAA,KAAAD,OAAA,GAAAD,SAAA,UAAAa,OAAA;cAEAzD,OAAA,CAAAO,KAAA;cACA+B,MAAA,CAAA3E,QAAA,CAAA6D,OAAA,6BAAA3B,MAAA,CAAAoC,WAAA,wCAAApC,MAAA,CAAAiD,QAAA;;cAEA;cACAR,MAAA,CAAAxI,iBAAA;cACAwI,MAAA,CAAAvI,aAAA;;cAEA;cACAuI,MAAA,CAAAxE,eAAA;cACAwE,MAAA,CAAAvE,aAAA;cAAAmF,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAQ,CAAA;cAGA1D,OAAA,CAAAO,KAAA;cACAkB,OAAA,CAAA7D,KAAA,YAAAoF,EAAA;cAEAD,YAAA;cACA,IAAAC,EAAA,CAAAzE,QAAA,IAAAyE,EAAA,CAAAzE,QAAA,CAAA1F,IAAA,IAAAmK,EAAA,CAAAzE,QAAA,CAAA1F,IAAA,CAAA8K,GAAA;gBACAZ,YAAA,GAAAC,EAAA,CAAAzE,QAAA,CAAA1F,IAAA,CAAA8K,GAAA;cACA,WAAAX,EAAA,CAAAY,OAAA;gBACAb,YAAA,GAAAC,EAAA,CAAAY,OAAA;cACA;cAEAtB,MAAA,CAAA3E,QAAA,CAAAC,KAAA,CAAAmF,YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAW,CAAA;UAAA;QAAA,GAAAnB,OAAA;MAAA;IAEA;IAEA;IACAoB,oBAAA,WAAAA,qBAAAjC,UAAA,EAAAkC,QAAA;MACA,IAAAA,QAAA;QACA,UAAAjK,iBAAA,CAAAkK,QAAA,CAAAnC,UAAA;UACA,KAAA/H,iBAAA,CAAAmK,IAAA,CAAApC,UAAA;QACA;MACA;QACA,IAAAqC,KAAA,QAAApK,iBAAA,CAAAqK,OAAA,CAAAtC,UAAA;QACA,IAAAqC,KAAA;UACA,KAAApK,iBAAA,CAAAsK,MAAA,CAAAF,KAAA;QACA;MACA;;MAEA;MACA,KAAAnK,aAAA,QAAAD,iBAAA,CAAAsC,MAAA,UAAA/C,YAAA,CAAA+C,MAAA;IACA;IACA;IACAiI,kBAAA,WAAAA,mBAAAxC,UAAA;MAAA,IAAAyC,MAAA;MACA,IAAAJ,KAAA,QAAArK,iBAAA,CAAAsK,OAAA,CAAAtC,UAAA;MACA,IAAAqC,KAAA;QACA;QACA,KAAArK,iBAAA,CAAAuK,MAAA,CAAAF,KAAA;QACA;QACA,SAAAtK,SAAA;UACA,KAAAA,SAAA;UACA;UACA,KAAAP,YAAA,CAAA4F,OAAA,WAAAsF,QAAA;YACA,IAAAA,QAAA,CAAA1C,UAAA,KAAAA,UAAA,KAAAyC,MAAA,CAAAzK,iBAAA,CAAAmK,QAAA,CAAAO,QAAA,CAAA1C,UAAA;cACAyC,MAAA,CAAAzK,iBAAA,CAAAoK,IAAA,CAAAM,QAAA,CAAA1C,UAAA;YACA;UACA;QACA;MACA;QACA;QACA,KAAAhI,iBAAA,CAAAoK,IAAA,CAAApC,UAAA;MACA;IACA;IACA;IACA2C,kBAAA,WAAAA,mBAAAD,QAAA;MACA,KAAArK,mBAAA,GAAAqK,QAAA;MACA,KAAAtK,mBAAA,GAAAsK,QAAA,CAAA9K,YAAA;MACA,KAAAO,mBAAA;IACA;IACA;IACAyK,kBAAA,WAAAA,mBAAAF,QAAA;MACA;MACA,IAAAG,cAAA,OAAA/F,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA2F,QAAA;QACA1C,UAAA;QAAA;QACA8C,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;MAAA,EACA;;MAEA;MACA,KAAA5K,mBAAA,GAAAwK,cAAA;MACA,KAAAzK,mBAAA,QAAA8K,2BAAA,CAAAR,QAAA,CAAA9K,YAAA;MACA,KAAAO,mBAAA;IACA;IAEA;IACA+K,2BAAA,WAAAA,4BAAAvF,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA,KAAAA,IAAA;IACA;IACA;IACAwF,oBAAA,WAAAA,qBAAAT,QAAA;MAAA,IAAAU,MAAA;MACA,IAAAtL,eAAA,GAAA4K,QAAA,CAAA5K,eAAA,CAAAuL,OAAA;MACA,IAAAC,cAAA,GAAAxL,eAAA,CAAAyC,MAAA,QAAAzC,eAAA,CAAAyL,SAAA,kBAAAzL,eAAA;MACA,KAAAiG,QAAA,0CAAAC,MAAA,CAAAsF,cAAA;QACArF,iBAAA;QACAC,gBAAA;QACAP,IAAA;MACA,GAAAlB,IAAA;QACA,IAAAkF,qBAAA,EAAAe,QAAA,CAAA1C,UAAA,EAAAvD,IAAA;UACA2G,MAAA,CAAAtH,QAAA,CAAA6D,OAAA;UACAyD,MAAA,CAAAnH,eAAA;UACAmH,MAAA,CAAAlH,aAAA;QACA,GAAAU,KAAA;UACAwG,MAAA,CAAAtH,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA;IACAyH,yBAAA,WAAAA,0BAAA;MACA,KAAArL,mBAAA;MACA,KAAA8D,eAAA;MACA,KAAAC,aAAA;IACA;IAIA;IACAuH,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,UAAA,QAAArL,eAAA,SAAAA,eAAA,CAAAkC,IAAA,GAAAF,MAAA;MACA,IAAAsJ,kBAAA,QAAApL,eAAA,SAAAA,eAAA,CAAA8B,MAAA;MAEA,IAAAqJ,UAAA,IAAAC,kBAAA;QACA,IAAA9B,OAAA;QACA,IAAA8B,kBAAA;UACA9B,OAAA,2CAAA/D,MAAA,MAAAvF,eAAA,CAAA8B,MAAA;QACA;QAEA,KAAAwD,QAAA,CAAAgE,OAAA;UACA9D,iBAAA;UACAC,gBAAA;UACAP,IAAA;QACA,GAAAlB,IAAA;UACA;UACAkH,MAAA,CAAA9I,kBAAA;UACA6I,IAAA;QACA,GAAA9G,KAAA;UACA;QAAA,CACA;MACA;QACA;QACA8G,IAAA;MACA;IACA;IAEA;IACA7I,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAAtC,eAAA;MACA,KAAAC,mBAAA;;MAEA;MACA,KAAAC,eAAA;MACA,KAAAC,WAAA;;MAEA;MACA,KAAAC,WAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,iBAAA;;MAEA;MACA,KAAAI,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,cAAA;;MAEA;MACA,KAAAC,aAAA;QACAC,OAAA;QACAC,cAAA;MACA;IACA;IAEA;IACAuK,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAA9K,WAAA;MACA,KAAAC,SAAA;;MAEA;MACA,KAAA4B,SAAA;QACA,IAAAkJ,eAAA,GAAAD,MAAA,CAAAE,KAAA,CAAAC,cAAA;QACA,IAAAF,eAAA;UACAA,eAAA,CAAAG,UAAA;QACA;MACA;MAEA,KAAArL,2BAAA;IAEA;IAEA;IACAsL,eAAA,WAAAA,gBAAA;MACA,KAAApL,aAAA;MACA,KAAAD,kBAAA;IACA;IAEA;IACAsL,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,YAAA,muDAuBA9J,IAAA;;MAEA;MACA,SAAAP,UAAA,SAAAC,iBAAA;QACA,KAAAD,UAAA,CAAAsK,OAAA,CAAAD,YAAA;MAEA;QACA;QACA,KAAAzJ,SAAA;UACA,IAAAwJ,MAAA,CAAApK,UAAA,IAAAoK,MAAA,CAAAnK,iBAAA;YACAmK,MAAA,CAAApK,UAAA,CAAAsK,OAAA,CAAAD,YAAA;UAEA;QACA;MACA;;MAEA;MACA,KAAAxL,kBAAA;;MAEA;MACA,KAAA+C,QAAA,CAAA6D,OAAA;IAGA;IAIA;IACA8E,oBAAA,WAAAA,qBAAA;MACA,KAAApF,QAAA;IACA;IAEA;IACAqF,YAAA,WAAAA,aAAAC,IAAA;MAGA,IAAAC,WAAA,GAAAD,IAAA,CAAAhH,IAAA,kFACAgH,IAAA,CAAAhH,IAAA,4EACAgH,IAAA,CAAA/N,IAAA,CAAAiO,QAAA,aAAAF,IAAA,CAAA/N,IAAA,CAAAiO,QAAA;MACA,IAAAC,OAAA,GAAAH,IAAA,CAAAI,IAAA;MAEA,KAAAH,WAAA;QACA,KAAA9I,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAA+I,OAAA;QACA,KAAAhJ,QAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,KAAA9B,UAAA,CAAAhD,MAAA,QAAAA,MAAA;;MAEA;MACA,KAAAgC,WAAA;MACA,KAAAC,SAAA;MAIA;IACA;IAEA;IACA8L,mBAAA,WAAAA,oBAAAtI,QAAA;MAAA,IAAAuI,OAAA;MACA,IAAAvI,QAAA,CAAAwI,IAAA;QACA;QACA,KAAAjM,WAAA;QACA,KAAAC,SAAA;;QAIA;QACA,KAAAT,eAAA;QACA,KAAAC,WAAA;;QAEA;QACAyM,UAAA;UACAF,OAAA,CAAAnM,2BAAA;UACAmM,OAAA,CAAA/L,SAAA;QACA;;QAEA;QACA,KAAAN,oBAAA;;QAEA;QACA,IAAA8D,QAAA,CAAA0I,SAAA,IAAA1I,QAAA,CAAA0I,SAAA,CAAA7K,MAAA;UACA,KAAA9B,eAAA,GAAAiE,QAAA,CAAA0I,SAAA,CAAAtF,GAAA,WAAA4C,QAAA;YAAA,WAAA5F,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA2F,QAAA;cACA2C,SAAA;YAAA;UAAA,CACA;UACA;UACA,KAAA1M,WAAA;UACA,KAAAD,WAAA,GAAAgE,QAAA,CAAA4I,MAAA;;UAEA;UACA,IAAAC,UAAA,GAAA7I,QAAA,CAAA4I,MAAA,GAAA5I,QAAA,CAAA4I,MAAA,CAAA/K,MAAA;UACA,IAAAgL,UAAA;YACA,KAAAzJ,QAAA,CAAA6D,OAAA,mCAAA3B,MAAA,CAAAtB,QAAA,CAAA0I,SAAA,CAAA7K,MAAA,sCAAAyD,MAAA,CAAAuH,UAAA;UACA;YACA,KAAAzJ,QAAA,CAAA6D,OAAA,mCAAA3B,MAAA,CAAAtB,QAAA,CAAA0I,SAAA,CAAA7K,MAAA;UACA;QAGA;UACA,KAAAuB,QAAA,CAAAC,KAAA;UACA,KAAAtD,eAAA;UACA,KAAAC,WAAA,GAAAgE,QAAA,CAAA4I,MAAA;QAGA;;QAEA;QACA,IAAA5I,QAAA,CAAA8I,eAAA;UACA,KAAAC,gBAAA,CAAA/I,QAAA,CAAA8I,eAAA;UACA,KAAAjN,eAAA,GAAAmE,QAAA,CAAA8I,eAAA;UACA,KAAAhN,mBAAA,GAAAkE,QAAA,CAAA8I,eAAA;UACA,KAAA3M,iBAAA,GAAA6D,QAAA,CAAA8I,eAAA;QAEA;;QAEA;QACAL,UAAA;UACAF,OAAA,CAAArM,oBAAA;QACA;MACA;QAEA,KAAAkD,QAAA,CAAAC,KAAA,CAAAW,QAAA,CAAAoF,GAAA;QACA;QACA,KAAA7I,WAAA;QACA,KAAAC,SAAA;MACA;IACA;IAEA;IACAwM,iBAAA,WAAAA,kBAAA;MACA,KAAA5J,QAAA,CAAAC,KAAA;;MAEA;MACA,KAAA9C,WAAA;MACA,KAAAC,SAAA;IACA;IAIA;IACAyM,cAAA,WAAAA,eAAAtD,KAAA;MACA,IAAAK,QAAA,QAAAjK,eAAA,CAAA4J,KAAA;MACA,KAAAuD,IAAA,CAAAlD,QAAA,gBAAAA,QAAA,CAAA2C,SAAA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAAnN,WAAA,SAAAA,WAAA;MACA,KAAAF,eAAA,CAAA2E,OAAA,WAAAsF,QAAA;QACAoD,OAAA,CAAAF,IAAA,CAAAlD,QAAA,gBAAAoD,OAAA,CAAAnN,WAAA;MACA;IAEA;IAEA;IACAoN,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,SAAAvN,eAAA,CAAA8B,MAAA;QACA,KAAAuB,QAAA,CAAAqE,OAAA;QACA;MACA;;MAEA;MACA,IAAAE,cAAA,+BAAArC,MAAA,MAAAvF,eAAA,CAAA8B,MAAA;MACA,IAAA0L,cAAA;MAEA,SAAA5M,aAAA,CAAAC,OAAA;QACA2M,cAAA,CAAA7D,IAAA;MACA;MACA,SAAA/I,aAAA,CAAAE,cAAA;QACA0M,cAAA,CAAA7D,IAAA;MACA;MAEA,IAAA6D,cAAA,CAAA1L,MAAA;QACA8F,cAAA,yCAAArC,MAAA,CAAAiI,cAAA,CAAAzE,IAAA;MACA;MAEA,KAAAzD,QAAA,CAAAsC,cAAA;QACApC,iBAAA;QACAC,gBAAA;QACAP,IAAA;QACA2C,wBAAA;MACA,GAAA7D,IAAA;QACAuJ,OAAA,CAAAE,eAAA;MACA,GAAAtJ,KAAA;IACA;IAEA;IACAsJ,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzF,kBAAA,CAAA3D,OAAA,mBAAA4D,aAAA,CAAA5D,OAAA,IAAA6D,CAAA,UAAAwF,SAAA;QAAA,IAAAC,iBAAA,EAAAC,UAAA,EAAA5J,QAAA,EAAA6J,MAAA,EAAAC,YAAA,EAAAC,SAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,GAAA;QAAA,WAAAlG,aAAA,CAAA5D,OAAA,IAAAqE,CAAA,WAAA0F,SAAA;UAAA,kBAAAA,SAAA,CAAAxF,CAAA;YAAA;cACA6E,OAAA,CAAAhN,kBAAA;cACAgN,OAAA,CAAA/M,cAAA;cAAA0N,SAAA,CAAAvF,CAAA;cAGA;cACA8E,iBAAA,OAAAU,mBAAA,CAAAhK,OAAA,EAAAoJ,OAAA,CAAA1N,eAAA;cAEA,IAAA0N,OAAA,CAAA9M,aAAA,CAAAC,OAAA;gBACA+M,iBAAA,CAAA/M,OAAA;gBACA6M,OAAA,CAAArK,QAAA,CAAA0E,IAAA;cACA;;cAEA;cACA2F,OAAA,CAAA/M,cAAA;;cAEA;cACAkN,UAAA;gBACArP,MAAA,EAAAkP,OAAA,CAAAlP,MAAA;gBACAmO,SAAA,EAAAiB,iBAAA;gBACA9M,cAAA,EAAA4M,OAAA,CAAA9M,aAAA,CAAAE,cAAA;gBACAD,OAAA,EAAA6M,OAAA,CAAA9M,aAAA,CAAAC;cACA;cAEA6M,OAAA,CAAA/M,cAAA;cAAA0N,SAAA,CAAAxF,CAAA;cAAA,OAEA,IAAA0F,8BAAA,EAAAV,UAAA;YAAA;cAAA5J,QAAA,GAAAoK,SAAA,CAAAjF,CAAA;cAEAsE,OAAA,CAAA/M,cAAA;cAAA,MAEAsD,QAAA,CAAAwI,IAAA;gBAAA4B,SAAA,CAAAxF,CAAA;gBAAA;cAAA;cACA6E,OAAA,CAAA/M,cAAA;;cAEA;cACAmN,MAAA,GAAA7J,QAAA,CAAA1F,IAAA;cACAwP,YAAA,GAAAD,MAAA,CAAAC,YAAA;cACAC,SAAA,GAAAF,MAAA,CAAAE,SAAA;cACAC,YAAA,GAAAH,MAAA,CAAAG,YAAA,OAEA;cACAC,aAAA,iDAAA3I,MAAA,CAAAwI,YAAA;cAEA,IAAAC,SAAA;gBACAE,aAAA,0BAAA3I,MAAA,CAAAyI,SAAA;cACA;cAEA,IAAAC,YAAA;gBACAC,aAAA,sCAAA3I,MAAA,CAAA0I,YAAA;cACA;cAEAC,aAAA;;cAEA;cACA,IAAAF,SAAA,QAAAC,YAAA;gBACAP,OAAA,CAAArK,QAAA,CAAAqE,OAAA,CAAAwG,aAAA;cACA;gBACAR,OAAA,CAAArK,QAAA,CAAA6D,OAAA,CAAAgH,aAAA;cACA;;cAEA;cACA,IAAAJ,MAAA,CAAAjB,MAAA,IAAAiB,MAAA,CAAAjB,MAAA,CAAA/K,MAAA;gBACAqF,OAAA,CAAAqH,IAAA,UAAAV,MAAA,CAAAjB,MAAA;;gBAEA;gBACA,IAAAoB,YAAA;kBACAE,aAAA,GAAAL,MAAA,CAAAjB,MAAA,CAAA4B,MAAA,WAAAnL,KAAA;oBAAA,OAAAA,KAAA,CAAAoG,QAAA;kBAAA;kBACA,IAAAyE,aAAA,CAAArM,MAAA;oBACAqF,OAAA,CAAAY,IAAA,aAAAoG,aAAA;kBACA;gBACA;cACA;cAAAE,SAAA,CAAAxF,CAAA;cAAA;YAAA;cAAA,MAEA,IAAA6F,KAAA,CAAAzK,QAAA,CAAAoF,GAAA;YAAA;cAGA;cACAqE,OAAA,CAAA7N,mBAAA;cACA6N,OAAA,CAAA5N,eAAA;cACA4N,OAAA,CAAA3N,mBAAA;cACA2N,OAAA,CAAA1N,eAAA;cACA0N,OAAA,CAAAzN,WAAA;;cAEA;cACAyN,OAAA,CAAAlK,eAAA;cACAkK,OAAA,CAAAjK,aAAA;cAAA4K,SAAA,CAAAxF,CAAA;cAAA;YAAA;cAAAwF,SAAA,CAAAvF,CAAA;cAAAsF,GAAA,GAAAC,SAAA,CAAAjF,CAAA;cAGAjC,OAAA,CAAA7D,KAAA,YAAA8K,GAAA;cACAV,OAAA,CAAArK,QAAA,CAAAC,KAAA,aAAA8K,GAAA,CAAA9E,OAAA;YAAA;cAAA+E,SAAA,CAAAvF,CAAA;cAEA4E,OAAA,CAAAhN,kBAAA;cACAgN,OAAA,CAAA/M,cAAA;cAAA,OAAA0N,SAAA,CAAAM,CAAA;YAAA;cAAA,OAAAN,SAAA,CAAA9E,CAAA;UAAA;QAAA,GAAAoE,QAAA;MAAA;IAEA;IAEA;IACAiB,cAAA,WAAAA,eAAAC,UAAA;MACA,IAAAA,UAAA;QACA;MACA,WAAAA,UAAA;QACA;MACA,WAAAA,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAvM,cAAA,WAAAA,eAAA;MAAA,IAAAwM,OAAA;MACA,SAAApN,iBAAA;QACA;MACA;;MAEA;MACA,KAAA2E,MAAA,CAAA0I,QAAA;QACA,KAAAC,kBAAA;QACA;MACA;MAEA;QACA;QACA,SAAAvN,UAAA;UACA,KAAAA,UAAA,CAAAc,OAAA;UACA,KAAAd,UAAA;QACA;;QAEA;QACA,IAAAwN,eAAA,GAAAxI,QAAA,CAAAyI,cAAA;QACA,KAAAD,eAAA;UACA;QACA;;QAEA;QACAA,eAAA,CAAAE,SAAA;;QAEA;QACA,KAAA9M,SAAA;UACA;UACA,KAAAgE,MAAA,CAAA0I,QAAA,KAAA1I,MAAA,CAAA0I,QAAA,CAAAnE,OAAA;YAEAkE,OAAA,CAAAM,kBAAA;YACA;UACA;UAEA;YACA;YACAN,OAAA,CAAArN,UAAA,GAAA4E,MAAA,CAAA0I,QAAA,CAAAnE,OAAA,6BAAAyE,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA;cACAgL,MAAA;cAAA;cACAC,OAAA,GACA;gBAAApR,IAAA;gBAAAqR,KAAA;cAAA,GACA;gBAAArR,IAAA;gBAAAqR,KAAA;cAAA,GACA;gBAAArR,IAAA;gBAAAqR,KAAA;cAAA,GACA;gBAAArR,IAAA;gBAAAqR,KAAA;cAAA,GACA;gBAAArR,IAAA;gBAAAqR,KAAA;cAAA,GACA;gBAAArR,IAAA;gBAAAqR,KAAA;cAAA,GACA;gBAAArR,IAAA;gBAAAqR,KAAA;cAAA,GACA;gBAAArR,IAAA;gBAAAqR,KAAA;cAAA,GACA;gBAAArR,IAAA;gBAAAqR,KAAA;cAAA,EACA;cACAC,aAAA;cACAC,QAAA;cACAC,aAAA;cACAC,cAAA;cACAC,YAAA;cACAC,cAAA;cACA;cACAC,cAAA;cACAC,qBAAA;cACA;cACAC,sBAAA;cACAC,kBAAA;cACA;cACAC,oBAAA,EAAAnP,OAAA,CAAAC,GAAA,CAAAC,gBAAA;cACAkP,iBAAA;cACA;cACAC,QAAA;YAAA,wBAEA,uCACA,2BAEA,oCACA;cACAC,aAAA,WAAAA,cAAAC,GAAA;gBACA,IAAAC,MAAA,GAAAD,GAAA,CAAAC,MAAA;gBACAA,MAAA,CAAAC,EAAA,yBAAAF,GAAA;kBACA,IAAAG,MAAA,GAAAH,GAAA,CAAAhS,IAAA;kBACA,IAAAmS,MAAA,CAAAC,OAAA;oBACAjE,UAAA;sBACA,IAAAkE,aAAA,GAAAC,WAAA;wBACA;0BACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;0BACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;4BACAC,aAAA,CAAAN,aAAA;4BACAF,MAAA,CAAAS,UAAA;0BACA;wBACA,SAAAC,CAAA;0BACA;wBAAA;sBAEA;sBACA1E,UAAA;wBAAA,OAAAwE,aAAA,CAAAN,aAAA;sBAAA;oBACA;kBACA;gBACA;cACA;YACA,EACA;UACA,SAAAtN,KAAA;YACAwL,OAAA,CAAAE,kBAAA;YACA;UACA;;UAEA;UACA,IAAAF,OAAA,CAAArN,UAAA,IAAAqN,OAAA,CAAArN,UAAA,CAAAgP,EAAA;YACA3B,OAAA,CAAArN,UAAA,CAAAgP,EAAA;cACA3B,OAAA,CAAAlM,2BAAA;YACA;YAEAkM,OAAA,CAAArN,UAAA,CAAAgP,EAAA;cACA3B,OAAA,CAAAlM,2BAAA;YACA;YAEAkM,OAAA,CAAArN,UAAA,CAAAgP,EAAA;cACA3B,OAAA,CAAApN,iBAAA;cACAoN,OAAA,CAAArN,UAAA,CAAAsK,OAAA;YACA;UACA;QACA;MAEA,SAAAzI,KAAA;QACA,KAAA0L,kBAAA;MACA;IACA;IAEA;IACAnM,kCAAA,WAAAA,mCAAA;MACA,UAAApB,UAAA,UAAAC,iBAAA;QACA;MACA;MAEA;QACA,IAAA2P,UAAA,QAAA5P,UAAA,CAAA6P,OAAA;QACA,IAAAC,uBAAA,QAAAC,qBAAA,CAAAH,UAAA;QACA,KAAAtR,mBAAA,QAAA0R,0BAAA,CAAAF,uBAAA;QACA,KAAAzR,eAAA,QAAAiC,uBAAA,CAAAwP,uBAAA;MACA,SAAAjO,KAAA;QACA6D,OAAA,CAAAqH,IAAA,eAAAlL,KAAA;MACA;IACA;IAEA;IACA0L,kBAAA,WAAAA,mBAAA;MAAA,IAAA0C,OAAA;MACA,IAAAzC,eAAA,GAAAxI,QAAA,CAAAyI,cAAA;MACA,IAAAD,eAAA;QACA,IAAA0C,QAAA,GAAAlL,QAAA,CAAAC,aAAA;QACAiL,QAAA,CAAAC,SAAA;QACAD,QAAA,CAAAE,WAAA;QACAF,QAAA,CAAAG,KAAA;QACAH,QAAA,CAAAI,KAAA,CAAAC,OAAA;;QAEA;QACAL,QAAA,CAAAM,gBAAA,oBAAAb,CAAA;UACA;UACAM,OAAA,CAAA5R,eAAA,GAAAsR,CAAA,CAAAc,MAAA,CAAAJ,KAAA;UACAJ,OAAA,CAAA3R,mBAAA,GAAAqR,CAAA,CAAAc,MAAA,CAAAJ,KAAA;QACA;QAEA7C,eAAA,CAAAE,SAAA;QACAF,eAAA,CAAAnI,WAAA,CAAA6K,QAAA;QACA,KAAAjQ,iBAAA;MACA;IACA;IAIA;IACAsL,gBAAA,WAAAA,iBAAAmF,OAAA;MACA,SAAA1Q,UAAA,SAAAC,iBAAA;QACA,KAAAD,UAAA,CAAAsK,OAAA,CAAAoG,OAAA;MACA;QACA,KAAArS,eAAA,GAAAqS,OAAA;QACA,KAAApS,mBAAA,GAAAoS,OAAA;MACA;IACA;IAIA;IACAzP,QAAA,WAAAA,SAAA0P,IAAA,EAAAC,IAAA;MACA,IAAAC,OAAA;MACA,IAAAC,SAAA,YAAAC,iBAAA;QAAA,IAAAC,OAAA;QAAA,SAAAC,IAAA,GAAAC,SAAA,CAAA7Q,MAAA,EAAA8Q,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;UAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;QAAA;QACA,IAAAC,KAAA,YAAAA,MAAA;UACAC,YAAA,CAAAV,OAAA;UACAA,OAAA;UACAF,IAAA,CAAAa,KAAA,CAAAR,OAAA,EAAAG,IAAA;QACA;QACAI,YAAA,CAAAV,OAAA;QACAA,OAAA,GAAA5F,UAAA,CAAAqG,KAAA,EAAAV,IAAA;MACA;;MAEA;MACAE,SAAA,CAAAvP,MAAA;QACAgQ,YAAA,CAAAV,OAAA;QACAA,OAAA;MACA;MAEA,OAAAC,SAAA;IACA;IAEA;IACAf,qBAAA,WAAAA,sBAAAW,OAAA;MACA,KAAAA,OAAA,SAAAA,OAAA;;MAEA;MACA,IAAAe,aAAA,GAAA7M,MAAA,CAAA8M,QAAA,CAAAC,MAAA;MACA,IAAAC,QAAA,OAAAC,MAAA,CAAAJ,aAAA,CAAAtI,OAAA;MAEA,OAAAuH,OAAA,CAAAvH,OAAA,CAAAyI,QAAA;IACA;IAEA;IACA1Q,aAAA,WAAAA,cAAA;MACA,UAAA7C,eAAA,CAAAkC,IAAA;QACA,KAAAhC,eAAA;QACA,KAAAC,WAAA;QACA;MACA;MAEA;QACA,IAAAsT,WAAA,QAAAC,oBAAA,MAAA1T,eAAA;QACA,KAAAE,eAAA,GAAAuT,WAAA,CAAA5G,SAAA,CAAAtF,GAAA,WAAA4C,QAAA;UAAA,WAAA5F,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA2F,QAAA;YACA2C,SAAA;UAAA;QAAA,CACA;QACA,KAAA3M,WAAA,GAAAsT,WAAA,CAAA1G,MAAA;QACA;QACA,KAAAzM,iBAAA,QAAAN,eAAA;MACA,SAAAwD,KAAA;QACA,KAAArD,WAAA,cAAAqD,KAAA,CAAAgG,OAAA;QACA,KAAAtJ,eAAA;MACA;IACA;IAEA;IACAwT,oBAAA,WAAAA,qBAAArB,OAAA;MACA,IAAAxF,SAAA;MACA,IAAAE,MAAA;MAEA,KAAAsF,OAAA,WAAAA,OAAA;QAEA;UAAAxF,SAAA,EAAAA,SAAA;UAAAE,MAAA;QAAA;MACA;MAEA;QAGA,IAAA4G,WAAA,QAAA1R,uBAAA,CAAAoQ,OAAA;QAEA,KAAAsB,WAAA,IAAAA,WAAA,CAAAzR,IAAA,GAAAF,MAAA;UACA;YAAA6K,SAAA,EAAAA,SAAA;YAAAE,MAAA;UAAA;QACA;QAEA,IAAA6G,KAAA,GAAAD,WAAA,CAAAE,KAAA,OAAAtM,GAAA,WAAAuM,IAAA;UAAA,OAAAA,IAAA,CAAA5R,IAAA;QAAA,GAAAyM,MAAA,WAAAmF,IAAA;UAAA,OAAAA,IAAA,CAAA9R,MAAA;QAAA;QAEA,IAAA4R,KAAA,CAAA5R,MAAA;UACA;YAAA6K,SAAA,EAAAA,SAAA;YAAAE,MAAA;UAAA;QACA;QAIA,IAAAgH,oBAAA;QACA,IAAAC,cAAA;QAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAA5R,MAAA,EAAAiS,CAAA;UACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;;UAEA;UACA,IAAAC,eAAA,QAAAC,mBAAA,CAAAL,IAAA,UAAAM,mBAAA,CAAAN,IAAA;UAEA,IAAAI,eAAA;YACA;YACA,IAAAH,oBAAA,CAAA/R,MAAA;cACA;gBACA,IAAAqS,YAAA,GAAAN,oBAAA,CAAA9K,IAAA;gBACA,IAAAqL,cAAA,QAAAC,sBAAA,CAAAF,YAAA,EAAAL,cAAA;gBACA,IAAAM,cAAA;kBACAzH,SAAA,CAAAhD,IAAA,CAAAyK,cAAA;gBACA;cACA,SAAA9Q,KAAA;gBACAuJ,MAAA,CAAAlD,IAAA,WAAApE,MAAA,CAAAuO,cAAA,uCAAAvO,MAAA,CAAAjC,KAAA,CAAAgG,OAAA;cACA;YACA;;YAEA;YACAuK,oBAAA,IAAAD,IAAA;YACAE,cAAA;UACA;YACA;YACA,IAAAD,oBAAA,CAAA/R,MAAA;cACA+R,oBAAA,CAAAlK,IAAA,CAAAiK,IAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAC,oBAAA,CAAA/R,MAAA;UACA;YACA,IAAAqS,aAAA,GAAAN,oBAAA,CAAA9K,IAAA;YACA,IAAAqL,eAAA,QAAAC,sBAAA,CAAAF,aAAA,EAAAL,cAAA;YACA,IAAAM,eAAA;cACAzH,SAAA,CAAAhD,IAAA,CAAAyK,eAAA;YACA;UACA,SAAA9Q,KAAA;YACAuJ,MAAA,CAAAlD,IAAA,WAAApE,MAAA,CAAAuO,cAAA,uCAAAvO,MAAA,CAAAjC,KAAA,CAAAgG,OAAA;UACA;QACA;MAEA,SAAAhG,KAAA;QACAuJ,MAAA,CAAAlD,IAAA,0CAAApE,MAAA,CAAAjC,KAAA,CAAAgG,OAAA;MACA;MAEA;QAAAqD,SAAA,EAAAA,SAAA;QAAAE,MAAA,EAAAA;MAAA;IACA;IAEA;IACAoH,mBAAA,WAAAA,oBAAAL,IAAA;MACA;MACA;MACA;MACA,wBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAM,mBAAA,WAAAA,oBAAAN,IAAA;MACA;MACA;MACA,mBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAS,sBAAA,WAAAA,uBAAAF,YAAA;MACA,IAAAT,KAAA,GAAAS,YAAA,CAAAR,KAAA,OAAAtM,GAAA,WAAAuM,IAAA;QAAA,OAAAA,IAAA,CAAA5R,IAAA;MAAA,GAAAyM,MAAA,WAAAmF,IAAA;QAAA,OAAAA,IAAA,CAAA9R,MAAA;MAAA;MAEA,IAAA4R,KAAA,CAAA5R,MAAA;QACA,UAAA4M,KAAA;MACA;MAEA,IAAAvP,YAAA;MACA,IAAAE,eAAA;MACA,IAAAkV,iBAAA;;MAEA;MACA,SAAAR,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAA5R,MAAA,EAAAiS,CAAA;QACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;QACA,IAAAS,SAAA,GAAAZ,IAAA,CAAAa,KAAA;QACA,IAAAD,SAAA;UACA,IAAAE,QAAA,GAAAF,SAAA;;UAEA;UACA,IAAAE,QAAA,CAAAhL,QAAA;YACAvK,YAAA;UACA,WAAAuV,QAAA,CAAAhL,QAAA;YACAvK,YAAA;UACA,WAAAuV,QAAA,CAAAhL,QAAA;YACAvK,YAAA;UACA,WAAAuV,QAAA,CAAAhL,QAAA;YACAvK,YAAA;UACA,WAAAuV,QAAA,CAAAhL,QAAA;YACAvK,YAAA;UACA;;UAEA;UACA,IAAAwV,gBAAA,GAAAf,IAAA,CAAAhJ,OAAA,iBAAA5I,IAAA;UACA,IAAA2S,gBAAA;YACAtV,eAAA,GAAAsV,gBAAA;YACAJ,iBAAA,GAAAR,CAAA;UACA;YACAQ,iBAAA,GAAAR,CAAA;UACA;UACA;QACA;MACA;;MAEA;MACA,IAAAQ,iBAAA;QACAA,iBAAA;MACA;;MAEA;MACA,SAAAR,EAAA,GAAAQ,iBAAA,EAAAR,EAAA,GAAAL,KAAA,CAAA5R,MAAA,EAAAiS,EAAA;QACA,IAAAH,KAAA,GAAAF,KAAA,CAAAK,EAAA;;QAEA;QACA,SAAAE,mBAAA,CAAAL,KAAA;UACA;UACAvU,eAAA,GAAAuU,KAAA,CAAAhJ,OAAA,uBAAA5I,IAAA;UACAuS,iBAAA,GAAAR,EAAA;UACA;QACA,YAAA1U,eAAA;UACA;UACAA,eAAA,GAAAuU,KAAA;UACAW,iBAAA,GAAAR,EAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAA,GAAA,GAAAQ,iBAAA,EAAAR,GAAA,GAAAL,KAAA,CAAA5R,MAAA,EAAAiS,GAAA;QACA,IAAAH,MAAA,GAAAF,KAAA,CAAAK,GAAA;;QAEA;QACA,SAAAa,YAAA,CAAAhB,MAAA,UAAAiB,YAAA,CAAAjB,MAAA,KACA,KAAAkB,iBAAA,CAAAlB,MAAA,UAAAmB,gBAAA,CAAAnB,MAAA;UACA;QACA;;QAEA;QACA,IAAAoB,SAAA,GAAApB,MAAA;QACA;QACA,SAAAK,mBAAA,CAAAL,MAAA;UACAoB,SAAA,GAAApB,MAAA,CAAAhJ,OAAA,uBAAA5I,IAAA;QACA;QAEA,IAAAgT,SAAA;UACA,IAAA3V,eAAA;YACAA,eAAA,WAAA2V,SAAA;UACA;YACA3V,eAAA,GAAA2V,SAAA;UACA;QACA;MACA;MAEA,KAAA3V,eAAA;QACA,UAAAqP,KAAA;MACA;;MAEA;MACA,IAAAuG,oBAAA,GAAA5V,eAAA,CAAA2C,IAAA;MACA;MACA,wBAAAsS,IAAA,CAAAW,oBAAA;QACAA,oBAAA,GAAAA,oBAAA,CAAArK,OAAA,0BAAA5I,IAAA;MACA;;MAEA;MACA,IAAAiT,oBAAA,CAAAvL,QAAA;QACAuL,oBAAA,QAAAC,oBAAA,CAAAD,oBAAA;MACA;MAEA,IAAAhL,QAAA;QACA9K,YAAA,EAAAA,YAAA;QACA+F,IAAA,EAAA/F,YAAA;QACAgW,QAAA,OAAAC,kBAAA,CAAAjW,YAAA;QACAE,eAAA,EAAA4V,oBAAA;QACA9C,OAAA,EAAA8C,oBAAA;QACA7V,UAAA;QAAA;QACAiW,WAAA;QACAC,OAAA;QACAC,aAAA;QACA3I,SAAA;MACA;;MAEA;MACA,IAAA4I,YAAA,QAAAC,qBAAA,CAAA/B,KAAA;MACAzJ,QAAA,CAAAqL,OAAA,GAAAE,YAAA,CAAAF,OAAA;;MAEA;MACA,IAAAnW,YAAA,mBAAA8K,QAAA,CAAAqL,OAAA,CAAAxT,MAAA;QACA;QACA3C,YAAA;QACA8K,QAAA,CAAA9K,YAAA,GAAAA,YAAA;QACA8K,QAAA,CAAA/E,IAAA,GAAA/F,YAAA;QACA8K,QAAA,CAAAkL,QAAA,QAAAC,kBAAA,CAAAjW,YAAA;MACA;;MAEA;MACA,KAAAuW,0BAAA,CAAAhC,KAAA,EAAAzJ,QAAA;;MAEA;MACA,IAAA9K,YAAA,iBAAA8K,QAAA,CAAAsL,aAAA,IAAAtL,QAAA,CAAAsL,aAAA,CAAAzT,MAAA;QACA;QACA,kBAAAwS,IAAA,CAAArK,QAAA,CAAAsL,aAAA;UACApW,YAAA;UACA8K,QAAA,CAAA9K,YAAA,GAAAA,YAAA;UACA8K,QAAA,CAAA/E,IAAA,GAAA/F,YAAA;UACA8K,QAAA,CAAAkL,QAAA,QAAAC,kBAAA,CAAAjW,YAAA;QACA;MACA;;MAEA;MACA8K,QAAA,CAAA5K,eAAA,QAAA6V,oBAAA,CAAAjL,QAAA,CAAA5K,eAAA;MACA4K,QAAA,CAAA5K,eAAA,QAAAsW,kBAAA,CAAA1L,QAAA,CAAA5K,eAAA;MACA4K,QAAA,CAAAkI,OAAA,GAAAlI,QAAA,CAAA5K,eAAA;MAEA,OAAA4K,QAAA;IACA;IAEA;IACA2K,YAAA,WAAAA,aAAAhB,IAAA;MACA;MACA;MACA,KAAAA,IAAA,IAAAA,IAAA,CAAA9R,MAAA;QACA;MACA;MAEA,IAAA2S,KAAA,GAAAb,IAAA,CAAAa,KAAA;MACA,IAAAA,KAAA;QACA,IAAAmB,SAAA,GAAAnB,KAAA,IAAAoB,WAAA;QACA,IAAAC,aAAA,GAAArB,KAAA,MAAAA,KAAA,IAAAzS,IAAA;;QAEA;QACA;QACA;QACA;QACA,cAAAsS,IAAA,CAAAsB,SAAA,KAAAE,aAAA,CAAAhU,MAAA,QAAAgU,aAAA,CAAAhU,MAAA;UACA;UACA,IAAAiU,eAAA,IACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;UAAA,CACA;UAEA,IAAAC,iBAAA,GAAAD,eAAA,CAAAE,IAAA,WAAAC,OAAA;YAAA,OAAAA,OAAA,CAAA5B,IAAA,CAAAwB,aAAA;UAAA;UACA,QAAAE,iBAAA;QACA;MACA;MACA;IACA;IAEA;IACAnB,YAAA,WAAAA,aAAAjB,IAAA;MACA;MACA,sBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAkB,iBAAA,WAAAA,kBAAAlB,IAAA;MACA;MACA,sBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAmB,gBAAA,WAAAA,iBAAAnB,IAAA;MACA;MACA,sBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAwB,kBAAA,WAAAA,mBAAAlQ,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA;IACA;IAEA;IACAiR,iBAAA,WAAAA,kBAAAhE,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA,IAAAiE,gBAAA,GAAAjE,OAAA,CAAAvH,OAAA,mDAAA6J,KAAA,EAAA4B,MAAA,EAAAC,GAAA,EAAAC,KAAA;UACA,KAAAD,GAAA,SAAA7B,KAAA;UAEA,IAAA6B,GAAA,CAAArF,UAAA,eAAAqF,GAAA,CAAArF,UAAA,gBAAAqF,GAAA,CAAArF,UAAA;YACA,OAAAwD,KAAA;UACA;UAEA,IAAA+B,OAAA,8BAAAF,GAAA,CAAArF,UAAA,QAAAqF,GAAA,SAAAA,GAAA;UACA,cAAA/Q,MAAA,CAAA8Q,MAAA,YAAA9Q,MAAA,CAAAiR,OAAA,QAAAjR,MAAA,CAAAgR,KAAA;QACA;QAEA,OAAAH,gBAAA;MACA,SAAA9S,KAAA;QACA,OAAA6O,OAAA;MACA;IACA;IAEA;IACAV,0BAAA,WAAAA,2BAAAU,OAAA;MAAA,IAAAsE,OAAA;MACA,KAAAtE,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA;QACA,IAAAiE,gBAAA,GAAAjE;QACA;QAAA,CACAvH,OAAA,oDAAA6J,KAAA,EAAA4B,MAAA,EAAAC,GAAA,EAAAC,KAAA;UACA,KAAAD,GAAA,CAAArF,UAAA,aAAAqF,GAAA,CAAArF,UAAA;YACA,IAAAuF,OAAA,GAAAC,OAAA,CAAAN,iBAAA,CAAAG,GAAA;YACA,cAAA/Q,MAAA,CAAA8Q,MAAA,YAAA9Q,MAAA,CAAAiR,OAAA,QAAAjR,MAAA,CAAAgR,KAAA;UACA;UACA,OAAA9B,KAAA;QACA;QACA;QAAA,CACA7J,OAAA,sBACAA,OAAA;QACA;QAAA,CACAA,OAAA;QACA;QAAA,CACAA,OAAA,sBACAA,OAAA;QAEA,OAAAwL,gBAAA,CAAApU,IAAA;MACA,SAAAsB,KAAA;QACA,OAAA6O,OAAA;MACA;IACA;IAEA;IACApQ,uBAAA,WAAAA,wBAAAoQ,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA,IAAAuE,MAAA;QACA,IAAAC,UAAA;QACA,IAAAC,uBAAA,GAAAzE,OAAA,CAAAvH,OAAA,2BAAA6J,KAAA;UACAiC,MAAA,CAAA/M,IAAA,CAAA8K,KAAA;UACA,gCAAAlP,MAAA,CAAAoR,UAAA;QACA;QAEA,IAAAlD,WAAA,GAAAmD,uBAAA,CACAhM,OAAA,uBACAA,OAAA,kBACAA,OAAA,qBACAA,OAAA,iBACAA,OAAA;QACA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;;QAEA,IAAAiM,YAAA,GAAApD,WAAA;QACAiD,MAAA,CAAA/R,OAAA,WAAAmS,GAAA,EAAAlN,KAAA;UACA,IAAAiI,WAAA,0BAAAtM,MAAA,CAAAqE,KAAA;UACA,IAAAiN,YAAA,CAAAnN,QAAA,CAAAmI,WAAA;YACAgF,YAAA,GAAAA,YAAA,CAAAjM,OAAA,CAAAiH,WAAA,EAAAiF,GAAA;UACA;QACA;QAEA,OAAAD,YAAA,CAAA7U,IAAA;MACA,SAAAsB,KAAA;QACA,OAAA6O,OAAA;MACA;IACA;IAEA;IACAsD,qBAAA,WAAAA,sBAAA/B,KAAA,EAAAqD,UAAA;MACA,IAAAzB,OAAA;MAEA,KAAAzC,KAAA,CAAAmE,OAAA,CAAAtD,KAAA,KAAAqD,UAAA,QAAAA,UAAA,IAAArD,KAAA,CAAA5R,MAAA;QACA;UAAAwT,OAAA,EAAAA;QAAA;MACA;MAEA;QACA,SAAAvB,CAAA,GAAAgD,UAAA,EAAAhD,CAAA,GAAAL,KAAA,CAAA5R,MAAA,EAAAiS,CAAA;UACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;UAEA,KAAAH,IAAA,WAAAA,IAAA;YACA;UACA;;UAEA;UACA,SAAAgB,YAAA,CAAAhB,IAAA;YACA,IAAAqD,WAAA,GAAArD,IAAA,CAAAa,KAAA;YACA,IAAAwC,WAAA;cACA,IAAArB,SAAA,GAAAqB,WAAA,IAAApB,WAAA;cACA,IAAAC,aAAA,GAAAmB,WAAA,MAAAA,WAAA,IAAAjV,IAAA;cAEA,IAAA4T,SAAA,IAAAE,aAAA;gBACAR,OAAA,CAAA3L,IAAA;kBACAiM,SAAA,EAAAA,SAAA;kBACAsB,KAAA,EAAAtB,SAAA;kBACAE,aAAA,EAAAA,aAAA;kBACA3D,OAAA,EAAA2D;gBACA;cACA;YACA;UACA,gBAAAjB,YAAA,CAAAjB,IAAA,UAAAkB,iBAAA,CAAAlB,IAAA,UAAAmB,gBAAA,CAAAnB,IAAA;YACA;YACA;UACA;YACA;YACA;YACA;YACA;YACA,IAAAA,IAAA,CAAA9R,MAAA,2BAAAwS,IAAA,CAAAV,IAAA;cACA,IAAAuD,oBAAA,GAAAvD,IAAA,CAAAa,KAAA;cACA,IAAA0C,oBAAA;gBACA;gBACA,IAAAC,aAAA,GAAAxD,IAAA,CAAAD,KAAA;gBAAA,IAAA0D,SAAA,OAAAC,2BAAA,CAAAhT,OAAA,EACA8S,aAAA;kBAAAG,KAAA;gBAAA;kBAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAxO,CAAA,IAAAoC,IAAA;oBAAA,IAAAwM,YAAA,GAAAF,KAAA,CAAAzF,KAAA;oBACA,KAAA2F,YAAA;;oBAEA;oBACA,SAAA7C,YAAA,CAAA6C,YAAA;sBACA,IAAAhD,KAAA,GAAAgD,YAAA,CAAAhD,KAAA;sBACA,IAAAA,KAAA;wBACA,IAAAmB,UAAA,GAAAnB,KAAA,IAAAoB,WAAA;wBACA,IAAAC,cAAA,GAAArB,KAAA,MAAAA,KAAA,IAAAzS,IAAA;wBAEA,IAAA4T,UAAA,IAAAE,cAAA;0BACAR,OAAA,CAAA3L,IAAA;4BACAiM,SAAA,EAAAA,UAAA;4BACAsB,KAAA,EAAAtB,UAAA;4BACAE,aAAA,EAAAA,cAAA;4BACA3D,OAAA,EAAA2D;0BACA;wBACA;sBACA;oBACA;kBACA;gBAAA,SAAA4B,GAAA;kBAAAL,SAAA,CAAAjG,CAAA,CAAAsG,GAAA;gBAAA;kBAAAL,SAAA,CAAA1I,CAAA;gBAAA;cACA;YACA;UACA;QACA;MACA,SAAArL,KAAA;QACA;MAAA;MAGA;QAAAgS,OAAA,EAAAA;MAAA;IACA;IAEA;IACAI,0BAAA,WAAAA,2BAAAhC,KAAA,EAAAzJ,QAAA;MACA,SAAA8J,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAA5R,MAAA,EAAAiS,CAAA;QACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;;QAEA;QACA,IAAA4D,WAAA,GAAA/D,IAAA,CAAAa,KAAA;QACA,IAAAkD,WAAA;UACA1N,QAAA,CAAAsL,aAAA,QAAAqC,gBAAA,CAAAD,WAAA,KAAA1N,QAAA,CAAA9K,YAAA;UACA;QACA;;QAEA;QACA,IAAA0Y,gBAAA,GAAAjE,IAAA,CAAAa,KAAA;QACA,IAAAoD,gBAAA;UACA5N,QAAA,CAAAoL,WAAA,GAAAwC,gBAAA,IAAA7V,IAAA;UACA;QACA;;QAEA;QACA,IAAA8V,eAAA,GAAAlE,IAAA,CAAAa,KAAA;QACA,IAAAqD,eAAA;UACA,IAAA1Y,UAAA,GAAA0Y,eAAA;UACA;UACA,IAAA1Y,UAAA;YACAA,UAAA;UACA;UACA;UACA,uBAAAsK,QAAA,CAAAtK,UAAA;YACA6K,QAAA,CAAA7K,UAAA,GAAAA,UAAA;UACA;UACA;QACA;MACA;;MAEA;MACA;MACA,KAAA6K,QAAA,CAAAsL,aAAA;QACAtL,QAAA,CAAAsL,aAAA,QAAAwC,gCAAA,CAAA9N,QAAA,CAAA5K,eAAA,EAAA4K,QAAA,CAAA9K,YAAA;MACA;IACA;IAEA;IACA4Y,gCAAA,WAAAA,iCAAA1Y,eAAA,EAAAF,YAAA;MACA,KAAAE,eAAA,WAAAA,eAAA;QACA;MACA;MAEA;QACA;QACA,IAAA2Y,QAAA,IACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA,CACA;QAEA,SAAAC,GAAA,MAAAC,SAAA,GAAAF,QAAA,EAAAC,GAAA,GAAAC,SAAA,CAAApW,MAAA,EAAAmW,GAAA;UAAA,IAAA/B,OAAA,GAAAgC,SAAA,CAAAD,GAAA;UACA,IAAAE,OAAA,GAAA9Y,eAAA,CAAAoV,KAAA,CAAAyB,OAAA;UACA,IAAAiC,OAAA,IAAAA,OAAA,CAAArW,MAAA;YACA;YACA,IAAAsW,SAAA,GAAAD,OAAA,CAAAA,OAAA,CAAArW,MAAA;YACA,IAAAuW,MAAA,GAAAD,SAAA,CAAAxN,OAAA,sBAAA5I,IAAA;YAEA,IAAAqW,MAAA;cACA,YAAAT,gBAAA,CAAAS,MAAA,EAAAlZ,YAAA;YACA;UACA;QACA;MACA,SAAAmE,KAAA;QACA;MAAA;MAGA;IACA;IAEA;IACAsU,gBAAA,WAAAA,iBAAAU,UAAA,EAAAnZ,YAAA;MACA,KAAAmZ,UAAA,WAAAA,UAAA;QACA;MACA;MAEA;QACA,IAAAC,aAAA,GAAAD,UAAA,CAAAtW,IAAA;QAEA,KAAAuW,aAAA;UACA;QACA;QAEA,IAAApZ,YAAA;UACA;UACA,OAAAoZ,aAAA;QACA;UACA;UACA,OAAAA,aAAA,CAAA1C,WAAA;QACA;MACA,SAAAvS,KAAA;QACA,OAAAgV,UAAA;MACA;IACA;IAMA;IACAE,2BAAA,WAAAA,4BAAAvO,QAAA;MACA,KAAAA,QAAA,KAAAA,QAAA,CAAA5K,eAAA;QACA;MACA;MAEA,IAAA8S,OAAA,GAAAlI,QAAA,CAAA5K,eAAA;;MAEA;MACA,SAAAU,mBAAA,SAAAA,mBAAA,CAAA2J,QAAA;QACA;QACA,IAAA+O,WAAA,QAAAC,uBAAA,CAAAzO,QAAA,CAAA5K,eAAA,OAAAU,mBAAA;QACA,IAAA0Y,WAAA;UACAtG,OAAA,GAAAsG,WAAA;QACA;MACA;;MAEA;MACAtG,OAAA,QAAA+C,oBAAA,CAAA/C,OAAA;;MAEA;MACAA,OAAA,QAAAwD,kBAAA,CAAAxD,OAAA;MAEA,YAAAgE,iBAAA,CAAAhE,OAAA;IACA;IAEA;IACAwG,mBAAA,WAAAA,oBAAAzT,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA;IACA;IAEA;IACAgQ,oBAAA,WAAAA,qBAAA/C,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA,OAAAA,OAAA;MACA;;MAEA;MACA,IAAAA,OAAA,CAAAzI,QAAA;QACA;QACA,OAAAyI,OAAA,CAAAvH,OAAA,wDACAA,OAAA;QAAA,CACAA,OAAA;MACA;QACA;QACA,OAAAuH,OAAA,CAAAvH,OAAA,0BAAA5I,IAAA;MACA;IACA;IAEA;IACA2T,kBAAA,WAAAA,mBAAAxD,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA,OAAAA,OAAA;MACA;;MAEA;MACA,IAAAA,OAAA,CAAAzI,QAAA;QACA;QACA,OAAAyI,OAAA,CAAAvH,OAAA,sDACAA,OAAA;QAAA,CACAA,OAAA;MACA;QACA;QACA,OAAAuH,OAAA,CAAAvH,OAAA,wBAAA5I,IAAA;MACA;IACA;IAEA;IACA0W,uBAAA,WAAAA,wBAAAE,YAAA,EAAAH,WAAA;MACA,KAAAG,YAAA,KAAAH,WAAA;QACA,OAAAG,YAAA;MACA;MAEA;QACA;QACA,IAAAC,SAAA,GAAAD,YAAA,CAAAhO,OAAA,uBAAA5I,IAAA;;QAEA;QACA,IAAA8W,UAAA,GAAAL,WAAA,CAAAhE,KAAA;QAAA,IAAAsE,UAAA,OAAAzB,2BAAA,CAAAhT,OAAA,EAEAwU,UAAA;UAAAE,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAvB,CAAA,MAAAwB,MAAA,GAAAD,UAAA,CAAAlQ,CAAA,IAAAoC,IAAA;YAAA,IAAAgO,SAAA,GAAAD,MAAA,CAAAlH,KAAA;YACA,IAAAoH,aAAA,GAAAD,SAAA,CAAArO,OAAA,iBAAA5I,IAAA;YACA;YACA,IAAAmX,kBAAA,GAAAD,aAAA,CAAAtO,OAAA,0BAAA5I,IAAA;YACA,IAAAmX,kBAAA,CAAAzP,QAAA,CAAAmP,SAAA,CAAA/N,SAAA;cACA;cACA,YAAAoK,oBAAA,CAAA+D,SAAA;YACA;UACA;QAAA,SAAAvB,GAAA;UAAAqB,UAAA,CAAA3H,CAAA,CAAAsG,GAAA;QAAA;UAAAqB,UAAA,CAAApK,CAAA;QAAA;QAEA,OAAAiK,YAAA;MACA,SAAAtV,KAAA;QACA,OAAAsV,YAAA;MACA;IACA;IAGA;IACAQ,YAAA,WAAAA,aAAA;MACA,KAAApa,WAAA,CAAAC,OAAA;MACA,KAAAuE,eAAA;IACA;IACA;IACA6V,WAAA,WAAAA,YAAA;MACA,KAAAra,WAAA,CAAAG,YAAA;MACA,KAAAH,WAAA,CAAAI,UAAA;MACA,KAAAJ,WAAA,CAAAK,eAAA;MACA,KAAAL,WAAA,CAAAC,OAAA;MACA,KAAAuE,eAAA;IACA;EACA;AACA", "ignoreList": []}]}