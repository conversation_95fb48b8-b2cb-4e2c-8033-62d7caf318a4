{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_QuestionCard", "_interopRequireDefault", "require", "_QuestionForm", "_question", "name", "components", "QuestionCard", "QuestionForm", "data", "bankId", "bankName", "statistics", "total", "singleChoice", "multipleChoice", "judgment", "questionList", "queryParams", "pageNum", "pageSize", "questionType", "difficulty", "questionContent", "expandAll", "expandedQuestions", "selectedQuestions", "isAllSelected", "questionFormVisible", "currentQuestionType", "currentQuestionData", "importDrawerVisible", "documentContent", "documentHtmlContent", "parsedQuestions", "parseErrors", "allExpanded", "isSettingFromBackend", "last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentImportDialogVisible", "rulesDialogVisible", "activeRuleTab", "isUploading", "isParsing", "importingQuestions", "importProgress", "importOptions", "reverse", "allowDuplicate", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "uploadHeaders", "Authorization", "$store", "getters", "token", "uploadData", "rich<PERSON><PERSON><PERSON>", "editorInitialized", "watch", "$route", "to", "from", "query", "initPage", "handler", "newVal", "length", "stripHtmlTagsKeepImages", "trim", "debounceParseDocument", "immediate", "_this", "clearImportContent", "$nextTick", "initRichEditor", "destroy", "created", "debounce", "parseDocument", "debounceEditorContentChange", "handleEditorContentChangeDebounced", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "cancel", "methods", "_this$$route$query", "$message", "error", "goBack", "getQuestionList", "getStatistics", "$router", "back", "_this2", "params", "convertQueryParams", "listQuestion", "then", "response", "rows", "catch", "convertedParams", "_objectSpread2", "default", "typeMap", "difficultyMap", "Object", "keys", "for<PERSON>ach", "key", "undefined", "_this3", "getQuestionStatistics", "handleBatchImportClick", "handleAddQuestion", "type", "toggleExpandAll", "handleExportQuestions", "_this4", "$confirm", "concat", "confirmButtonText", "cancelButtonText", "loading", "$loading", "lock", "text", "spinner", "background", "exportQuestionsToWord", "close", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "success", "console", "handleToggleSelectAll", "map", "q", "questionId", "handleBatchDelete", "_this5", "warning", "deleteCount", "confirmMessage", "dangerouslyUseHTMLString", "performBatchDelete", "info", "_this6", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "questionIds", "startTime", "endTime", "duration", "errorMessage", "_t", "w", "_context", "n", "p", "join", "Date", "now", "delQuestion", "toFixed", "v", "msg", "message", "a", "handleQuestionSelect", "selected", "includes", "push", "index", "indexOf", "splice", "handleToggleExpand", "_this7", "question", "handleEditQuestion", "handleCopyQuestion", "copiedQuestion", "createTime", "updateTime", "createBy", "updateBy", "convertQuestionTypeToString", "handleDeleteQuestion", "_this8", "replace", "displayContent", "substring", "handleQuestionFormSuccess", "handleDrawerClose", "done", "_this9", "<PERSON><PERSON><PERSON><PERSON>", "hasParsedQuestions", "showDocumentImportDialog", "_this0", "uploadComponent", "$refs", "documentUpload", "clearFiles", "showRulesDialog", "copyExampleToEditor", "_this1", "htmlTemplate", "setData", "downloadWordTemplate", "beforeUpload", "file", "isValidType", "endsWith", "isLt10M", "size", "handleUploadSuccess", "_this10", "code", "setTimeout", "questions", "collapsed", "errors", "errorCount", "originalContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleUploadError", "toggleQuestion", "$set", "toggleAllQuestions", "_this11", "confirmImport", "_this12", "optionMessages", "importQuestions", "_this13", "_callee2", "questionsToImport", "importData", "result", "successCount", "failCount", "skippedCount", "resultMessage", "skippedErrors", "_t2", "_context2", "_toConsumableArray2", "batchImportQuestions", "warn", "filter", "Error", "f", "formatProgress", "percentage", "_this14", "CKEDITOR", "fallbackToTextarea", "<PERSON><PERSON><PERSON><PERSON>", "getElementById", "innerHTML", "showFallbackEditor", "_defineProperty2", "height", "toolbar", "items", "removeButtons", "language", "removePlugins", "resize_enabled", "extraPlugins", "<PERSON><PERSON><PERSON><PERSON>", "fontSize_sizes", "fontSize_defaultLabel", "colorButton_enableMore", "colorButton_colors", "filebrowserUploadUrl", "image_previewText", "baseHref", "instanceReady", "evt", "editor", "on", "dialog", "getName", "checkInterval", "setInterval", "urlField", "getContentElement", "getValue", "startsWith", "clearInterval", "selectPage", "e", "rawContent", "getData", "contentWithRelativeUrls", "convertUrlsToRelative", "preserveRichTextFormatting", "_this15", "textarea", "className", "placeholder", "value", "style", "cssText", "addEventListener", "target", "content", "func", "wait", "timeout", "debounced", "executedFunction", "_this16", "_len", "arguments", "args", "Array", "_key", "later", "clearTimeout", "apply", "<PERSON><PERSON><PERSON><PERSON>", "location", "origin", "urlRegex", "RegExp", "parseResult", "parseQuestionContent", "textContent", "lines", "split", "line", "currentQuestionLines", "questionNumber", "i", "isQuestionStart", "isQuestionStartLine", "isQuestionTypeStart", "questionText", "parsedQuestion", "parseQuestionFromLines", "test", "contentStartIndex", "typeMatch", "match", "typeText", "remainingContent", "isOptionLine", "isAnswerLine", "isExplanationLine", "isDifficultyLine", "cleanLine", "finalQ<PERSON>ionContent", "removeQuestionNumber", "typeName", "getTypeDisplayName", "explanation", "options", "<PERSON><PERSON><PERSON><PERSON>", "optionResult", "parseOptionsFromLines", "parseQuestionMetaFromLines", "removeQuestionType", "optionKey", "toUpperCase", "optionContent", "excludePatterns", "isDescriptiveText", "some", "pattern", "processImagePaths", "processedContent", "before", "src", "after", "fullSrc", "_this17", "images", "imageIndex", "contentWithPlaceholders", "finalContent", "img", "startIndex", "isArray", "optionMatch", "label", "multipleOptionsMatch", "singleOptions", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "singleOption", "err", "answerMatch", "parseAnswerValue", "explanationMatch", "difficultyMatch", "extractAnswerFromQuestionContent", "patterns", "_i3", "_patterns", "matches", "lastMatch", "answer", "answerText", "trimmedAnswer", "getFormattedQuestionContent", "htmlContent", "extractQuestionFromHtml", "getQuestionTypeName", "plainContent", "plainText", "paragraphs", "_iterator2", "_step2", "paragraph", "paragraphText", "cleanParagraphText", "handleSearch", "resetSearch"], "sources": ["src/views/biz/questionBank/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"handleBatchImportClick\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n                :loading=\"importingQuestions\"\n              >\n                <i class=\"el-icon-upload2\"></i>\n                {{ importingQuestions ? '正在导入...' : '导入题目' }}\n              </el-button>\n\n              <div class=\"import-options\">\n                <el-checkbox\n                  v-model=\"importOptions.reverse\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后将按题目顺序倒序导入，即最后一题先导入\" placement=\"top\">\n                    <span>按题目顺序倒序导入</span>\n                  </el-tooltip>\n                </el-checkbox>\n\n                <el-checkbox\n                  v-model=\"importOptions.allowDuplicate\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后允许导入重复的题目内容，否则会跳过重复题目\" placement=\"top\">\n                    <span>允许题目重复</span>\n                  </el-tooltip>\n                </el-checkbox>\n              </div>\n\n              <div v-if=\"importingQuestions\" class=\"import-progress\">\n                <el-progress\n                  :percentage=\"importProgress\"\n                  :show-text=\"true\"\n                  :format=\"formatProgress\"\n                  status=\"success\"\n                  :stroke-width=\"6\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <!-- <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button> -->\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载Word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1. 建议使用新版Office或WPS软件编辑题目文件，仅支持上传.docx格式的文件<br>\n          2. 题目数量过多、题目文件过大等情况建议分批导入<br>\n          3. 需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport { listQuestion, delQuestion, getQuestionStatistics, batchImportQuestions, exportQuestionsToWord } from '@/api/biz/question'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      expandedQuestions: [],\n      // 选择状态\n      selectedQuestions: [],\n      isAllSelected: false,\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '',\n      parsedQuestions: [],\n      parseErrors: [],\n      allExpanded: true,\n      isSettingFromBackend: false,\n      lastParsedContent: '', // 记录上次解析的内容，避免重复解析\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importingQuestions: false,\n      importProgress: 0,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听路由变化，当路由参数改变时重新初始化页面\n    '$route'(to, from) {\n      // 只有当路由参数中的bankId发生变化时才重新初始化\n      if (to.query.bankId !== from.query.bankId) {\n        this.initPage()\n      }\n    },\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n        // 如果已经有解析结果且内容没有实质性变化，不重新解析\n        if (this.parsedQuestions.length > 0 && this.lastParsedContent &&\n            this.stripHtmlTagsKeepImages(newVal) === this.stripHtmlTagsKeepImages(this.lastParsedContent)) {\n          return\n        }\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时清空所有内容并初始化编辑器\n          this.clearImportContent()\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数 - 增加延时到2秒，减少卡顿\n    this.debounceParseDocument = this.debounce(this.parseDocument, 2000)\n    // 创建编辑器内容变化的防抖函数 - 延时1.5秒\n    this.debounceEditorContentChange = this.debounce(this.handleEditorContentChangeDebounced, 1500)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n    // 取消所有防抖函数\n    if (this.debounceParseDocument && this.debounceParseDocument.cancel) {\n      this.debounceParseDocument.cancel()\n    }\n    if (this.debounceEditorContentChange && this.debounceEditorContentChange.cancel) {\n      this.debounceEditorContentChange.cancel()\n    }\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n\n      // 重置查询参数，确保分页从第一页开始\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: bankId,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      }\n\n      // 重置其他状态\n      this.questionList = []\n      this.total = 0\n      this.expandedQuestions = []\n      this.selectedQuestions = []\n      this.expandAll = false\n\n      // 重新获取数据\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(() => {\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(() => {\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n\n\n    // 处理批量导题按钮点击\n    handleBatchImportClick() {\n      this.importDrawerVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      // 确认导出\n      this.$confirm(`确认导出题库\"${this.bankName}\"中的所有题目吗？`, '导出确认', {\n        confirmButtonText: '确定导出',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        const loading = this.$loading({\n          lock: true,\n          text: `正在导出题库中的所有题目...`,\n          spinner: 'el-icon-loading',\n          background: 'rgba(0, 0, 0, 0.7)'\n        })\n\n        // 调用导出API - 导出当前题库的所有题目\n        exportQuestionsToWord({\n          bankId: this.bankId,\n          bankName: this.bankName\n        }).then(response => {\n          loading.close()\n\n          // 创建下载链接\n          const blob = new Blob([response], {\n            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n          })\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n          link.download = `${this.bankName}.docx`\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          this.$message.success(`成功导出题库\"${this.bankName}\"`)\n        }).catch(error => {\n          loading.close()\n          console.error('导出失败:', error)\n          this.$message.error('导出失败，请重试')\n        })\n      }).catch(() => {\n        // 用户取消导出\n      })\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除（优化版本）\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      const deleteCount = this.selectedQuestions.length\n      let confirmMessage = `确认删除选中的 ${deleteCount} 道题目吗？`\n\n      if (deleteCount > 20) {\n        confirmMessage += '\\n\\n注意：题目较多，删除可能需要一些时间，请耐心等待。'\n      }\n\n      this.$confirm(confirmMessage, '批量删除', {\n        confirmButtonText: '确定删除',\n        cancelButtonText: '取消',\n        type: 'warning',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.performBatchDelete()\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 执行批量删除\n    async performBatchDelete() {\n      const deleteCount = this.selectedQuestions.length\n      const loading = this.$loading({\n        lock: true,\n        text: `正在删除 ${deleteCount} 道题目，请稍候...`,\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      })\n\n      try {\n        // 使用真正的批量删除API\n        const questionIds = this.selectedQuestions.join(',')\n        const startTime = Date.now()\n\n        await delQuestion(questionIds) // 调用批量删除API\n\n        const endTime = Date.now()\n        const duration = ((endTime - startTime) / 1000).toFixed(1)\n\n        loading.close()\n        this.$message.success(`成功删除 ${deleteCount} 道题目 (耗时 ${duration}s)`)\n\n        // 清理选择状态\n        this.selectedQuestions = []\n        this.isAllSelected = false\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        loading.close()\n        console.error('批量删除失败:', error)\n\n        let errorMessage = '批量删除失败'\n        if (error.response && error.response.data && error.response.data.msg) {\n          errorMessage = error.response.data.msg\n        } else if (error.message) {\n          errorMessage = error.message\n        }\n\n        this.$message.error(errorMessage)\n      }\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        // 收起题目\n        this.expandedQuestions.splice(index, 1)\n        // 如果当前是\"展开所有\"状态，则取消\"展开所有\"状态\n        if (this.expandAll) {\n          this.expandAll = false\n          // 将其他题目添加到expandedQuestions数组中，除了当前要收起的题目\n          this.questionList.forEach(question => {\n            if (question.questionId !== questionId && !this.expandedQuestions.includes(question.questionId)) {\n              this.expandedQuestions.push(question.questionId)\n            }\n          })\n        }\n      } else {\n        // 展开题目\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(() => {\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      // 检查是否有未保存的内容\n      const hasContent = this.documentContent && this.documentContent.trim().length > 0\n      const hasParsedQuestions = this.parsedQuestions && this.parsedQuestions.length > 0\n\n      if (hasContent || hasParsedQuestions) {\n        let message = '关闭后将丢失当前编辑的内容，确认关闭吗？'\n        if (hasParsedQuestions) {\n          message = `当前已解析出 ${this.parsedQuestions.length} 道题目，关闭后将丢失所有内容，确认关闭吗？`\n        }\n\n        this.$confirm(message, '确认关闭', {\n          confirmButtonText: '确定关闭',\n          cancelButtonText: '继续编辑',\n          type: 'warning'\n        }).then(() => {\n          // 清空内容\n          this.clearImportContent()\n          done()\n        }).catch(() => {\n          // 取消关闭，继续编辑\n        })\n      } else {\n        // 没有内容直接关闭\n        done()\n      }\n    },\n\n    // 清空导入内容\n    clearImportContent() {\n      // 清空文档内容\n      this.documentContent = ''\n      this.documentHtmlContent = ''\n\n      // 清空解析结果\n      this.parsedQuestions = []\n      this.parseErrors = []\n\n      // 重置解析状态\n      this.allExpanded = true\n      this.isSettingFromBackend = false\n      this.lastParsedContent = '' // 清空上次解析的内容记录\n\n      // 重置上传状态\n      this.isUploading = false\n      this.isParsing = false\n      this.importingQuestions = false\n      this.importProgress = 0\n\n      // 重置导入选项\n      this.importOptions = {\n        reverse: false,\n        allowDuplicate: false\n      }\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response) {\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n          this.lastParsedContent = response.originalContent // 记录已解析的内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 5000) // 延长到5秒，确保编辑器内容稳定\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError() {\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      // 构建确认信息\n      let confirmMessage = `确认导入 ${this.parsedQuestions.length} 道题目吗？`\n      let optionMessages = []\n\n      if (this.importOptions.reverse) {\n        optionMessages.push('将按倒序导入')\n      }\n      if (this.importOptions.allowDuplicate) {\n        optionMessages.push('允许重复题目')\n      }\n\n      if (optionMessages.length > 0) {\n        confirmMessage += `\\n\\n导入选项：${optionMessages.join('，')}`\n      }\n\n      this.$confirm(confirmMessage, '确认导入', {\n        confirmButtonText: '确定导入',\n        cancelButtonText: '取消',\n        type: 'info',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      this.importingQuestions = true\n      this.importProgress = 0\n\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n          this.$message.info('已按倒序排列题目')\n        }\n\n        // 模拟进度更新\n        this.importProgress = 10\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate,\n          reverse: this.importOptions.reverse\n        }\n\n        this.importProgress = 30\n\n        const response = await batchImportQuestions(importData)\n\n        this.importProgress = 80\n\n        if (response.code === 200) {\n          this.importProgress = 100\n\n          // 显示详细的导入结果\n          const result = response.data || {}\n          const successCount = result.successCount || 0\n          const failCount = result.failCount || 0\n          const skippedCount = result.skippedCount || 0\n\n          // 构建结果消息\n          let resultMessage = `导入完成：成功 ${successCount} 道`\n\n          if (failCount > 0) {\n            resultMessage += `，失败 ${failCount} 道`\n          }\n\n          if (skippedCount > 0) {\n            resultMessage += `，跳过重复 ${skippedCount} 道`\n          }\n\n          resultMessage += ' 题目'\n\n          // 根据结果类型显示不同的消息\n          if (failCount > 0 || skippedCount > 0) {\n            this.$message.warning(resultMessage)\n          } else {\n            this.$message.success(resultMessage)\n          }\n\n          // 如果有错误信息，显示详情\n          if (result.errors && result.errors.length > 0) {\n            console.warn('导入详情:', result.errors)\n\n            // 如果有跳过的题目，可以显示更详细的信息\n            if (skippedCount > 0) {\n              const skippedErrors = result.errors.filter(error => error.includes('重复跳过'))\n              if (skippedErrors.length > 0) {\n                console.info('跳过的重复题目:', skippedErrors)\n              }\n            }\n          }\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n\n        // 清理状态并关闭抽屉\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        console.error('导入题目失败:', error)\n        this.$message.error('导入失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.importingQuestions = false\n        this.importProgress = 0\n      }\n    },\n\n    // 格式化进度显示\n    formatProgress(percentage) {\n      if (percentage === 100) {\n        return '导入完成'\n      } else if (percentage >= 80) {\n        return '正在保存...'\n      } else if (percentage >= 30) {\n        return '正在处理...'\n      } else {\n        return '准备中...'\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              on: {\n                instanceReady: function(evt) {\n                  const editor = evt.editor\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                }\n              }\n            })\n          } catch (error) {\n            this.fallbackToTextarea()\n            return\n          }\n\n          // 监听内容变化 - 使用防抖优化性能\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('key', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('instanceReady', () => {\n              this.editorInitialized = true\n              this.richEditor.setData('')\n            })\n          }\n        })\n\n      } catch (error) {\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 处理编辑器内容变化（防抖后执行）\n    handleEditorContentChangeDebounced() {\n      if (!this.richEditor || !this.editorInitialized) {\n        return\n      }\n\n      try {\n        const rawContent = this.richEditor.getData()\n        const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n        this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n        this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n      } catch (error) {\n        console.warn('编辑器内容处理失败:', error)\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = '' // 确保文本框为空\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化 - 使用防抖优化性能\n        textarea.addEventListener('input', (e) => {\n          // 立即更新内容，但防抖解析\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        this.documentContent = content\n        this.documentHtmlContent = content\n      }\n    },\n\n\n\n    // 防抖函数 - 优化版本，支持取消\n    debounce(func, wait) {\n      let timeout\n      const debounced = function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          timeout = null\n          func.apply(this, args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n\n      // 添加取消方法\n      debounced.cancel = function() {\n        clearTimeout(timeout)\n        timeout = null\n      }\n\n      return debounced\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n        // 记录解析成功的内容，避免重复解析\n        this.lastParsedContent = this.documentContent\n      } catch (error) {\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n        if (lines.length === 0) {\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号和题型标识\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.questionContent = this.removeQuestionType(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      // 严格验证：避免误将题目内容中的字母+符号识别为选项\n      if (!line || line.length > 200) {\n        return false\n      }\n\n      const match = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n      if (match) {\n        const optionKey = match[1].toUpperCase()\n        const optionContent = match[2] ? match[2].trim() : ''\n\n        // 严格验证条件：\n        // 1. 选项字母必须是A-Z单个字母\n        // 2. 选项内容长度合理（1-100字符）\n        // 3. 排除明显的题目内容描述（如包含\"表示\"、\"数据\"等词汇的长句）\n        if (/^[A-Z]$/.test(optionKey) && optionContent.length > 0 && optionContent.length <= 100) {\n          // 排除明显的题目内容描述\n          const excludePatterns = [\n            /表示.*?数据/,     // 排除\"表示...数据\"这类描述\n            /一般用.*?或/,      // 排除\"一般用...或\"这类描述\n            /通常.*?来/,       // 排除\"通常...来\"这类描述\n            /可以.*?进行/,     // 排除\"可以...进行\"这类描述\n            /.*?坐标.*?表示/   // 排除\"坐标...表示\"这类描述\n          ]\n\n          const isDescriptiveText = excludePatterns.some(pattern => pattern.test(optionContent))\n          return !isDescriptiveText\n        }\n      }\n      return false\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          return `<img${before}src=\"${fullSrc}\"${after}>`\n        })\n\n        return processedContent\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')\n          .replace(/<\\/p>/gi, '\\n')\n          .replace(/<p[^>]*>/gi, '\\n')\n          .replace(/<[^>]*>/g, '')\n          .replace(/\\n\\s*\\n/g, '\\n')\n          // 处理HTML实体字符\n          .replace(/&nbsp;/g, ' ')      // 非断行空格\n          .replace(/&amp;/g, '&')       // &符号\n          .replace(/&lt;/g, '<')        // 小于号\n          .replace(/&gt;/g, '>')        // 大于号\n          .replace(/&quot;/g, '\"')      // 双引号\n          .replace(/&#39;/g, \"'\")       // 单引号\n          .replace(/&hellip;/g, '...')  // 省略号\n          .replace(/&mdash;/g, '—')     // 长破折号\n          .replace(/&ndash;/g, '–')     // 短破折号\n          .replace(/&ldquo;/g, '\"')     // 左双引号\n          .replace(/&rdquo;/g, '\"')     // 右双引号\n          .replace(/&lsquo;/g, \"'\")     // 左单引号\n          .replace(/&rsquo;/g, \"'\")     // 右单引号\n          .replace(/\\s+/g, ' ')         // 多个空白字符替换为单个空格\n\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 使用严格的选项行验证逻辑\n          if (this.isOptionLine(line)) {\n            const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n            if (optionMatch) {\n              const optionKey = optionMatch[1].toUpperCase()\n              const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n              if (optionKey && optionContent) {\n                options.push({\n                  optionKey: optionKey,\n                  label: optionKey,\n                  optionContent: optionContent,\n                  content: optionContent\n                })\n              }\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            // 但是要避免误将题目内容中的字母+符号识别为选项\n            // 只有当行长度较短且不包含描述性文字时才尝试解析多选项\n            if (line.length < 50 && !/表示|数据|一般|通常|可以/.test(line)) {\n              const multipleOptionsMatch = line.match(/([A-Z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Z][.:：．、]\\s*[^\\s]+)*)/g)\n              if (multipleOptionsMatch) {\n                // 处理同一行多个选项的情况\n                const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n                for (const singleOption of singleOptions) {\n                  if (!singleOption) continue\n\n                  // 使用严格的选项验证逻辑\n                  if (this.isOptionLine(singleOption)) {\n                    const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                    if (match) {\n                      const optionKey = match[1].toUpperCase()\n                      const optionContent = match[2] ? match[2].trim() : ''\n\n                      if (optionKey && optionContent) {\n                        options.push({\n                          optionKey: optionKey,\n                          label: optionKey,\n                          optionContent: optionContent,\n                          content: optionContent\n                        })\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        // 忽略错误\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n          // 忽略错误\n        }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n          return answerText || ''\n        }\n    },\n\n\n\n\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      // 清理题型标识：移除题目内容开头的[题型]标识\n      content = this.removeQuestionType(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 清理题目内容中的题型标识\n    removeQuestionType(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，清理标签内的题型标识\n        return content.replace(/<p[^>]*>(\\s*\\[.*?题\\]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\[.*?题\\]\\s*)/, '') // 清理开头的题型标识\n                     .replace(/>\\s*\\[.*?题\\]\\s*/g, '>') // 清理标签后的题型标识\n      } else {\n        // 对于纯文本内容，清理开头的题型标识\n        return content.replace(/^\\s*\\[.*?题\\]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        return plainContent\n      } catch (error) {\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 导入选项样式 */\n.import-options {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  margin-top: 15px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.import-options .el-checkbox {\n  margin-right: 0;\n  margin-bottom: 0;\n}\n\n.import-options .el-checkbox__label {\n  font-size: 14px;\n  color: #333;\n  font-weight: 500;\n}\n\n.import-options .el-tooltip {\n  cursor: help;\n}\n\n.import-progress {\n  margin-top: 20px;\n  padding: 15px;\n  background: #fff;\n  border-radius: 6px;\n  border: 1px solid #e1f5fe;\n}\n\n.import-progress .el-progress {\n  margin-bottom: 0;\n}\n\n.import-progress .el-progress__text {\n  font-size: 14px !important;\n  font-weight: 500;\n  color: #409eff;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuhBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,YAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,MAAA;MACAC,QAAA;MACA;MACAC,UAAA;QACAC,KAAA;QACAC,YAAA;QACAC,cAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAJ,KAAA;MACAK,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACAC,iBAAA;MACA;MACAC,iBAAA;MACAC,aAAA;MACA;MACAC,mBAAA;MACAC,mBAAA;MACAC,mBAAA;MACA;MACAC,mBAAA;MACA;MACAC,eAAA;MACAC,mBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,iBAAA;MAAA;MACAC,2BAAA;MACAC,kBAAA;MACAC,aAAA;MACA;MACAC,WAAA;MACAC,SAAA;MACAC,kBAAA;MACAC,cAAA;MACAC,aAAA;QACAC,OAAA;QACAC,cAAA;MACA;MACA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA;QACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;MACA;MACAC,UAAA;MACA;MACAC,UAAA;MACAC,iBAAA;IACA;EACA;EAEAC,KAAA;IACA;IACA,mBAAAC,OAAAC,EAAA,EAAAC,IAAA;MACA;MACA,IAAAD,EAAA,CAAAE,KAAA,CAAAvD,MAAA,KAAAsD,IAAA,CAAAC,KAAA,CAAAvD,MAAA;QACA,KAAAwD,QAAA;MACA;IACA;IACA;IACAlC,eAAA;MACAmC,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,SAAA/B,oBAAA;UACA;QACA;;QAEA;QACA,SAAAH,eAAA,CAAAmC,MAAA,aAAA/B,iBAAA,IACA,KAAAgC,uBAAA,CAAAF,MAAA,WAAAE,uBAAA,MAAAhC,iBAAA;UACA;QACA;QAEA,IAAA8B,MAAA,IAAAA,MAAA,CAAAG,IAAA;UACA,KAAAC,qBAAA;QACA;UACA,KAAAtC,eAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAsC,SAAA;IACA;IACA;IACA1C,mBAAA;MACAoC,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAM,KAAA;QACA,IAAAN,MAAA;UACA;UACA,KAAAO,kBAAA;UACA,KAAAC,SAAA;YACAF,KAAA,CAAAG,cAAA;UACA;QACA;UACA;UACA,SAAAlB,UAAA;YACA,KAAAA,UAAA,CAAAmB,OAAA;YACA,KAAAnB,UAAA;YACA,KAAAC,iBAAA;UACA;QACA;MACA;MACAa,SAAA;IACA;EACA;EAEAM,OAAA,WAAAA,QAAA;IACA,KAAAb,QAAA;IACA;IACA,KAAAM,qBAAA,QAAAQ,QAAA,MAAAC,aAAA;IACA;IACA,KAAAC,2BAAA,QAAAF,QAAA,MAAAG,kCAAA;IACA;IACA,KAAAzB,UAAA;MACAhD,MAAA,OAAAA;IACA;IACA,KAAA2C,aAAA;MACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA;EACA;EAEA2B,OAAA,WAAAA,QAAA;IACA;EAAA,CAEA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAAb,qBAAA,SAAAA,qBAAA,CAAAc,MAAA;MACA,KAAAd,qBAAA,CAAAc,MAAA;IACA;IACA,SAAAJ,2BAAA,SAAAA,2BAAA,CAAAI,MAAA;MACA,KAAAJ,2BAAA,CAAAI,MAAA;IACA;;IAEA;IACA,SAAA3B,UAAA;MACA,KAAAA,UAAA,CAAAmB,OAAA;MACA,KAAAnB,UAAA;IACA;EACA;EACA4B,OAAA;IACA;IACArB,QAAA,WAAAA,SAAA;MACA,IAAAsB,kBAAA,QAAA1B,MAAA,CAAAG,KAAA;QAAAvD,MAAA,GAAA8E,kBAAA,CAAA9E,MAAA;QAAAC,QAAA,GAAA6E,kBAAA,CAAA7E,QAAA;MACA,KAAAD,MAAA;QACA,KAAA+E,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MACA,KAAAjF,MAAA,GAAAA,MAAA;MACA,KAAAC,QAAA,GAAAA,QAAA;;MAEA;MACA,KAAAO,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA,EAAAA,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;;MAEA;MACA,KAAAN,YAAA;MACA,KAAAJ,KAAA;MACA,KAAAY,iBAAA;MACA,KAAAC,iBAAA;MACA,KAAAF,SAAA;;MAEA;MACA,KAAAoE,eAAA;MACA,KAAAC,aAAA;IACA;IACA;IACAF,MAAA,WAAAA,OAAA;MACA,KAAAG,OAAA,CAAAC,IAAA;IACA;IACA;IACAH,eAAA,WAAAA,gBAAA;MAAA,IAAAI,MAAA;MACA;MACA,IAAAC,MAAA,QAAAC,kBAAA,MAAAhF,WAAA;MACA,IAAAiF,sBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,MAAA,CAAA/E,YAAA,GAAAoF,QAAA,CAAAC,IAAA;QACAN,MAAA,CAAAnF,KAAA,GAAAwF,QAAA,CAAAxF,KAAA;MACA,GAAA0F,KAAA;QACAP,MAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAAD,MAAA;MACA,IAAAO,eAAA,OAAAC,cAAA,CAAAC,OAAA,MAAAT,MAAA;;MAEA;MACA,IAAAO,eAAA,CAAAnF,YAAA;QACA,IAAAsF,OAAA;UACA;UACA;UACA;QACA;QACAH,eAAA,CAAAnF,YAAA,GAAAsF,OAAA,CAAAH,eAAA,CAAAnF,YAAA,KAAAmF,eAAA,CAAAnF,YAAA;MACA;;MAEA;MACA,IAAAmF,eAAA,CAAAlF,UAAA;QACA,IAAAsF,aAAA;UACA;UACA;UACA;QACA;QACAJ,eAAA,CAAAlF,UAAA,GAAAsF,aAAA,CAAAJ,eAAA,CAAAlF,UAAA,KAAAkF,eAAA,CAAAlF,UAAA;MACA;;MAEA;MACAuF,MAAA,CAAAC,IAAA,CAAAN,eAAA,EAAAO,OAAA,WAAAC,GAAA;QACA,IAAAR,eAAA,CAAAQ,GAAA,YAAAR,eAAA,CAAAQ,GAAA,cAAAR,eAAA,CAAAQ,GAAA,MAAAC,SAAA;UACA,OAAAT,eAAA,CAAAQ,GAAA;QACA;MACA;MAEA,OAAAR,eAAA;IACA;IACA;IACAX,aAAA,WAAAA,cAAA;MAAA,IAAAqB,MAAA;MACA,IAAAC,+BAAA,OAAAzG,MAAA,EAAA0F,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAAtG,UAAA,GAAAyF,QAAA,CAAA5F,IAAA;MACA,GAAA8F,KAAA;QACA;QACAW,MAAA,CAAAtG,UAAA;UACAC,KAAA;UACAC,YAAA;UACAC,cAAA;UACAC,QAAA;QACA;MACA;IACA;IAGA;IACAoG,sBAAA,WAAAA,uBAAA;MACA,KAAArF,mBAAA;IACA;IACA;IACAsF,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAzF,mBAAA,GAAAyF,IAAA;MACA,KAAAxF,mBAAA;MACA,KAAAF,mBAAA;IACA;IACA;IACA2F,eAAA,WAAAA,gBAAA;MACA,KAAA/F,SAAA,SAAAA,SAAA;MACA,UAAAA,SAAA;QACA,KAAAC,iBAAA;MACA;IACA;IAIA;IACA+F,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAC,QAAA,0CAAAC,MAAA,MAAAhH,QAAA;QACAiH,iBAAA;QACAC,gBAAA;QACAP,IAAA;MACA,GAAAlB,IAAA;QACA,IAAA0B,OAAA,GAAAL,MAAA,CAAAM,QAAA;UACAC,IAAA;UACAC,IAAA;UACAC,OAAA;UACAC,UAAA;QACA;;QAEA;QACA,IAAAC,+BAAA;UACA1H,MAAA,EAAA+G,MAAA,CAAA/G,MAAA;UACAC,QAAA,EAAA8G,MAAA,CAAA9G;QACA,GAAAyF,IAAA,WAAAC,QAAA;UACAyB,OAAA,CAAAO,KAAA;;UAEA;UACA,IAAAC,IAAA,OAAAC,IAAA,EAAAlC,QAAA;YACAiB,IAAA;UACA;UACA,IAAAkB,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;UACA,IAAAM,IAAA,GAAAC,QAAA,CAAAC,aAAA;UACAF,IAAA,CAAAG,IAAA,GAAAP,GAAA;UACAI,IAAA,CAAAI,QAAA,MAAArB,MAAA,CAAAF,MAAA,CAAA9G,QAAA;UACAkI,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,IAAA;UACAA,IAAA,CAAAO,KAAA;UACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,IAAA;UACAH,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAb,GAAA;UAEAf,MAAA,CAAAhC,QAAA,CAAA6D,OAAA,0CAAA3B,MAAA,CAAAF,MAAA,CAAA9G,QAAA;QACA,GAAA4F,KAAA,WAAAb,KAAA;UACAoC,OAAA,CAAAO,KAAA;UACAkB,OAAA,CAAA7D,KAAA,UAAAA,KAAA;UACA+B,MAAA,CAAAhC,QAAA,CAAAC,KAAA;QACA;MACA,GAAAa,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAiD,qBAAA,WAAAA,sBAAA;MACA,KAAA7H,aAAA,SAAAA,aAAA;MACA,SAAAA,aAAA;QACA;QACA,KAAAD,iBAAA,QAAAT,YAAA,CAAAwI,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,UAAA;QAAA;QACA,KAAAlE,QAAA,CAAA6D,OAAA,uBAAA3B,MAAA,MAAAjG,iBAAA,CAAA2C,MAAA;MACA;QACA;QACA,KAAA3C,iBAAA;QACA,KAAA+D,QAAA,CAAA6D,OAAA;MACA;IACA;IAIA;IACAM,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAAnI,iBAAA,CAAA2C,MAAA;QACA,KAAAoB,QAAA,CAAAqE,OAAA;QACA;MACA;MAEA,IAAAC,WAAA,QAAArI,iBAAA,CAAA2C,MAAA;MACA,IAAA2F,cAAA,iDAAArC,MAAA,CAAAoC,WAAA;MAEA,IAAAA,WAAA;QACAC,cAAA;MACA;MAEA,KAAAtC,QAAA,CAAAsC,cAAA;QACApC,iBAAA;QACAC,gBAAA;QACAP,IAAA;QACA2C,wBAAA;MACA,GAAA7D,IAAA;QACAyD,MAAA,CAAAK,kBAAA;MACA,GAAA3D,KAAA;QACAsD,MAAA,CAAApE,QAAA,CAAA0E,IAAA;MACA;IACA;IAEA;IACAD,kBAAA,WAAAA,mBAAA;MAAA,IAAAE,MAAA;MAAA,WAAAC,kBAAA,CAAA3D,OAAA,mBAAA4D,aAAA,CAAA5D,OAAA,IAAA6D,CAAA,UAAAC,QAAA;QAAA,IAAAT,WAAA,EAAAjC,OAAA,EAAA2C,WAAA,EAAAC,SAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,EAAA;QAAA,WAAAR,aAAA,CAAA5D,OAAA,IAAAqE,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAlB,WAAA,GAAAK,MAAA,CAAA1I,iBAAA,CAAA2C,MAAA;cACAyD,OAAA,GAAAsC,MAAA,CAAArC,QAAA;gBACAC,IAAA;gBACAC,IAAA,8BAAAN,MAAA,CAAAoC,WAAA;gBACA7B,OAAA;gBACAC,UAAA;cACA;cAAA6C,QAAA,CAAAE,CAAA;cAGA;cACAT,WAAA,GAAAL,MAAA,CAAA1I,iBAAA,CAAAyJ,IAAA;cACAT,SAAA,GAAAU,IAAA,CAAAC,GAAA;cAAAL,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAAK,qBAAA,EAAAb,WAAA;YAAA;cAAA;cAEAE,OAAA,GAAAS,IAAA,CAAAC,GAAA;cACAT,QAAA,KAAAD,OAAA,GAAAD,SAAA,UAAAa,OAAA;cAEAzD,OAAA,CAAAO,KAAA;cACA+B,MAAA,CAAA3E,QAAA,CAAA6D,OAAA,6BAAA3B,MAAA,CAAAoC,WAAA,wCAAApC,MAAA,CAAAiD,QAAA;;cAEA;cACAR,MAAA,CAAA1I,iBAAA;cACA0I,MAAA,CAAAzI,aAAA;;cAEA;cACAyI,MAAA,CAAAxE,eAAA;cACAwE,MAAA,CAAAvE,aAAA;cAAAmF,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAQ,CAAA;cAGA1D,OAAA,CAAAO,KAAA;cACAkB,OAAA,CAAA7D,KAAA,YAAAoF,EAAA;cAEAD,YAAA;cACA,IAAAC,EAAA,CAAAzE,QAAA,IAAAyE,EAAA,CAAAzE,QAAA,CAAA5F,IAAA,IAAAqK,EAAA,CAAAzE,QAAA,CAAA5F,IAAA,CAAAgL,GAAA;gBACAZ,YAAA,GAAAC,EAAA,CAAAzE,QAAA,CAAA5F,IAAA,CAAAgL,GAAA;cACA,WAAAX,EAAA,CAAAY,OAAA;gBACAb,YAAA,GAAAC,EAAA,CAAAY,OAAA;cACA;cAEAtB,MAAA,CAAA3E,QAAA,CAAAC,KAAA,CAAAmF,YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAW,CAAA;UAAA;QAAA,GAAAnB,OAAA;MAAA;IAEA;IAEA;IACAoB,oBAAA,WAAAA,qBAAAjC,UAAA,EAAAkC,QAAA;MACA,IAAAA,QAAA;QACA,UAAAnK,iBAAA,CAAAoK,QAAA,CAAAnC,UAAA;UACA,KAAAjI,iBAAA,CAAAqK,IAAA,CAAApC,UAAA;QACA;MACA;QACA,IAAAqC,KAAA,QAAAtK,iBAAA,CAAAuK,OAAA,CAAAtC,UAAA;QACA,IAAAqC,KAAA;UACA,KAAAtK,iBAAA,CAAAwK,MAAA,CAAAF,KAAA;QACA;MACA;;MAEA;MACA,KAAArK,aAAA,QAAAD,iBAAA,CAAA2C,MAAA,UAAApD,YAAA,CAAAoD,MAAA;IACA;IACA;IACA8H,kBAAA,WAAAA,mBAAAxC,UAAA;MAAA,IAAAyC,MAAA;MACA,IAAAJ,KAAA,QAAAvK,iBAAA,CAAAwK,OAAA,CAAAtC,UAAA;MACA,IAAAqC,KAAA;QACA;QACA,KAAAvK,iBAAA,CAAAyK,MAAA,CAAAF,KAAA;QACA;QACA,SAAAxK,SAAA;UACA,KAAAA,SAAA;UACA;UACA,KAAAP,YAAA,CAAA8F,OAAA,WAAAsF,QAAA;YACA,IAAAA,QAAA,CAAA1C,UAAA,KAAAA,UAAA,KAAAyC,MAAA,CAAA3K,iBAAA,CAAAqK,QAAA,CAAAO,QAAA,CAAA1C,UAAA;cACAyC,MAAA,CAAA3K,iBAAA,CAAAsK,IAAA,CAAAM,QAAA,CAAA1C,UAAA;YACA;UACA;QACA;MACA;QACA;QACA,KAAAlI,iBAAA,CAAAsK,IAAA,CAAApC,UAAA;MACA;IACA;IACA;IACA2C,kBAAA,WAAAA,mBAAAD,QAAA;MACA,KAAAvK,mBAAA,GAAAuK,QAAA;MACA,KAAAxK,mBAAA,GAAAwK,QAAA,CAAAhL,YAAA;MACA,KAAAO,mBAAA;IACA;IACA;IACA2K,kBAAA,WAAAA,mBAAAF,QAAA;MACA;MACA,IAAAG,cAAA,OAAA/F,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA2F,QAAA;QACA1C,UAAA;QAAA;QACA8C,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;MAAA,EACA;;MAEA;MACA,KAAA9K,mBAAA,GAAA0K,cAAA;MACA,KAAA3K,mBAAA,QAAAgL,2BAAA,CAAAR,QAAA,CAAAhL,YAAA;MACA,KAAAO,mBAAA;IACA;IAEA;IACAiL,2BAAA,WAAAA,4BAAAvF,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA,KAAAA,IAAA;IACA;IACA;IACAwF,oBAAA,WAAAA,qBAAAT,QAAA;MAAA,IAAAU,MAAA;MACA,IAAAxL,eAAA,GAAA8K,QAAA,CAAA9K,eAAA,CAAAyL,OAAA;MACA,IAAAC,cAAA,GAAA1L,eAAA,CAAA8C,MAAA,QAAA9C,eAAA,CAAA2L,SAAA,kBAAA3L,eAAA;MACA,KAAAmG,QAAA,0CAAAC,MAAA,CAAAsF,cAAA;QACArF,iBAAA;QACAC,gBAAA;QACAP,IAAA;MACA,GAAAlB,IAAA;QACA,IAAAkF,qBAAA,EAAAe,QAAA,CAAA1C,UAAA,EAAAvD,IAAA;UACA2G,MAAA,CAAAtH,QAAA,CAAA6D,OAAA;UACAyD,MAAA,CAAAnH,eAAA;UACAmH,MAAA,CAAAlH,aAAA;QACA,GAAAU,KAAA;UACAwG,MAAA,CAAAtH,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA;IACAyH,yBAAA,WAAAA,0BAAA;MACA,KAAAvL,mBAAA;MACA,KAAAgE,eAAA;MACA,KAAAC,aAAA;IACA;IAIA;IACAuH,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,UAAA,QAAAvL,eAAA,SAAAA,eAAA,CAAAuC,IAAA,GAAAF,MAAA;MACA,IAAAmJ,kBAAA,QAAAtL,eAAA,SAAAA,eAAA,CAAAmC,MAAA;MAEA,IAAAkJ,UAAA,IAAAC,kBAAA;QACA,IAAA9B,OAAA;QACA,IAAA8B,kBAAA;UACA9B,OAAA,2CAAA/D,MAAA,MAAAzF,eAAA,CAAAmC,MAAA;QACA;QAEA,KAAAqD,QAAA,CAAAgE,OAAA;UACA9D,iBAAA;UACAC,gBAAA;UACAP,IAAA;QACA,GAAAlB,IAAA;UACA;UACAkH,MAAA,CAAA3I,kBAAA;UACA0I,IAAA;QACA,GAAA9G,KAAA;UACA;QAAA,CACA;MACA;QACA;QACA8G,IAAA;MACA;IACA;IAEA;IACA1I,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAA3C,eAAA;MACA,KAAAC,mBAAA;;MAEA;MACA,KAAAC,eAAA;MACA,KAAAC,WAAA;;MAEA;MACA,KAAAC,WAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,iBAAA;;MAEA;MACA,KAAAI,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,cAAA;;MAEA;MACA,KAAAC,aAAA;QACAC,OAAA;QACAC,cAAA;MACA;IACA;IAEA;IACAyK,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAhL,WAAA;MACA,KAAAC,SAAA;;MAEA;MACA,KAAAiC,SAAA;QACA,IAAA+I,eAAA,GAAAD,MAAA,CAAAE,KAAA,CAAAC,cAAA;QACA,IAAAF,eAAA;UACAA,eAAA,CAAAG,UAAA;QACA;MACA;MAEA,KAAAvL,2BAAA;IAEA;IAEA;IACAwL,eAAA,WAAAA,gBAAA;MACA,KAAAtL,aAAA;MACA,KAAAD,kBAAA;IACA;IAEA;IACAwL,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,YAAA,muDAuBA3J,IAAA;;MAEA;MACA,SAAAZ,UAAA,SAAAC,iBAAA;QACA,KAAAD,UAAA,CAAAwK,OAAA,CAAAD,YAAA;MAEA;QACA;QACA,KAAAtJ,SAAA;UACA,IAAAqJ,MAAA,CAAAtK,UAAA,IAAAsK,MAAA,CAAArK,iBAAA;YACAqK,MAAA,CAAAtK,UAAA,CAAAwK,OAAA,CAAAD,YAAA;UAEA;QACA;MACA;;MAEA;MACA,KAAA1L,kBAAA;;MAEA;MACA,KAAAiD,QAAA,CAAA6D,OAAA;IAGA;IAIA;IACA8E,oBAAA,WAAAA,qBAAA;MACA,KAAApF,QAAA;IACA;IAEA;IACAqF,YAAA,WAAAA,aAAAC,IAAA;MAGA,IAAAC,WAAA,GAAAD,IAAA,CAAAhH,IAAA,kFACAgH,IAAA,CAAAhH,IAAA,4EACAgH,IAAA,CAAAjO,IAAA,CAAAmO,QAAA,aAAAF,IAAA,CAAAjO,IAAA,CAAAmO,QAAA;MACA,IAAAC,OAAA,GAAAH,IAAA,CAAAI,IAAA;MAEA,KAAAH,WAAA;QACA,KAAA9I,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAA+I,OAAA;QACA,KAAAhJ,QAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,KAAAhC,UAAA,CAAAhD,MAAA,QAAAA,MAAA;;MAEA;MACA,KAAAgC,WAAA;MACA,KAAAC,SAAA;MAIA;IACA;IAEA;IACAgM,mBAAA,WAAAA,oBAAAtI,QAAA;MAAA,IAAAuI,OAAA;MACA,IAAAvI,QAAA,CAAAwI,IAAA;QACA;QACA,KAAAnM,WAAA;QACA,KAAAC,SAAA;;QAIA;QACA,KAAAT,eAAA;QACA,KAAAC,WAAA;;QAEA;QACA2M,UAAA;UACAF,OAAA,CAAArM,2BAAA;UACAqM,OAAA,CAAAjM,SAAA;QACA;;QAEA;QACA,KAAAN,oBAAA;;QAEA;QACA,IAAAgE,QAAA,CAAA0I,SAAA,IAAA1I,QAAA,CAAA0I,SAAA,CAAA1K,MAAA;UACA,KAAAnC,eAAA,GAAAmE,QAAA,CAAA0I,SAAA,CAAAtF,GAAA,WAAA4C,QAAA;YAAA,WAAA5F,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA2F,QAAA;cACA2C,SAAA;YAAA;UAAA,CACA;UACA;UACA,KAAA5M,WAAA;UACA,KAAAD,WAAA,GAAAkE,QAAA,CAAA4I,MAAA;;UAEA;UACA,IAAAC,UAAA,GAAA7I,QAAA,CAAA4I,MAAA,GAAA5I,QAAA,CAAA4I,MAAA,CAAA5K,MAAA;UACA,IAAA6K,UAAA;YACA,KAAAzJ,QAAA,CAAA6D,OAAA,mCAAA3B,MAAA,CAAAtB,QAAA,CAAA0I,SAAA,CAAA1K,MAAA,sCAAAsD,MAAA,CAAAuH,UAAA;UACA;YACA,KAAAzJ,QAAA,CAAA6D,OAAA,mCAAA3B,MAAA,CAAAtB,QAAA,CAAA0I,SAAA,CAAA1K,MAAA;UACA;QAGA;UACA,KAAAoB,QAAA,CAAAC,KAAA;UACA,KAAAxD,eAAA;UACA,KAAAC,WAAA,GAAAkE,QAAA,CAAA4I,MAAA;QAGA;;QAEA;QACA,IAAA5I,QAAA,CAAA8I,eAAA;UACA,KAAAC,gBAAA,CAAA/I,QAAA,CAAA8I,eAAA;UACA,KAAAnN,eAAA,GAAAqE,QAAA,CAAA8I,eAAA;UACA,KAAAlN,mBAAA,GAAAoE,QAAA,CAAA8I,eAAA;UACA,KAAA7M,iBAAA,GAAA+D,QAAA,CAAA8I,eAAA;QAEA;;QAEA;QACAL,UAAA;UACAF,OAAA,CAAAvM,oBAAA;QACA;MACA;QAEA,KAAAoD,QAAA,CAAAC,KAAA,CAAAW,QAAA,CAAAoF,GAAA;QACA;QACA,KAAA/I,WAAA;QACA,KAAAC,SAAA;MACA;IACA;IAEA;IACA0M,iBAAA,WAAAA,kBAAA;MACA,KAAA5J,QAAA,CAAAC,KAAA;;MAEA;MACA,KAAAhD,WAAA;MACA,KAAAC,SAAA;IACA;IAIA;IACA2M,cAAA,WAAAA,eAAAtD,KAAA;MACA,IAAAK,QAAA,QAAAnK,eAAA,CAAA8J,KAAA;MACA,KAAAuD,IAAA,CAAAlD,QAAA,gBAAAA,QAAA,CAAA2C,SAAA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAArN,WAAA,SAAAA,WAAA;MACA,KAAAF,eAAA,CAAA6E,OAAA,WAAAsF,QAAA;QACAoD,OAAA,CAAAF,IAAA,CAAAlD,QAAA,gBAAAoD,OAAA,CAAArN,WAAA;MACA;IAEA;IAEA;IACAsN,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,SAAAzN,eAAA,CAAAmC,MAAA;QACA,KAAAoB,QAAA,CAAAqE,OAAA;QACA;MACA;;MAEA;MACA,IAAAE,cAAA,+BAAArC,MAAA,MAAAzF,eAAA,CAAAmC,MAAA;MACA,IAAAuL,cAAA;MAEA,SAAA9M,aAAA,CAAAC,OAAA;QACA6M,cAAA,CAAA7D,IAAA;MACA;MACA,SAAAjJ,aAAA,CAAAE,cAAA;QACA4M,cAAA,CAAA7D,IAAA;MACA;MAEA,IAAA6D,cAAA,CAAAvL,MAAA;QACA2F,cAAA,yCAAArC,MAAA,CAAAiI,cAAA,CAAAzE,IAAA;MACA;MAEA,KAAAzD,QAAA,CAAAsC,cAAA;QACApC,iBAAA;QACAC,gBAAA;QACAP,IAAA;QACA2C,wBAAA;MACA,GAAA7D,IAAA;QACAuJ,OAAA,CAAAE,eAAA;MACA,GAAAtJ,KAAA;IACA;IAEA;IACAsJ,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzF,kBAAA,CAAA3D,OAAA,mBAAA4D,aAAA,CAAA5D,OAAA,IAAA6D,CAAA,UAAAwF,SAAA;QAAA,IAAAC,iBAAA,EAAAC,UAAA,EAAA5J,QAAA,EAAA6J,MAAA,EAAAC,YAAA,EAAAC,SAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,GAAA;QAAA,WAAAlG,aAAA,CAAA5D,OAAA,IAAAqE,CAAA,WAAA0F,SAAA;UAAA,kBAAAA,SAAA,CAAAxF,CAAA;YAAA;cACA6E,OAAA,CAAAlN,kBAAA;cACAkN,OAAA,CAAAjN,cAAA;cAAA4N,SAAA,CAAAvF,CAAA;cAGA;cACA8E,iBAAA,OAAAU,mBAAA,CAAAhK,OAAA,EAAAoJ,OAAA,CAAA5N,eAAA;cAEA,IAAA4N,OAAA,CAAAhN,aAAA,CAAAC,OAAA;gBACAiN,iBAAA,CAAAjN,OAAA;gBACA+M,OAAA,CAAArK,QAAA,CAAA0E,IAAA;cACA;;cAEA;cACA2F,OAAA,CAAAjN,cAAA;;cAEA;cACAoN,UAAA;gBACAvP,MAAA,EAAAoP,OAAA,CAAApP,MAAA;gBACAqO,SAAA,EAAAiB,iBAAA;gBACAhN,cAAA,EAAA8M,OAAA,CAAAhN,aAAA,CAAAE,cAAA;gBACAD,OAAA,EAAA+M,OAAA,CAAAhN,aAAA,CAAAC;cACA;cAEA+M,OAAA,CAAAjN,cAAA;cAAA4N,SAAA,CAAAxF,CAAA;cAAA,OAEA,IAAA0F,8BAAA,EAAAV,UAAA;YAAA;cAAA5J,QAAA,GAAAoK,SAAA,CAAAjF,CAAA;cAEAsE,OAAA,CAAAjN,cAAA;cAAA,MAEAwD,QAAA,CAAAwI,IAAA;gBAAA4B,SAAA,CAAAxF,CAAA;gBAAA;cAAA;cACA6E,OAAA,CAAAjN,cAAA;;cAEA;cACAqN,MAAA,GAAA7J,QAAA,CAAA5F,IAAA;cACA0P,YAAA,GAAAD,MAAA,CAAAC,YAAA;cACAC,SAAA,GAAAF,MAAA,CAAAE,SAAA;cACAC,YAAA,GAAAH,MAAA,CAAAG,YAAA,OAEA;cACAC,aAAA,iDAAA3I,MAAA,CAAAwI,YAAA;cAEA,IAAAC,SAAA;gBACAE,aAAA,0BAAA3I,MAAA,CAAAyI,SAAA;cACA;cAEA,IAAAC,YAAA;gBACAC,aAAA,sCAAA3I,MAAA,CAAA0I,YAAA;cACA;cAEAC,aAAA;;cAEA;cACA,IAAAF,SAAA,QAAAC,YAAA;gBACAP,OAAA,CAAArK,QAAA,CAAAqE,OAAA,CAAAwG,aAAA;cACA;gBACAR,OAAA,CAAArK,QAAA,CAAA6D,OAAA,CAAAgH,aAAA;cACA;;cAEA;cACA,IAAAJ,MAAA,CAAAjB,MAAA,IAAAiB,MAAA,CAAAjB,MAAA,CAAA5K,MAAA;gBACAkF,OAAA,CAAAqH,IAAA,UAAAV,MAAA,CAAAjB,MAAA;;gBAEA;gBACA,IAAAoB,YAAA;kBACAE,aAAA,GAAAL,MAAA,CAAAjB,MAAA,CAAA4B,MAAA,WAAAnL,KAAA;oBAAA,OAAAA,KAAA,CAAAoG,QAAA;kBAAA;kBACA,IAAAyE,aAAA,CAAAlM,MAAA;oBACAkF,OAAA,CAAAY,IAAA,aAAAoG,aAAA;kBACA;gBACA;cACA;cAAAE,SAAA,CAAAxF,CAAA;cAAA;YAAA;cAAA,MAEA,IAAA6F,KAAA,CAAAzK,QAAA,CAAAoF,GAAA;YAAA;cAGA;cACAqE,OAAA,CAAA/N,mBAAA;cACA+N,OAAA,CAAA9N,eAAA;cACA8N,OAAA,CAAA7N,mBAAA;cACA6N,OAAA,CAAA5N,eAAA;cACA4N,OAAA,CAAA3N,WAAA;;cAEA;cACA2N,OAAA,CAAAlK,eAAA;cACAkK,OAAA,CAAAjK,aAAA;cAAA4K,SAAA,CAAAxF,CAAA;cAAA;YAAA;cAAAwF,SAAA,CAAAvF,CAAA;cAAAsF,GAAA,GAAAC,SAAA,CAAAjF,CAAA;cAGAjC,OAAA,CAAA7D,KAAA,YAAA8K,GAAA;cACAV,OAAA,CAAArK,QAAA,CAAAC,KAAA,aAAA8K,GAAA,CAAA9E,OAAA;YAAA;cAAA+E,SAAA,CAAAvF,CAAA;cAEA4E,OAAA,CAAAlN,kBAAA;cACAkN,OAAA,CAAAjN,cAAA;cAAA,OAAA4N,SAAA,CAAAM,CAAA;YAAA;cAAA,OAAAN,SAAA,CAAA9E,CAAA;UAAA;QAAA,GAAAoE,QAAA;MAAA;IAEA;IAEA;IACAiB,cAAA,WAAAA,eAAAC,UAAA;MACA,IAAAA,UAAA;QACA;MACA,WAAAA,UAAA;QACA;MACA,WAAAA,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACApM,cAAA,WAAAA,eAAA;MAAA,IAAAqM,OAAA;MACA,SAAAtN,iBAAA;QACA;MACA;;MAEA;MACA,KAAA6E,MAAA,CAAA0I,QAAA;QACA,KAAAC,kBAAA;QACA;MACA;MAEA;QACA;QACA,SAAAzN,UAAA;UACA,KAAAA,UAAA,CAAAmB,OAAA;UACA,KAAAnB,UAAA;QACA;;QAEA;QACA,IAAA0N,eAAA,GAAAxI,QAAA,CAAAyI,cAAA;QACA,KAAAD,eAAA;UACA;QACA;;QAEA;QACAA,eAAA,CAAAE,SAAA;;QAEA;QACA,KAAA3M,SAAA;UACA;UACA,KAAA6D,MAAA,CAAA0I,QAAA,KAAA1I,MAAA,CAAA0I,QAAA,CAAAnE,OAAA;YAEAkE,OAAA,CAAAM,kBAAA;YACA;UACA;UAEA;YACA;YACAN,OAAA,CAAAvN,UAAA,GAAA8E,MAAA,CAAA0I,QAAA,CAAAnE,OAAA,6BAAAyE,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA;cACAgL,MAAA;cAAA;cACAC,OAAA,GACA;gBAAAtR,IAAA;gBAAAuR,KAAA;cAAA,GACA;gBAAAvR,IAAA;gBAAAuR,KAAA;cAAA,GACA;gBAAAvR,IAAA;gBAAAuR,KAAA;cAAA,GACA;gBAAAvR,IAAA;gBAAAuR,KAAA;cAAA,GACA;gBAAAvR,IAAA;gBAAAuR,KAAA;cAAA,GACA;gBAAAvR,IAAA;gBAAAuR,KAAA;cAAA,GACA;gBAAAvR,IAAA;gBAAAuR,KAAA;cAAA,GACA;gBAAAvR,IAAA;gBAAAuR,KAAA;cAAA,GACA;gBAAAvR,IAAA;gBAAAuR,KAAA;cAAA,EACA;cACAC,aAAA;cACAC,QAAA;cACAC,aAAA;cACAC,cAAA;cACAC,YAAA;cACAC,cAAA;cACA;cACAC,cAAA;cACAC,qBAAA;cACA;cACAC,sBAAA;cACAC,kBAAA;cACA;cACAC,oBAAA,EAAArP,OAAA,CAAAC,GAAA,CAAAC,gBAAA;cACAoP,iBAAA;cACA;cACAC,QAAA;YAAA,wBAEA,uCACA,2BAEA,oCACA;cACAC,aAAA,WAAAA,cAAAC,GAAA;gBACA,IAAAC,MAAA,GAAAD,GAAA,CAAAC,MAAA;gBACAA,MAAA,CAAAC,EAAA,yBAAAF,GAAA;kBACA,IAAAG,MAAA,GAAAH,GAAA,CAAAlS,IAAA;kBACA,IAAAqS,MAAA,CAAAC,OAAA;oBACAjE,UAAA;sBACA,IAAAkE,aAAA,GAAAC,WAAA;wBACA;0BACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;0BACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;4BACAC,aAAA,CAAAN,aAAA;4BACAF,MAAA,CAAAS,UAAA;0BACA;wBACA,SAAAC,CAAA;0BACA;wBAAA;sBAEA;sBACA1E,UAAA;wBAAA,OAAAwE,aAAA,CAAAN,aAAA;sBAAA;oBACA;kBACA;gBACA;cACA;YACA,EACA;UACA,SAAAtN,KAAA;YACAwL,OAAA,CAAAE,kBAAA;YACA;UACA;;UAEA;UACA,IAAAF,OAAA,CAAAvN,UAAA,IAAAuN,OAAA,CAAAvN,UAAA,CAAAkP,EAAA;YACA3B,OAAA,CAAAvN,UAAA,CAAAkP,EAAA;cACA3B,OAAA,CAAAhM,2BAAA;YACA;YAEAgM,OAAA,CAAAvN,UAAA,CAAAkP,EAAA;cACA3B,OAAA,CAAAhM,2BAAA;YACA;YAEAgM,OAAA,CAAAvN,UAAA,CAAAkP,EAAA;cACA3B,OAAA,CAAAtN,iBAAA;cACAsN,OAAA,CAAAvN,UAAA,CAAAwK,OAAA;YACA;UACA;QACA;MAEA,SAAAzI,KAAA;QACA,KAAA0L,kBAAA;MACA;IACA;IAEA;IACAjM,kCAAA,WAAAA,mCAAA;MACA,UAAAxB,UAAA,UAAAC,iBAAA;QACA;MACA;MAEA;QACA,IAAA6P,UAAA,QAAA9P,UAAA,CAAA+P,OAAA;QACA,IAAAC,uBAAA,QAAAC,qBAAA,CAAAH,UAAA;QACA,KAAAxR,mBAAA,QAAA4R,0BAAA,CAAAF,uBAAA;QACA,KAAA3R,eAAA,QAAAsC,uBAAA,CAAAqP,uBAAA;MACA,SAAAjO,KAAA;QACA6D,OAAA,CAAAqH,IAAA,eAAAlL,KAAA;MACA;IACA;IAEA;IACA0L,kBAAA,WAAAA,mBAAA;MAAA,IAAA0C,OAAA;MACA,IAAAzC,eAAA,GAAAxI,QAAA,CAAAyI,cAAA;MACA,IAAAD,eAAA;QACA,IAAA0C,QAAA,GAAAlL,QAAA,CAAAC,aAAA;QACAiL,QAAA,CAAAC,SAAA;QACAD,QAAA,CAAAE,WAAA;QACAF,QAAA,CAAAG,KAAA;QACAH,QAAA,CAAAI,KAAA,CAAAC,OAAA;;QAEA;QACAL,QAAA,CAAAM,gBAAA,oBAAAb,CAAA;UACA;UACAM,OAAA,CAAA9R,eAAA,GAAAwR,CAAA,CAAAc,MAAA,CAAAJ,KAAA;UACAJ,OAAA,CAAA7R,mBAAA,GAAAuR,CAAA,CAAAc,MAAA,CAAAJ,KAAA;QACA;QAEA7C,eAAA,CAAAE,SAAA;QACAF,eAAA,CAAAnI,WAAA,CAAA6K,QAAA;QACA,KAAAnQ,iBAAA;MACA;IACA;IAIA;IACAwL,gBAAA,WAAAA,iBAAAmF,OAAA;MACA,SAAA5Q,UAAA,SAAAC,iBAAA;QACA,KAAAD,UAAA,CAAAwK,OAAA,CAAAoG,OAAA;MACA;QACA,KAAAvS,eAAA,GAAAuS,OAAA;QACA,KAAAtS,mBAAA,GAAAsS,OAAA;MACA;IACA;IAIA;IACAvP,QAAA,WAAAA,SAAAwP,IAAA,EAAAC,IAAA;MACA,IAAAC,OAAA;MACA,IAAAC,SAAA,YAAAC,iBAAA;QAAA,IAAAC,OAAA;QAAA,SAAAC,IAAA,GAAAC,SAAA,CAAA1Q,MAAA,EAAA2Q,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;UAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;QAAA;QACA,IAAAC,KAAA,YAAAA,MAAA;UACAC,YAAA,CAAAV,OAAA;UACAA,OAAA;UACAF,IAAA,CAAAa,KAAA,CAAAR,OAAA,EAAAG,IAAA;QACA;QACAI,YAAA,CAAAV,OAAA;QACAA,OAAA,GAAA5F,UAAA,CAAAqG,KAAA,EAAAV,IAAA;MACA;;MAEA;MACAE,SAAA,CAAArP,MAAA;QACA8P,YAAA,CAAAV,OAAA;QACAA,OAAA;MACA;MAEA,OAAAC,SAAA;IACA;IAEA;IACAf,qBAAA,WAAAA,sBAAAW,OAAA;MACA,KAAAA,OAAA,SAAAA,OAAA;;MAEA;MACA,IAAAe,aAAA,GAAA7M,MAAA,CAAA8M,QAAA,CAAAC,MAAA;MACA,IAAAC,QAAA,OAAAC,MAAA,CAAAJ,aAAA,CAAAtI,OAAA;MAEA,OAAAuH,OAAA,CAAAvH,OAAA,CAAAyI,QAAA;IACA;IAEA;IACAxQ,aAAA,WAAAA,cAAA;MACA,UAAAjD,eAAA,CAAAuC,IAAA;QACA,KAAArC,eAAA;QACA,KAAAC,WAAA;QACA;MACA;MAEA;QACA,IAAAwT,WAAA,QAAAC,oBAAA,MAAA5T,eAAA;QACA,KAAAE,eAAA,GAAAyT,WAAA,CAAA5G,SAAA,CAAAtF,GAAA,WAAA4C,QAAA;UAAA,WAAA5F,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA2F,QAAA;YACA2C,SAAA;UAAA;QAAA,CACA;QACA,KAAA7M,WAAA,GAAAwT,WAAA,CAAA1G,MAAA;QACA;QACA,KAAA3M,iBAAA,QAAAN,eAAA;MACA,SAAA0D,KAAA;QACA,KAAAvD,WAAA,cAAAuD,KAAA,CAAAgG,OAAA;QACA,KAAAxJ,eAAA;MACA;IACA;IAEA;IACA0T,oBAAA,WAAAA,qBAAArB,OAAA;MACA,IAAAxF,SAAA;MACA,IAAAE,MAAA;MAEA,KAAAsF,OAAA,WAAAA,OAAA;QAEA;UAAAxF,SAAA,EAAAA,SAAA;UAAAE,MAAA;QAAA;MACA;MAEA;QAGA,IAAA4G,WAAA,QAAAvR,uBAAA,CAAAiQ,OAAA;QAEA,KAAAsB,WAAA,IAAAA,WAAA,CAAAtR,IAAA,GAAAF,MAAA;UACA;YAAA0K,SAAA,EAAAA,SAAA;YAAAE,MAAA;UAAA;QACA;QAEA,IAAA6G,KAAA,GAAAD,WAAA,CAAAE,KAAA,OAAAtM,GAAA,WAAAuM,IAAA;UAAA,OAAAA,IAAA,CAAAzR,IAAA;QAAA,GAAAsM,MAAA,WAAAmF,IAAA;UAAA,OAAAA,IAAA,CAAA3R,MAAA;QAAA;QAEA,IAAAyR,KAAA,CAAAzR,MAAA;UACA;YAAA0K,SAAA,EAAAA,SAAA;YAAAE,MAAA;UAAA;QACA;QAIA,IAAAgH,oBAAA;QACA,IAAAC,cAAA;QAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAAzR,MAAA,EAAA8R,CAAA;UACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;;UAEA;UACA,IAAAC,eAAA,QAAAC,mBAAA,CAAAL,IAAA,UAAAM,mBAAA,CAAAN,IAAA;UAEA,IAAAI,eAAA;YACA;YACA,IAAAH,oBAAA,CAAA5R,MAAA;cACA;gBACA,IAAAkS,YAAA,GAAAN,oBAAA,CAAA9K,IAAA;gBACA,IAAAqL,cAAA,QAAAC,sBAAA,CAAAF,YAAA,EAAAL,cAAA;gBACA,IAAAM,cAAA;kBACAzH,SAAA,CAAAhD,IAAA,CAAAyK,cAAA;gBACA;cACA,SAAA9Q,KAAA;gBACAuJ,MAAA,CAAAlD,IAAA,WAAApE,MAAA,CAAAuO,cAAA,uCAAAvO,MAAA,CAAAjC,KAAA,CAAAgG,OAAA;cACA;YACA;;YAEA;YACAuK,oBAAA,IAAAD,IAAA;YACAE,cAAA;UACA;YACA;YACA,IAAAD,oBAAA,CAAA5R,MAAA;cACA4R,oBAAA,CAAAlK,IAAA,CAAAiK,IAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAC,oBAAA,CAAA5R,MAAA;UACA;YACA,IAAAkS,aAAA,GAAAN,oBAAA,CAAA9K,IAAA;YACA,IAAAqL,eAAA,QAAAC,sBAAA,CAAAF,aAAA,EAAAL,cAAA;YACA,IAAAM,eAAA;cACAzH,SAAA,CAAAhD,IAAA,CAAAyK,eAAA;YACA;UACA,SAAA9Q,KAAA;YACAuJ,MAAA,CAAAlD,IAAA,WAAApE,MAAA,CAAAuO,cAAA,uCAAAvO,MAAA,CAAAjC,KAAA,CAAAgG,OAAA;UACA;QACA;MAEA,SAAAhG,KAAA;QACAuJ,MAAA,CAAAlD,IAAA,0CAAApE,MAAA,CAAAjC,KAAA,CAAAgG,OAAA;MACA;MAEA;QAAAqD,SAAA,EAAAA,SAAA;QAAAE,MAAA,EAAAA;MAAA;IACA;IAEA;IACAoH,mBAAA,WAAAA,oBAAAL,IAAA;MACA;MACA;MACA;MACA,wBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAM,mBAAA,WAAAA,oBAAAN,IAAA;MACA;MACA;MACA,mBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAS,sBAAA,WAAAA,uBAAAF,YAAA;MACA,IAAAT,KAAA,GAAAS,YAAA,CAAAR,KAAA,OAAAtM,GAAA,WAAAuM,IAAA;QAAA,OAAAA,IAAA,CAAAzR,IAAA;MAAA,GAAAsM,MAAA,WAAAmF,IAAA;QAAA,OAAAA,IAAA,CAAA3R,MAAA;MAAA;MAEA,IAAAyR,KAAA,CAAAzR,MAAA;QACA,UAAAyM,KAAA;MACA;MAEA,IAAAzP,YAAA;MACA,IAAAE,eAAA;MACA,IAAAoV,iBAAA;;MAEA;MACA,SAAAR,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAAzR,MAAA,EAAA8R,CAAA;QACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;QACA,IAAAS,SAAA,GAAAZ,IAAA,CAAAa,KAAA;QACA,IAAAD,SAAA;UACA,IAAAE,QAAA,GAAAF,SAAA;;UAEA;UACA,IAAAE,QAAA,CAAAhL,QAAA;YACAzK,YAAA;UACA,WAAAyV,QAAA,CAAAhL,QAAA;YACAzK,YAAA;UACA,WAAAyV,QAAA,CAAAhL,QAAA;YACAzK,YAAA;UACA,WAAAyV,QAAA,CAAAhL,QAAA;YACAzK,YAAA;UACA,WAAAyV,QAAA,CAAAhL,QAAA;YACAzK,YAAA;UACA;;UAEA;UACA,IAAA0V,gBAAA,GAAAf,IAAA,CAAAhJ,OAAA,iBAAAzI,IAAA;UACA,IAAAwS,gBAAA;YACAxV,eAAA,GAAAwV,gBAAA;YACAJ,iBAAA,GAAAR,CAAA;UACA;YACAQ,iBAAA,GAAAR,CAAA;UACA;UACA;QACA;MACA;;MAEA;MACA,IAAAQ,iBAAA;QACAA,iBAAA;MACA;;MAEA;MACA,SAAAR,EAAA,GAAAQ,iBAAA,EAAAR,EAAA,GAAAL,KAAA,CAAAzR,MAAA,EAAA8R,EAAA;QACA,IAAAH,KAAA,GAAAF,KAAA,CAAAK,EAAA;;QAEA;QACA,SAAAE,mBAAA,CAAAL,KAAA;UACA;UACAzU,eAAA,GAAAyU,KAAA,CAAAhJ,OAAA,uBAAAzI,IAAA;UACAoS,iBAAA,GAAAR,EAAA;UACA;QACA,YAAA5U,eAAA;UACA;UACAA,eAAA,GAAAyU,KAAA;UACAW,iBAAA,GAAAR,EAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAA,GAAA,GAAAQ,iBAAA,EAAAR,GAAA,GAAAL,KAAA,CAAAzR,MAAA,EAAA8R,GAAA;QACA,IAAAH,MAAA,GAAAF,KAAA,CAAAK,GAAA;;QAEA;QACA,SAAAa,YAAA,CAAAhB,MAAA,UAAAiB,YAAA,CAAAjB,MAAA,KACA,KAAAkB,iBAAA,CAAAlB,MAAA,UAAAmB,gBAAA,CAAAnB,MAAA;UACA;QACA;;QAEA;QACA,IAAAoB,SAAA,GAAApB,MAAA;QACA;QACA,SAAAK,mBAAA,CAAAL,MAAA;UACAoB,SAAA,GAAApB,MAAA,CAAAhJ,OAAA,uBAAAzI,IAAA;QACA;QAEA,IAAA6S,SAAA;UACA,IAAA7V,eAAA;YACAA,eAAA,WAAA6V,SAAA;UACA;YACA7V,eAAA,GAAA6V,SAAA;UACA;QACA;MACA;MAEA,KAAA7V,eAAA;QACA,UAAAuP,KAAA;MACA;;MAEA;MACA,IAAAuG,oBAAA,GAAA9V,eAAA,CAAAgD,IAAA;MACA;MACA,wBAAAmS,IAAA,CAAAW,oBAAA;QACAA,oBAAA,GAAAA,oBAAA,CAAArK,OAAA,0BAAAzI,IAAA;MACA;;MAEA;MACA,IAAA8S,oBAAA,CAAAvL,QAAA;QACAuL,oBAAA,QAAAC,oBAAA,CAAAD,oBAAA;MACA;MAEA,IAAAhL,QAAA;QACAhL,YAAA,EAAAA,YAAA;QACAiG,IAAA,EAAAjG,YAAA;QACAkW,QAAA,OAAAC,kBAAA,CAAAnW,YAAA;QACAE,eAAA,EAAA8V,oBAAA;QACA9C,OAAA,EAAA8C,oBAAA;QACA/V,UAAA;QAAA;QACAmW,WAAA;QACAC,OAAA;QACAC,aAAA;QACA3I,SAAA;MACA;;MAEA;MACA,IAAA4I,YAAA,QAAAC,qBAAA,CAAA/B,KAAA;MACAzJ,QAAA,CAAAqL,OAAA,GAAAE,YAAA,CAAAF,OAAA;;MAEA;MACA,IAAArW,YAAA,mBAAAgL,QAAA,CAAAqL,OAAA,CAAArT,MAAA;QACA;QACAhD,YAAA;QACAgL,QAAA,CAAAhL,YAAA,GAAAA,YAAA;QACAgL,QAAA,CAAA/E,IAAA,GAAAjG,YAAA;QACAgL,QAAA,CAAAkL,QAAA,QAAAC,kBAAA,CAAAnW,YAAA;MACA;;MAEA;MACA,KAAAyW,0BAAA,CAAAhC,KAAA,EAAAzJ,QAAA;;MAEA;MACA,IAAAhL,YAAA,iBAAAgL,QAAA,CAAAsL,aAAA,IAAAtL,QAAA,CAAAsL,aAAA,CAAAtT,MAAA;QACA;QACA,kBAAAqS,IAAA,CAAArK,QAAA,CAAAsL,aAAA;UACAtW,YAAA;UACAgL,QAAA,CAAAhL,YAAA,GAAAA,YAAA;UACAgL,QAAA,CAAA/E,IAAA,GAAAjG,YAAA;UACAgL,QAAA,CAAAkL,QAAA,QAAAC,kBAAA,CAAAnW,YAAA;QACA;MACA;;MAEA;MACAgL,QAAA,CAAA9K,eAAA,QAAA+V,oBAAA,CAAAjL,QAAA,CAAA9K,eAAA;MACA8K,QAAA,CAAA9K,eAAA,QAAAwW,kBAAA,CAAA1L,QAAA,CAAA9K,eAAA;MACA8K,QAAA,CAAAkI,OAAA,GAAAlI,QAAA,CAAA9K,eAAA;MAEA,OAAA8K,QAAA;IACA;IAEA;IACA2K,YAAA,WAAAA,aAAAhB,IAAA;MACA;MACA;MACA,KAAAA,IAAA,IAAAA,IAAA,CAAA3R,MAAA;QACA;MACA;MAEA,IAAAwS,KAAA,GAAAb,IAAA,CAAAa,KAAA;MACA,IAAAA,KAAA;QACA,IAAAmB,SAAA,GAAAnB,KAAA,IAAAoB,WAAA;QACA,IAAAC,aAAA,GAAArB,KAAA,MAAAA,KAAA,IAAAtS,IAAA;;QAEA;QACA;QACA;QACA;QACA,cAAAmS,IAAA,CAAAsB,SAAA,KAAAE,aAAA,CAAA7T,MAAA,QAAA6T,aAAA,CAAA7T,MAAA;UACA;UACA,IAAA8T,eAAA,IACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;UAAA,CACA;UAEA,IAAAC,iBAAA,GAAAD,eAAA,CAAAE,IAAA,WAAAC,OAAA;YAAA,OAAAA,OAAA,CAAA5B,IAAA,CAAAwB,aAAA;UAAA;UACA,QAAAE,iBAAA;QACA;MACA;MACA;IACA;IAEA;IACAnB,YAAA,WAAAA,aAAAjB,IAAA;MACA;MACA,sBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAkB,iBAAA,WAAAA,kBAAAlB,IAAA;MACA;MACA,sBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAmB,gBAAA,WAAAA,iBAAAnB,IAAA;MACA;MACA,sBAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAwB,kBAAA,WAAAA,mBAAAlQ,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA;IACA;IAEA;IACAiR,iBAAA,WAAAA,kBAAAhE,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA,IAAAiE,gBAAA,GAAAjE,OAAA,CAAAvH,OAAA,mDAAA6J,KAAA,EAAA4B,MAAA,EAAAC,GAAA,EAAAC,KAAA;UACA,KAAAD,GAAA,SAAA7B,KAAA;UAEA,IAAA6B,GAAA,CAAArF,UAAA,eAAAqF,GAAA,CAAArF,UAAA,gBAAAqF,GAAA,CAAArF,UAAA;YACA,OAAAwD,KAAA;UACA;UAEA,IAAA+B,OAAA,8BAAAF,GAAA,CAAArF,UAAA,QAAAqF,GAAA,SAAAA,GAAA;UACA,cAAA/Q,MAAA,CAAA8Q,MAAA,YAAA9Q,MAAA,CAAAiR,OAAA,QAAAjR,MAAA,CAAAgR,KAAA;QACA;QAEA,OAAAH,gBAAA;MACA,SAAA9S,KAAA;QACA,OAAA6O,OAAA;MACA;IACA;IAEA;IACAV,0BAAA,WAAAA,2BAAAU,OAAA;MAAA,IAAAsE,OAAA;MACA,KAAAtE,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA;QACA,IAAAiE,gBAAA,GAAAjE;QACA;QAAA,CACAvH,OAAA,oDAAA6J,KAAA,EAAA4B,MAAA,EAAAC,GAAA,EAAAC,KAAA;UACA,KAAAD,GAAA,CAAArF,UAAA,aAAAqF,GAAA,CAAArF,UAAA;YACA,IAAAuF,OAAA,GAAAC,OAAA,CAAAN,iBAAA,CAAAG,GAAA;YACA,cAAA/Q,MAAA,CAAA8Q,MAAA,YAAA9Q,MAAA,CAAAiR,OAAA,QAAAjR,MAAA,CAAAgR,KAAA;UACA;UACA,OAAA9B,KAAA;QACA;QACA;QAAA,CACA7J,OAAA,sBACAA,OAAA;QACA;QAAA,CACAA,OAAA;QACA;QAAA,CACAA,OAAA,sBACAA,OAAA;QAEA,OAAAwL,gBAAA,CAAAjU,IAAA;MACA,SAAAmB,KAAA;QACA,OAAA6O,OAAA;MACA;IACA;IAEA;IACAjQ,uBAAA,WAAAA,wBAAAiQ,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA,IAAAuE,MAAA;QACA,IAAAC,UAAA;QACA,IAAAC,uBAAA,GAAAzE,OAAA,CAAAvH,OAAA,2BAAA6J,KAAA;UACAiC,MAAA,CAAA/M,IAAA,CAAA8K,KAAA;UACA,gCAAAlP,MAAA,CAAAoR,UAAA;QACA;QAEA,IAAAlD,WAAA,GAAAmD,uBAAA,CACAhM,OAAA,uBACAA,OAAA,kBACAA,OAAA,qBACAA,OAAA,iBACAA,OAAA;QACA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;QAAA,CACAA,OAAA;;QAEA,IAAAiM,YAAA,GAAApD,WAAA;QACAiD,MAAA,CAAA/R,OAAA,WAAAmS,GAAA,EAAAlN,KAAA;UACA,IAAAiI,WAAA,0BAAAtM,MAAA,CAAAqE,KAAA;UACA,IAAAiN,YAAA,CAAAnN,QAAA,CAAAmI,WAAA;YACAgF,YAAA,GAAAA,YAAA,CAAAjM,OAAA,CAAAiH,WAAA,EAAAiF,GAAA;UACA;QACA;QAEA,OAAAD,YAAA,CAAA1U,IAAA;MACA,SAAAmB,KAAA;QACA,OAAA6O,OAAA;MACA;IACA;IAEA;IACAsD,qBAAA,WAAAA,sBAAA/B,KAAA,EAAAqD,UAAA;MACA,IAAAzB,OAAA;MAEA,KAAAzC,KAAA,CAAAmE,OAAA,CAAAtD,KAAA,KAAAqD,UAAA,QAAAA,UAAA,IAAArD,KAAA,CAAAzR,MAAA;QACA;UAAAqT,OAAA,EAAAA;QAAA;MACA;MAEA;QACA,SAAAvB,CAAA,GAAAgD,UAAA,EAAAhD,CAAA,GAAAL,KAAA,CAAAzR,MAAA,EAAA8R,CAAA;UACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;UAEA,KAAAH,IAAA,WAAAA,IAAA;YACA;UACA;;UAEA;UACA,SAAAgB,YAAA,CAAAhB,IAAA;YACA,IAAAqD,WAAA,GAAArD,IAAA,CAAAa,KAAA;YACA,IAAAwC,WAAA;cACA,IAAArB,SAAA,GAAAqB,WAAA,IAAApB,WAAA;cACA,IAAAC,aAAA,GAAAmB,WAAA,MAAAA,WAAA,IAAA9U,IAAA;cAEA,IAAAyT,SAAA,IAAAE,aAAA;gBACAR,OAAA,CAAA3L,IAAA;kBACAiM,SAAA,EAAAA,SAAA;kBACAsB,KAAA,EAAAtB,SAAA;kBACAE,aAAA,EAAAA,aAAA;kBACA3D,OAAA,EAAA2D;gBACA;cACA;YACA;UACA,gBAAAjB,YAAA,CAAAjB,IAAA,UAAAkB,iBAAA,CAAAlB,IAAA,UAAAmB,gBAAA,CAAAnB,IAAA;YACA;YACA;UACA;YACA;YACA;YACA;YACA;YACA,IAAAA,IAAA,CAAA3R,MAAA,2BAAAqS,IAAA,CAAAV,IAAA;cACA,IAAAuD,oBAAA,GAAAvD,IAAA,CAAAa,KAAA;cACA,IAAA0C,oBAAA;gBACA;gBACA,IAAAC,aAAA,GAAAxD,IAAA,CAAAD,KAAA;gBAAA,IAAA0D,SAAA,OAAAC,2BAAA,CAAAhT,OAAA,EACA8S,aAAA;kBAAAG,KAAA;gBAAA;kBAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAxO,CAAA,IAAAoC,IAAA;oBAAA,IAAAwM,YAAA,GAAAF,KAAA,CAAAzF,KAAA;oBACA,KAAA2F,YAAA;;oBAEA;oBACA,SAAA7C,YAAA,CAAA6C,YAAA;sBACA,IAAAhD,KAAA,GAAAgD,YAAA,CAAAhD,KAAA;sBACA,IAAAA,KAAA;wBACA,IAAAmB,UAAA,GAAAnB,KAAA,IAAAoB,WAAA;wBACA,IAAAC,cAAA,GAAArB,KAAA,MAAAA,KAAA,IAAAtS,IAAA;wBAEA,IAAAyT,UAAA,IAAAE,cAAA;0BACAR,OAAA,CAAA3L,IAAA;4BACAiM,SAAA,EAAAA,UAAA;4BACAsB,KAAA,EAAAtB,UAAA;4BACAE,aAAA,EAAAA,cAAA;4BACA3D,OAAA,EAAA2D;0BACA;wBACA;sBACA;oBACA;kBACA;gBAAA,SAAA4B,GAAA;kBAAAL,SAAA,CAAAjG,CAAA,CAAAsG,GAAA;gBAAA;kBAAAL,SAAA,CAAA1I,CAAA;gBAAA;cACA;YACA;UACA;QACA;MACA,SAAArL,KAAA;QACA;MAAA;MAGA;QAAAgS,OAAA,EAAAA;MAAA;IACA;IAEA;IACAI,0BAAA,WAAAA,2BAAAhC,KAAA,EAAAzJ,QAAA;MACA,SAAA8J,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAAzR,MAAA,EAAA8R,CAAA;QACA,IAAAH,IAAA,GAAAF,KAAA,CAAAK,CAAA;;QAEA;QACA,IAAA4D,WAAA,GAAA/D,IAAA,CAAAa,KAAA;QACA,IAAAkD,WAAA;UACA1N,QAAA,CAAAsL,aAAA,QAAAqC,gBAAA,CAAAD,WAAA,KAAA1N,QAAA,CAAAhL,YAAA;UACA;QACA;;QAEA;QACA,IAAA4Y,gBAAA,GAAAjE,IAAA,CAAAa,KAAA;QACA,IAAAoD,gBAAA;UACA5N,QAAA,CAAAoL,WAAA,GAAAwC,gBAAA,IAAA1V,IAAA;UACA;QACA;;QAEA;QACA,IAAA2V,eAAA,GAAAlE,IAAA,CAAAa,KAAA;QACA,IAAAqD,eAAA;UACA,IAAA5Y,UAAA,GAAA4Y,eAAA;UACA;UACA,IAAA5Y,UAAA;YACAA,UAAA;UACA;UACA;UACA,uBAAAwK,QAAA,CAAAxK,UAAA;YACA+K,QAAA,CAAA/K,UAAA,GAAAA,UAAA;UACA;UACA;QACA;MACA;;MAEA;MACA;MACA,KAAA+K,QAAA,CAAAsL,aAAA;QACAtL,QAAA,CAAAsL,aAAA,QAAAwC,gCAAA,CAAA9N,QAAA,CAAA9K,eAAA,EAAA8K,QAAA,CAAAhL,YAAA;MACA;IACA;IAEA;IACA8Y,gCAAA,WAAAA,iCAAA5Y,eAAA,EAAAF,YAAA;MACA,KAAAE,eAAA,WAAAA,eAAA;QACA;MACA;MAEA;QACA;QACA,IAAA6Y,QAAA,IACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA,CACA;QAEA,SAAAC,GAAA,MAAAC,SAAA,GAAAF,QAAA,EAAAC,GAAA,GAAAC,SAAA,CAAAjW,MAAA,EAAAgW,GAAA;UAAA,IAAA/B,OAAA,GAAAgC,SAAA,CAAAD,GAAA;UACA,IAAAE,OAAA,GAAAhZ,eAAA,CAAAsV,KAAA,CAAAyB,OAAA;UACA,IAAAiC,OAAA,IAAAA,OAAA,CAAAlW,MAAA;YACA;YACA,IAAAmW,SAAA,GAAAD,OAAA,CAAAA,OAAA,CAAAlW,MAAA;YACA,IAAAoW,MAAA,GAAAD,SAAA,CAAAxN,OAAA,sBAAAzI,IAAA;YAEA,IAAAkW,MAAA;cACA,YAAAT,gBAAA,CAAAS,MAAA,EAAApZ,YAAA;YACA;UACA;QACA;MACA,SAAAqE,KAAA;QACA;MAAA;MAGA;IACA;IAEA;IACAsU,gBAAA,WAAAA,iBAAAU,UAAA,EAAArZ,YAAA;MACA,KAAAqZ,UAAA,WAAAA,UAAA;QACA;MACA;MAEA;QACA,IAAAC,aAAA,GAAAD,UAAA,CAAAnW,IAAA;QAEA,KAAAoW,aAAA;UACA;QACA;QAEA,IAAAtZ,YAAA;UACA;UACA,OAAAsZ,aAAA;QACA;UACA;UACA,OAAAA,aAAA,CAAA1C,WAAA;QACA;MACA,SAAAvS,KAAA;QACA,OAAAgV,UAAA;MACA;IACA;IAMA;IACAE,2BAAA,WAAAA,4BAAAvO,QAAA;MACA,KAAAA,QAAA,KAAAA,QAAA,CAAA9K,eAAA;QACA;MACA;MAEA,IAAAgT,OAAA,GAAAlI,QAAA,CAAA9K,eAAA;;MAEA;MACA,SAAAU,mBAAA,SAAAA,mBAAA,CAAA6J,QAAA;QACA;QACA,IAAA+O,WAAA,QAAAC,uBAAA,CAAAzO,QAAA,CAAA9K,eAAA,OAAAU,mBAAA;QACA,IAAA4Y,WAAA;UACAtG,OAAA,GAAAsG,WAAA;QACA;MACA;;MAEA;MACAtG,OAAA,QAAA+C,oBAAA,CAAA/C,OAAA;;MAEA;MACAA,OAAA,QAAAwD,kBAAA,CAAAxD,OAAA;MAEA,YAAAgE,iBAAA,CAAAhE,OAAA;IACA;IAEA;IACAwG,mBAAA,WAAAA,oBAAAzT,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA;IACA;IAEA;IACAgQ,oBAAA,WAAAA,qBAAA/C,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA,OAAAA,OAAA;MACA;;MAEA;MACA,IAAAA,OAAA,CAAAzI,QAAA;QACA;QACA,OAAAyI,OAAA,CAAAvH,OAAA,wDACAA,OAAA;QAAA,CACAA,OAAA;MACA;QACA;QACA,OAAAuH,OAAA,CAAAvH,OAAA,0BAAAzI,IAAA;MACA;IACA;IAEA;IACAwT,kBAAA,WAAAA,mBAAAxD,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA,OAAAA,OAAA;MACA;;MAEA;MACA,IAAAA,OAAA,CAAAzI,QAAA;QACA;QACA,OAAAyI,OAAA,CAAAvH,OAAA,sDACAA,OAAA;QAAA,CACAA,OAAA;MACA;QACA;QACA,OAAAuH,OAAA,CAAAvH,OAAA,wBAAAzI,IAAA;MACA;IACA;IAEA;IACAuW,uBAAA,WAAAA,wBAAAE,YAAA,EAAAH,WAAA;MACA,KAAAG,YAAA,KAAAH,WAAA;QACA,OAAAG,YAAA;MACA;MAEA;QACA;QACA,IAAAC,SAAA,GAAAD,YAAA,CAAAhO,OAAA,uBAAAzI,IAAA;;QAEA;QACA,IAAA2W,UAAA,GAAAL,WAAA,CAAAhE,KAAA;QAAA,IAAAsE,UAAA,OAAAzB,2BAAA,CAAAhT,OAAA,EAEAwU,UAAA;UAAAE,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAvB,CAAA,MAAAwB,MAAA,GAAAD,UAAA,CAAAlQ,CAAA,IAAAoC,IAAA;YAAA,IAAAgO,SAAA,GAAAD,MAAA,CAAAlH,KAAA;YACA,IAAAoH,aAAA,GAAAD,SAAA,CAAArO,OAAA,iBAAAzI,IAAA;YACA;YACA,IAAAgX,kBAAA,GAAAD,aAAA,CAAAtO,OAAA,0BAAAzI,IAAA;YACA,IAAAgX,kBAAA,CAAAzP,QAAA,CAAAmP,SAAA,CAAA/N,SAAA;cACA;cACA,YAAAoK,oBAAA,CAAA+D,SAAA;YACA;UACA;QAAA,SAAAvB,GAAA;UAAAqB,UAAA,CAAA3H,CAAA,CAAAsG,GAAA;QAAA;UAAAqB,UAAA,CAAApK,CAAA;QAAA;QAEA,OAAAiK,YAAA;MACA,SAAAtV,KAAA;QACA,OAAAsV,YAAA;MACA;IACA;IAGA;IACAQ,YAAA,WAAAA,aAAA;MACA,KAAAta,WAAA,CAAAC,OAAA;MACA,KAAAyE,eAAA;IACA;IACA;IACA6V,WAAA,WAAAA,YAAA;MACA,KAAAva,WAAA,CAAAG,YAAA;MACA,KAAAH,WAAA,CAAAI,UAAA;MACA,KAAAJ,WAAA,CAAAK,eAAA;MACA,KAAAL,WAAA,CAAAC,OAAA;MACA,KAAAyE,eAAA;IACA;EACA;AACA", "ignoreList": []}]}