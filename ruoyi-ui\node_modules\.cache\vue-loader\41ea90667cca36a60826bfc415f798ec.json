{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBRdWVzdGlvbkNhcmQgZnJvbSAnLi9jb21wb25lbnRzL1F1ZXN0aW9uQ2FyZCcKaW1wb3J0IFF1ZXN0aW9uRm9ybSBmcm9tICcuL2NvbXBvbmVudHMvUXVlc3Rpb25Gb3JtJwppbXBvcnQgeyBsaXN0UXVlc3Rpb24sIGRlbFF1ZXN0aW9uLCBnZXRRdWVzdGlvblN0YXRpc3RpY3MsIGJhdGNoSW1wb3J0UXVlc3Rpb25zLCBleHBvcnRRdWVzdGlvbnNUb1dvcmQgfSBmcm9tICdAL2FwaS9iaXovcXVlc3Rpb24nCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlF1ZXN0aW9uQmFua0RldGFpbCIsCiAgY29tcG9uZW50czogewogICAgUXVlc3Rpb25DYXJkLAogICAgUXVlc3Rpb25Gb3JtCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6aKY5bqT5L+h5oGvCiAgICAgIGJhbmtJZDogbnVsbCwKICAgICAgYmFua05hbWU6ICcnLAogICAgICAvLyDnu5/orqHmlbDmja4KICAgICAgc3RhdGlzdGljczogewogICAgICAgIHRvdGFsOiAwLAogICAgICAgIHNpbmdsZUNob2ljZTogMCwKICAgICAgICBtdWx0aXBsZUNob2ljZTogMCwKICAgICAgICBqdWRnbWVudDogMAogICAgICB9LAogICAgICAvLyDpopjnm67liJfooagKICAgICAgcXVlc3Rpb25MaXN0OiBbXSwKICAgICAgLy8g5YiG6aG15Y+C5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGJhbmtJZDogbnVsbCwKICAgICAgICBxdWVzdGlvblR5cGU6IG51bGwsCiAgICAgICAgZGlmZmljdWx0eTogbnVsbCwKICAgICAgICBxdWVzdGlvbkNvbnRlbnQ6IG51bGwKICAgICAgfSwKICAgICAgLy8g5bGV5byA54q25oCBCiAgICAgIGV4cGFuZEFsbDogZmFsc2UsCiAgICAgIGV4cGFuZGVkUXVlc3Rpb25zOiBbXSwKICAgICAgLy8g6YCJ5oup54q25oCBCiAgICAgIHNlbGVjdGVkUXVlc3Rpb25zOiBbXSwKICAgICAgaXNBbGxTZWxlY3RlZDogZmFsc2UsCiAgICAgIC8vIOihqOWNleebuOWFswogICAgICBxdWVzdGlvbkZvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgY3VycmVudFF1ZXN0aW9uVHlwZTogJ3NpbmdsZScsCiAgICAgIGN1cnJlbnRRdWVzdGlvbkRhdGE6IG51bGwsCiAgICAgIC8vIOaJuemHj+WvvOWFpQogICAgICBpbXBvcnREcmF3ZXJWaXNpYmxlOiBmYWxzZSwKICAgICAgLy8g5paH5qGj5a+85YWl5oq95bGJCiAgICAgIGRvY3VtZW50Q29udGVudDogJycsCiAgICAgIGRvY3VtZW50SHRtbENvbnRlbnQ6ICcnLAogICAgICBwYXJzZWRRdWVzdGlvbnM6IFtdLAogICAgICBwYXJzZUVycm9yczogW10sCiAgICAgIGFsbEV4cGFuZGVkOiB0cnVlLAogICAgICBpc1NldHRpbmdGcm9tQmFja2VuZDogZmFsc2UsCiAgICAgIGxhc3RQYXJzZWRDb250ZW50OiAnJywgLy8g6K6w5b2V5LiK5qyh6Kej5p6Q55qE5YaF5a6577yM6YG/5YWN6YeN5aSN6Kej5p6QCiAgICAgIGRvY3VtZW50SW1wb3J0RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHJ1bGVzRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGFjdGl2ZVJ1bGVUYWI6ICdleGFtcGxlcycsCiAgICAgIC8vIOS4iuS8oOWSjOino+aekOeKtuaAgQogICAgICBpc1VwbG9hZGluZzogZmFsc2UsCiAgICAgIGlzUGFyc2luZzogZmFsc2UsCiAgICAgIGltcG9ydGluZ1F1ZXN0aW9uczogZmFsc2UsCiAgICAgIGltcG9ydFByb2dyZXNzOiAwLAogICAgICBpbXBvcnRPcHRpb25zOiB7CiAgICAgICAgcmV2ZXJzZTogZmFsc2UsCiAgICAgICAgYWxsb3dEdXBsaWNhdGU6IGZhbHNlCiAgICAgIH0sCiAgICAgIC8vIOaWh+S7tuS4iuS8oAogICAgICB1cGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL2Jpei9xdWVzdGlvbkJhbmsvdXBsb2FkRG9jdW1lbnQnLAogICAgICB1cGxvYWRIZWFkZXJzOiB7CiAgICAgICAgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgdGhpcy4kc3RvcmUuZ2V0dGVycy50b2tlbgogICAgICB9LAogICAgICB1cGxvYWREYXRhOiB7fSwKICAgICAgLy8g5a+M5paH5pys57yW6L6R5ZmoCiAgICAgIHJpY2hFZGl0b3I6IG51bGwsCiAgICAgIGVkaXRvckluaXRpYWxpemVkOiBmYWxzZQogICAgfQogIH0sCgogIHdhdGNoOiB7CiAgICAvLyDnm5HlkKzot6/nlLHlj5jljJbvvIzlvZPot6/nlLHlj4LmlbDmlLnlj5jml7bph43mlrDliJ3lp4vljJbpobXpnaIKICAgICckcm91dGUnKHRvLCBmcm9tKSB7CiAgICAgIC8vIOWPquacieW9k+i3r+eUseWPguaVsOS4reeahGJhbmtJZOWPkeeUn+WPmOWMluaXtuaJjemHjeaWsOWIneWni+WMlgogICAgICBpZiAodG8ucXVlcnkuYmFua0lkICE9PSBmcm9tLnF1ZXJ5LmJhbmtJZCkgewogICAgICAgIHRoaXMuaW5pdFBhZ2UoKQogICAgICB9CiAgICB9LAogICAgLy8g55uR5ZCs5paH5qGj5YaF5a655Y+Y5YyW77yM6Ieq5Yqo6Kej5p6QCiAgICBkb2N1bWVudENvbnRlbnQ6IHsKICAgICAgaGFuZGxlcihuZXdWYWwpIHsKICAgICAgICAvLyDlpoLmnpzmmK/ku47lkI7nq6/orr7nva7lhoXlrrnvvIzkuI3op6blj5HliY3nq6/op6PmnpAKICAgICAgICBpZiAodGhpcy5pc1NldHRpbmdGcm9tQmFja2VuZCkgewogICAgICAgICAgcmV0dXJuCiAgICAgICAgfQoKICAgICAgICAvLyDlpoLmnpzlt7Lnu4/mnInop6PmnpDnu5PmnpzkuJTlhoXlrrnmsqHmnInlrp7otKjmgKflj5jljJbvvIzkuI3ph43mlrDop6PmnpAKICAgICAgICBpZiAodGhpcy5wYXJzZWRRdWVzdGlvbnMubGVuZ3RoID4gMCAmJiB0aGlzLmxhc3RQYXJzZWRDb250ZW50ICYmCiAgICAgICAgICAgIHRoaXMuc3RyaXBIdG1sVGFnc0tlZXBJbWFnZXMobmV3VmFsKSA9PT0gdGhpcy5zdHJpcEh0bWxUYWdzS2VlcEltYWdlcyh0aGlzLmxhc3RQYXJzZWRDb250ZW50KSkgewogICAgICAgICAgcmV0dXJuCiAgICAgICAgfQoKICAgICAgICBpZiAobmV3VmFsICYmIG5ld1ZhbC50cmltKCkpIHsKICAgICAgICAgIHRoaXMuZGVib3VuY2VQYXJzZURvY3VtZW50KCkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBbXQogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCiAgICAgICAgfQogICAgICB9LAogICAgICBpbW1lZGlhdGU6IGZhbHNlCiAgICB9LAogICAgLy8g55uR5ZCs5oq95bGJ5omT5byA54q25oCBCiAgICBpbXBvcnREcmF3ZXJWaXNpYmxlOiB7CiAgICAgIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgaWYgKG5ld1ZhbCkgewogICAgICAgICAgLy8g5oq95bGJ5omT5byA5pe25riF56m65omA5pyJ5YaF5a655bm25Yid5aeL5YyW57yW6L6R5ZmoCiAgICAgICAgICB0aGlzLmNsZWFySW1wb3J0Q29udGVudCgpCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuaW5pdFJpY2hFZGl0b3IoKQogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g5oq95bGJ5YWz6Zet5pe26ZSA5q+B57yW6L6R5ZmoCiAgICAgICAgICBpZiAodGhpcy5yaWNoRWRpdG9yKSB7CiAgICAgICAgICAgIHRoaXMucmljaEVkaXRvci5kZXN0cm95KCkKICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yID0gbnVsbAogICAgICAgICAgICB0aGlzLmVkaXRvckluaXRpYWxpemVkID0gZmFsc2UKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogZmFsc2UKICAgIH0KICB9LAoKICBjcmVhdGVkKCkgewogICAgdGhpcy5pbml0UGFnZSgpCiAgICAvLyDliJvlu7rpmLLmipblh73mlbAgLSDlop7liqDlu7bml7bliLAy56eS77yM5YeP5bCR5Y2h6aG/CiAgICB0aGlzLmRlYm91bmNlUGFyc2VEb2N1bWVudCA9IHRoaXMuZGVib3VuY2UodGhpcy5wYXJzZURvY3VtZW50LCAyMDAwKQogICAgLy8g5Yib5bu657yW6L6R5Zmo5YaF5a655Y+Y5YyW55qE6Ziy5oqW5Ye95pWwIC0g5bu25pe2MS4156eSCiAgICB0aGlzLmRlYm91bmNlRWRpdG9yQ29udGVudENoYW5nZSA9IHRoaXMuZGVib3VuY2UodGhpcy5oYW5kbGVFZGl0b3JDb250ZW50Q2hhbmdlRGVib3VuY2VkLCAxNTAwKQogICAgLy8g5Yid5aeL5YyW5LiK5Lyg5pWw5o2uCiAgICB0aGlzLnVwbG9hZERhdGEgPSB7CiAgICAgIGJhbmtJZDogdGhpcy5iYW5rSWQKICAgIH0KICAgIHRoaXMudXBsb2FkSGVhZGVycyA9IHsKICAgICAgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgdGhpcy4kc3RvcmUuZ2V0dGVycy50b2tlbgogICAgfQogIH0sCgogIG1vdW50ZWQoKSB7CiAgICAvLyDnvJbovpHlmajlsIblnKjmir3lsYnmiZPlvIDml7bliJ3lp4vljJYKCiAgfSwKCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIC8vIOWPlua2iOaJgOaciemYsuaKluWHveaVsAogICAgaWYgKHRoaXMuZGVib3VuY2VQYXJzZURvY3VtZW50ICYmIHRoaXMuZGVib3VuY2VQYXJzZURvY3VtZW50LmNhbmNlbCkgewogICAgICB0aGlzLmRlYm91bmNlUGFyc2VEb2N1bWVudC5jYW5jZWwoKQogICAgfQogICAgaWYgKHRoaXMuZGVib3VuY2VFZGl0b3JDb250ZW50Q2hhbmdlICYmIHRoaXMuZGVib3VuY2VFZGl0b3JDb250ZW50Q2hhbmdlLmNhbmNlbCkgewogICAgICB0aGlzLmRlYm91bmNlRWRpdG9yQ29udGVudENoYW5nZS5jYW5jZWwoKQogICAgfQoKICAgIC8vIOmUgOavgeWvjOaWh+acrOe8lui+keWZqAogICAgaWYgKHRoaXMucmljaEVkaXRvcikgewogICAgICB0aGlzLnJpY2hFZGl0b3IuZGVzdHJveSgpCiAgICAgIHRoaXMucmljaEVkaXRvciA9IG51bGwKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWIneWni+WMlumhtemdogogICAgaW5pdFBhZ2UoKSB7CiAgICAgIGNvbnN0IHsgYmFua0lkLCBiYW5rTmFtZSB9ID0gdGhpcy4kcm91dGUucXVlcnkKICAgICAgaWYgKCFiYW5rSWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvLrlsJHpopjlupNJROWPguaVsCcpCiAgICAgICAgdGhpcy5nb0JhY2soKQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIHRoaXMuYmFua0lkID0gYmFua0lkCiAgICAgIHRoaXMuYmFua05hbWUgPSBiYW5rTmFtZSB8fCAn6aKY5bqT6K+m5oOFJwoKICAgICAgLy8g6YeN572u5p+l6K+i5Y+C5pWw77yM56Gu5L+d5YiG6aG15LuO56ys5LiA6aG15byA5aeLCiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgYmFua0lkOiBiYW5rSWQsCiAgICAgICAgcXVlc3Rpb25UeXBlOiBudWxsLAogICAgICAgIGRpZmZpY3VsdHk6IG51bGwsCiAgICAgICAgcXVlc3Rpb25Db250ZW50OiBudWxsCiAgICAgIH0KCiAgICAgIC8vIOmHjee9ruWFtuS7lueKtuaAgQogICAgICB0aGlzLnF1ZXN0aW9uTGlzdCA9IFtdCiAgICAgIHRoaXMudG90YWwgPSAwCiAgICAgIHRoaXMuZXhwYW5kZWRRdWVzdGlvbnMgPSBbXQogICAgICB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zID0gW10KICAgICAgdGhpcy5leHBhbmRBbGwgPSBmYWxzZQoKICAgICAgLy8g6YeN5paw6I635Y+W5pWw5o2uCiAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKICAgIH0sCiAgICAvLyDov5Tlm57popjlupPliJfooagKICAgIGdvQmFjaygpIHsKICAgICAgdGhpcy4kcm91dGVyLmJhY2soKQogICAgfSwKICAgIC8vIOiOt+WPlumimOebruWIl+ihqAogICAgZ2V0UXVlc3Rpb25MaXN0KCkgewogICAgICAvLyDovazmjaLmn6Xor6Llj4LmlbDmoLzlvI8KICAgICAgY29uc3QgcGFyYW1zID0gdGhpcy5jb252ZXJ0UXVlcnlQYXJhbXModGhpcy5xdWVyeVBhcmFtcykKICAgICAgbGlzdFF1ZXN0aW9uKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5xdWVzdGlvbkxpc3QgPSByZXNwb25zZS5yb3dzCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bpopjnm67liJfooajlpLHotKUnKQogICAgICB9KQogICAgfSwKCiAgICAvLyDovazmjaLmn6Xor6Llj4LmlbDmoLzlvI8KICAgIGNvbnZlcnRRdWVyeVBhcmFtcyhwYXJhbXMpIHsKICAgICAgY29uc3QgY29udmVydGVkUGFyYW1zID0geyAuLi5wYXJhbXMgfQoKICAgICAgLy8g6L2s5o2i6aKY5Z6LCiAgICAgIGlmIChjb252ZXJ0ZWRQYXJhbXMucXVlc3Rpb25UeXBlKSB7CiAgICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAgICdzaW5nbGUnOiAxLAogICAgICAgICAgJ211bHRpcGxlJzogMiwKICAgICAgICAgICdqdWRnbWVudCc6IDMKICAgICAgICB9CiAgICAgICAgY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZSA9IHR5cGVNYXBbY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZV0gfHwgY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZQogICAgICB9CgogICAgICAvLyDovazmjaLpmr7luqYKICAgICAgaWYgKGNvbnZlcnRlZFBhcmFtcy5kaWZmaWN1bHR5KSB7CiAgICAgICAgY29uc3QgZGlmZmljdWx0eU1hcCA9IHsKICAgICAgICAgICfnroDljZUnOiAxLAogICAgICAgICAgJ+S4reetiSc6IDIsCiAgICAgICAgICAn5Zuw6Zq+JzogMwogICAgICAgIH0KICAgICAgICBjb252ZXJ0ZWRQYXJhbXMuZGlmZmljdWx0eSA9IGRpZmZpY3VsdHlNYXBbY29udmVydGVkUGFyYW1zLmRpZmZpY3VsdHldIHx8IGNvbnZlcnRlZFBhcmFtcy5kaWZmaWN1bHR5CiAgICAgIH0KCiAgICAgIC8vIOa4heeQhuepuuWAvAogICAgICBPYmplY3Qua2V5cyhjb252ZXJ0ZWRQYXJhbXMpLmZvckVhY2goa2V5ID0+IHsKICAgICAgICBpZiAoY29udmVydGVkUGFyYW1zW2tleV0gPT09ICcnIHx8IGNvbnZlcnRlZFBhcmFtc1trZXldID09PSBudWxsIHx8IGNvbnZlcnRlZFBhcmFtc1trZXldID09PSB1bmRlZmluZWQpIHsKICAgICAgICAgIGRlbGV0ZSBjb252ZXJ0ZWRQYXJhbXNba2V5XQogICAgICAgIH0KICAgICAgfSkKCiAgICAgIHJldHVybiBjb252ZXJ0ZWRQYXJhbXMKICAgIH0sCiAgICAvLyDojrflj5bnu5/orqHmlbDmja4KICAgIGdldFN0YXRpc3RpY3MoKSB7CiAgICAgIGdldFF1ZXN0aW9uU3RhdGlzdGljcyh0aGlzLmJhbmtJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5zdGF0aXN0aWNzID0gcmVzcG9uc2UuZGF0YQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgLy8g5L2/55So5qih5ouf5pWw5o2uCiAgICAgICAgdGhpcy5zdGF0aXN0aWNzID0gewogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaW5nbGVDaG9pY2U6IDAsCiAgICAgICAgICBtdWx0aXBsZUNob2ljZTogMCwKICAgICAgICAgIGp1ZGdtZW50OiAwCiAgICAgICAgfQogICAgICB9KQogICAgfSwKCgogICAgLy8g5aSE55CG5om56YeP5a+86aKY5oyJ6ZKu54K55Ye7CiAgICBoYW5kbGVCYXRjaEltcG9ydENsaWNrKCkgewogICAgICB0aGlzLmltcG9ydERyYXdlclZpc2libGUgPSB0cnVlCiAgICB9LAogICAgLy8g5re75Yqg6aKY55uuCiAgICBoYW5kbGVBZGRRdWVzdGlvbih0eXBlKSB7CiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uVHlwZSA9IHR5cGUKICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb25EYXRhID0gbnVsbAogICAgICB0aGlzLnF1ZXN0aW9uRm9ybVZpc2libGUgPSB0cnVlCiAgICB9LAogICAgLy8g5YiH5o2i5bGV5byA54q25oCBCiAgICB0b2dnbGVFeHBhbmRBbGwoKSB7CiAgICAgIHRoaXMuZXhwYW5kQWxsID0gIXRoaXMuZXhwYW5kQWxsCiAgICAgIGlmICghdGhpcy5leHBhbmRBbGwpIHsKICAgICAgICB0aGlzLmV4cGFuZGVkUXVlc3Rpb25zID0gW10KICAgICAgfQogICAgfSwKCgoKICAgIC8vIOWvvOWHuumimOebrgogICAgaGFuZGxlRXhwb3J0UXVlc3Rpb25zKCkgewogICAgICAvLyDnoa7orqTlr7zlh7oKICAgICAgdGhpcy4kY29uZmlybShg56Gu6K6k5a+85Ye66aKY5bqTIiR7dGhpcy5iYW5rTmFtZX0i5Lit55qE5omA5pyJ6aKY55uu5ZCX77yfYCwgJ+WvvOWHuuehruiupCcsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumuWvvOWHuicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ2luZm8nCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIGNvbnN0IGxvYWRpbmcgPSB0aGlzLiRsb2FkaW5nKHsKICAgICAgICAgIGxvY2s6IHRydWUsCiAgICAgICAgICB0ZXh0OiBg5q2j5Zyo5a+85Ye66aKY5bqT5Lit55qE5omA5pyJ6aKY55uuLi4uYCwKICAgICAgICAgIHNwaW5uZXI6ICdlbC1pY29uLWxvYWRpbmcnLAogICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMCwgMCwgMCwgMC43KScKICAgICAgICB9KQoKICAgICAgICAvLyDosIPnlKjlr7zlh7pBUEkgLSDlr7zlh7rlvZPliY3popjlupPnmoTmiYDmnInpopjnm64KICAgICAgICBleHBvcnRRdWVzdGlvbnNUb1dvcmQoewogICAgICAgICAgYmFua0lkOiB0aGlzLmJhbmtJZCwKICAgICAgICAgIGJhbmtOYW1lOiB0aGlzLmJhbmtOYW1lCiAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICBsb2FkaW5nLmNsb3NlKCkKCiAgICAgICAgICAvLyDliJvlu7rkuIvovb3pk77mjqUKICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbcmVzcG9uc2VdLCB7CiAgICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCcKICAgICAgICAgIH0pCiAgICAgICAgICBjb25zdCB1cmwgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKQogICAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKQogICAgICAgICAgbGluay5ocmVmID0gdXJsCiAgICAgICAgICBsaW5rLmRvd25sb2FkID0gYCR7dGhpcy5iYW5rTmFtZX0uZG9jeGAKICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluaykKICAgICAgICAgIGxpbmsuY2xpY2soKQogICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKQogICAgICAgICAgd2luZG93LlVSTC5yZXZva2VPYmplY3RVUkwodXJsKQoKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf5a+85Ye66aKY5bqTIiR7dGhpcy5iYW5rTmFtZX0iYCkKICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICBsb2FkaW5nLmNsb3NlKCkKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WvvOWHuuWksei0pTonLCBlcnJvcikKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWHuuWksei0pe+8jOivt+mHjeivlScpCiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIC8vIOeUqOaIt+WPlua2iOWvvOWHugogICAgICB9KQogICAgfSwKCiAgICAvLyDliIfmjaLlhajpgIkv5YWo5LiN6YCJCiAgICBoYW5kbGVUb2dnbGVTZWxlY3RBbGwoKSB7CiAgICAgIHRoaXMuaXNBbGxTZWxlY3RlZCA9ICF0aGlzLmlzQWxsU2VsZWN0ZWQKICAgICAgaWYgKHRoaXMuaXNBbGxTZWxlY3RlZCkgewogICAgICAgIC8vIOWFqOmAiQogICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMgPSB0aGlzLnF1ZXN0aW9uTGlzdC5tYXAocSA9PiBxLnF1ZXN0aW9uSWQpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlt7LpgInmi6kgJHt0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uuYCkKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlhajkuI3pgIkKICAgICAgICB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3suWPlua2iOmAieaLqeaJgOaciemimOebricpCiAgICAgIH0KICAgIH0sCgoKCiAgICAvLyDmibnph4/liKDpmaTvvIjkvJjljJbniYjmnKzvvIkKICAgIGhhbmRsZUJhdGNoRGVsZXRlKCkgewogICAgICBpZiAodGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeWIoOmZpOeahOmimOebricpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIGNvbnN0IGRlbGV0ZUNvdW50ID0gdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGgKICAgICAgbGV0IGNvbmZpcm1NZXNzYWdlID0gYOehruiupOWIoOmZpOmAieS4reeahCAke2RlbGV0ZUNvdW50fSDpgZPpopjnm67lkJfvvJ9gCgogICAgICBpZiAoZGVsZXRlQ291bnQgPiAyMCkgewogICAgICAgIGNvbmZpcm1NZXNzYWdlICs9ICdcblxu5rOo5oSP77ya6aKY55uu6L6D5aSa77yM5Yig6Zmk5Y+v6IO96ZyA6KaB5LiA5Lqb5pe26Ze077yM6K+36ICQ5b+D562J5b6F44CCJwogICAgICB9CgogICAgICB0aGlzLiRjb25maXJtKGNvbmZpcm1NZXNzYWdlLCAn5om56YeP5Yig6ZmkJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6a5Yig6ZmkJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiBmYWxzZQogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLnBlcmZvcm1CYXRjaERlbGV0ZSgpCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+W3suWPlua2iOWIoOmZpCcpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOaJp+ihjOaJuemHj+WIoOmZpAogICAgYXN5bmMgcGVyZm9ybUJhdGNoRGVsZXRlKCkgewogICAgICBjb25zdCBkZWxldGVDb3VudCA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoCiAgICAgIGNvbnN0IGxvYWRpbmcgPSB0aGlzLiRsb2FkaW5nKHsKICAgICAgICBsb2NrOiB0cnVlLAogICAgICAgIHRleHQ6IGDmraPlnKjliKDpmaQgJHtkZWxldGVDb3VudH0g6YGT6aKY55uu77yM6K+356iN5YCZLi4uYCwKICAgICAgICBzcGlubmVyOiAnZWwtaWNvbi1sb2FkaW5nJywKICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLCAwLCAwLCAwLjcpJwogICAgICB9KQoKICAgICAgdHJ5IHsKICAgICAgICAvLyDkvb/nlKjnnJ/mraPnmoTmibnph4/liKDpmaRBUEkKICAgICAgICBjb25zdCBxdWVzdGlvbklkcyA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMuam9pbignLCcpCiAgICAgICAgY29uc3Qgc3RhcnRUaW1lID0gRGF0ZS5ub3coKQoKICAgICAgICBhd2FpdCBkZWxRdWVzdGlvbihxdWVzdGlvbklkcykgLy8g6LCD55So5om56YeP5Yig6ZmkQVBJCgogICAgICAgIGNvbnN0IGVuZFRpbWUgPSBEYXRlLm5vdygpCiAgICAgICAgY29uc3QgZHVyYXRpb24gPSAoKGVuZFRpbWUgLSBzdGFydFRpbWUpIC8gMTAwMCkudG9GaXhlZCgxKQoKICAgICAgICBsb2FkaW5nLmNsb3NlKCkKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaIkOWKn+WIoOmZpCAke2RlbGV0ZUNvdW50fSDpgZPpopjnm64gKOiAl+aXtiAke2R1cmF0aW9ufXMpYCkKCiAgICAgICAgLy8g5riF55CG6YCJ5oup54q25oCBCiAgICAgICAgdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgdGhpcy5pc0FsbFNlbGVjdGVkID0gZmFsc2UKCiAgICAgICAgLy8g5Yi35paw5pWw5o2uCiAgICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpCgogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGxvYWRpbmcuY2xvc2UoKQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aJuemHj+WIoOmZpOWksei0pTonLCBlcnJvcikKCiAgICAgICAgbGV0IGVycm9yTWVzc2FnZSA9ICfmibnph4/liKDpmaTlpLHotKUnCiAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlICYmIGVycm9yLnJlc3BvbnNlLmRhdGEgJiYgZXJyb3IucmVzcG9uc2UuZGF0YS5tc2cpIHsKICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yLnJlc3BvbnNlLmRhdGEubXNnCiAgICAgICAgfSBlbHNlIGlmIChlcnJvci5tZXNzYWdlKSB7CiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlCiAgICAgICAgfQoKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGVycm9yTWVzc2FnZSkKICAgICAgfQogICAgfSwKCiAgICAvLyDpopjnm67pgInmi6nnirbmgIHlj5jljJYKICAgIGhhbmRsZVF1ZXN0aW9uU2VsZWN0KHF1ZXN0aW9uSWQsIHNlbGVjdGVkKSB7CiAgICAgIGlmIChzZWxlY3RlZCkgewogICAgICAgIGlmICghdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5pbmNsdWRlcyhxdWVzdGlvbklkKSkgewogICAgICAgICAgdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5wdXNoKHF1ZXN0aW9uSWQpCiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5pbmRleE9mKHF1ZXN0aW9uSWQpCiAgICAgICAgaWYgKGluZGV4ID4gLTEpIHsKICAgICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMuc3BsaWNlKGluZGV4LCAxKQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5pu05paw5YWo6YCJ54q25oCBCiAgICAgIHRoaXMuaXNBbGxTZWxlY3RlZCA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoID09PSB0aGlzLnF1ZXN0aW9uTGlzdC5sZW5ndGgKICAgIH0sCiAgICAvLyDliIfmjaLljZXkuKrpopjnm67lsZXlvIDnirbmgIEKICAgIGhhbmRsZVRvZ2dsZUV4cGFuZChxdWVzdGlvbklkKSB7CiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5leHBhbmRlZFF1ZXN0aW9ucy5pbmRleE9mKHF1ZXN0aW9uSWQpCiAgICAgIGlmIChpbmRleCA+IC0xKSB7CiAgICAgICAgLy8g5pS26LW36aKY55uuCiAgICAgICAgdGhpcy5leHBhbmRlZFF1ZXN0aW9ucy5zcGxpY2UoaW5kZXgsIDEpCiAgICAgICAgLy8g5aaC5p6c5b2T5YmN5pivIuWxleW8gOaJgOaciSLnirbmgIHvvIzliJnlj5bmtogi5bGV5byA5omA5pyJIueKtuaAgQogICAgICAgIGlmICh0aGlzLmV4cGFuZEFsbCkgewogICAgICAgICAgdGhpcy5leHBhbmRBbGwgPSBmYWxzZQogICAgICAgICAgLy8g5bCG5YW25LuW6aKY55uu5re75Yqg5YiwZXhwYW5kZWRRdWVzdGlvbnPmlbDnu4TkuK3vvIzpmaTkuoblvZPliY3opoHmlLbotbfnmoTpopjnm64KICAgICAgICAgIHRoaXMucXVlc3Rpb25MaXN0LmZvckVhY2gocXVlc3Rpb24gPT4gewogICAgICAgICAgICBpZiAocXVlc3Rpb24ucXVlc3Rpb25JZCAhPT0gcXVlc3Rpb25JZCAmJiAhdGhpcy5leHBhbmRlZFF1ZXN0aW9ucy5pbmNsdWRlcyhxdWVzdGlvbi5xdWVzdGlvbklkKSkgewogICAgICAgICAgICAgIHRoaXMuZXhwYW5kZWRRdWVzdGlvbnMucHVzaChxdWVzdGlvbi5xdWVzdGlvbklkKQogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlsZXlvIDpopjnm64KICAgICAgICB0aGlzLmV4cGFuZGVkUXVlc3Rpb25zLnB1c2gocXVlc3Rpb25JZCkKICAgICAgfQogICAgfSwKICAgIC8vIOe8lui+kemimOebrgogICAgaGFuZGxlRWRpdFF1ZXN0aW9uKHF1ZXN0aW9uKSB7CiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uRGF0YSA9IHF1ZXN0aW9uCiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uVHlwZSA9IHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZQogICAgICB0aGlzLnF1ZXN0aW9uRm9ybVZpc2libGUgPSB0cnVlCiAgICB9LAogICAgLy8g5aSN5Yi26aKY55uuCiAgICBoYW5kbGVDb3B5UXVlc3Rpb24ocXVlc3Rpb24pIHsKICAgICAgLy8g5Yib5bu65aSN5Yi255qE6aKY55uu5pWw5o2u77yI56e76ZmkSUTnm7jlhbPlrZfmrrXvvIkKICAgICAgY29uc3QgY29waWVkUXVlc3Rpb24gPSB7CiAgICAgICAgLi4ucXVlc3Rpb24sCiAgICAgICAgcXVlc3Rpb25JZDogbnVsbCwgIC8vIOa4hemZpElE77yM6KGo56S65paw5aKeCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwKICAgICAgICB1cGRhdGVUaW1lOiBudWxsLAogICAgICAgIGNyZWF0ZUJ5OiBudWxsLAogICAgICAgIHVwZGF0ZUJ5OiBudWxsCiAgICAgIH0KCiAgICAgIC8vIOiuvue9ruS4uue8lui+keaooeW8j+W5tuaJk+W8gOihqOWNlQogICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvbkRhdGEgPSBjb3BpZWRRdWVzdGlvbgogICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvblR5cGUgPSB0aGlzLmNvbnZlcnRRdWVzdGlvblR5cGVUb1N0cmluZyhxdWVzdGlvbi5xdWVzdGlvblR5cGUpCiAgICAgIHRoaXMucXVlc3Rpb25Gb3JtVmlzaWJsZSA9IHRydWUKICAgIH0sCgogICAgLy8g6aKY5Z6L5pWw5a2X6L2s5a2X56ym5Liy77yI55So5LqO5aSN5Yi25Yqf6IO977yJCiAgICBjb252ZXJ0UXVlc3Rpb25UeXBlVG9TdHJpbmcodHlwZSkgewogICAgICBjb25zdCB0eXBlTWFwID0gewogICAgICAgIDE6ICdzaW5nbGUnLAogICAgICAgIDI6ICdtdWx0aXBsZScsCiAgICAgICAgMzogJ2p1ZGdtZW50JwogICAgICB9CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8IHR5cGUKICAgIH0sCiAgICAvLyDliKDpmaTpopjnm64KICAgIGhhbmRsZURlbGV0ZVF1ZXN0aW9uKHF1ZXN0aW9uKSB7CiAgICAgIGNvbnN0IHF1ZXN0aW9uQ29udGVudCA9IHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudC5yZXBsYWNlKC88W14+XSo+L2csICcnKQogICAgICBjb25zdCBkaXNwbGF5Q29udGVudCA9IHF1ZXN0aW9uQ29udGVudC5sZW5ndGggPiA1MCA/IHF1ZXN0aW9uQ29udGVudC5zdWJzdHJpbmcoMCwgNTApICsgJy4uLicgOiBxdWVzdGlvbkNvbnRlbnQKICAgICAgdGhpcy4kY29uZmlybShg56Gu6K6k5Yig6Zmk6aKY55uuIiR7ZGlzcGxheUNvbnRlbnR9IuWQl++8n2AsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICBkZWxRdWVzdGlvbihxdWVzdGlvbi5xdWVzdGlvbklkKS50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykKICAgICAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgICAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yig6Zmk6aKY55uu5aSx6LSlJykKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIC8vIOmimOebruihqOWNleaIkOWKn+WbnuiwgwogICAgaGFuZGxlUXVlc3Rpb25Gb3JtU3VjY2VzcygpIHsKICAgICAgdGhpcy5xdWVzdGlvbkZvcm1WaXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKQogICAgfSwKCgoKICAgIC8vIOaKveWxieWFs+mXreWJjeWkhOeQhgogICAgaGFuZGxlRHJhd2VyQ2xvc2UoZG9uZSkgewogICAgICAvLyDmo4Dmn6XmmK/lkKbmnInmnKrkv53lrZjnmoTlhoXlrrkKICAgICAgY29uc3QgaGFzQ29udGVudCA9IHRoaXMuZG9jdW1lbnRDb250ZW50ICYmIHRoaXMuZG9jdW1lbnRDb250ZW50LnRyaW0oKS5sZW5ndGggPiAwCiAgICAgIGNvbnN0IGhhc1BhcnNlZFF1ZXN0aW9ucyA9IHRoaXMucGFyc2VkUXVlc3Rpb25zICYmIHRoaXMucGFyc2VkUXVlc3Rpb25zLmxlbmd0aCA+IDAKCiAgICAgIGlmIChoYXNDb250ZW50IHx8IGhhc1BhcnNlZFF1ZXN0aW9ucykgewogICAgICAgIGxldCBtZXNzYWdlID0gJ+WFs+mXreWQjuWwhuS4ouWkseW9k+WJjee8lui+keeahOWGheWuue+8jOehruiupOWFs+mXreWQl++8nycKICAgICAgICBpZiAoaGFzUGFyc2VkUXVlc3Rpb25zKSB7CiAgICAgICAgICBtZXNzYWdlID0gYOW9k+WJjeW3suino+aekOWHuiAke3RoaXMucGFyc2VkUXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uu77yM5YWz6Zet5ZCO5bCG5Lii5aSx5omA5pyJ5YaF5a6577yM56Gu6K6k5YWz6Zet5ZCX77yfYAogICAgICAgIH0KCiAgICAgICAgdGhpcy4kY29uZmlybShtZXNzYWdlLCAn56Gu6K6k5YWz6ZetJywgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrprlhbPpl60nLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+e7p+e7ree8lui+kScsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgIC8vIOa4heepuuWGheWuuQogICAgICAgICAgdGhpcy5jbGVhckltcG9ydENvbnRlbnQoKQogICAgICAgICAgZG9uZSgpCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgLy8g5Y+W5raI5YWz6Zet77yM57un57ut57yW6L6RCiAgICAgICAgfSkKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDmsqHmnInlhoXlrrnnm7TmjqXlhbPpl60KICAgICAgICBkb25lKCkKICAgICAgfQogICAgfSwKCiAgICAvLyDmuIXnqbrlr7zlhaXlhoXlrrkKICAgIGNsZWFySW1wb3J0Q29udGVudCgpIHsKICAgICAgLy8g5riF56m65paH5qGj5YaF5a65CiAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gJycKICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gJycKCiAgICAgIC8vIOa4heepuuino+aekOe7k+aenAogICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSBbXQoKICAgICAgLy8g6YeN572u6Kej5p6Q54q25oCBCiAgICAgIHRoaXMuYWxsRXhwYW5kZWQgPSB0cnVlCiAgICAgIHRoaXMuaXNTZXR0aW5nRnJvbUJhY2tlbmQgPSBmYWxzZQogICAgICB0aGlzLmxhc3RQYXJzZWRDb250ZW50ID0gJycgLy8g5riF56m65LiK5qyh6Kej5p6Q55qE5YaF5a656K6w5b2VCgogICAgICAvLyDph43nva7kuIrkvKDnirbmgIEKICAgICAgdGhpcy5pc1VwbG9hZGluZyA9IGZhbHNlCiAgICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2UKICAgICAgdGhpcy5pbXBvcnRpbmdRdWVzdGlvbnMgPSBmYWxzZQogICAgICB0aGlzLmltcG9ydFByb2dyZXNzID0gMAoKICAgICAgLy8g6YeN572u5a+85YWl6YCJ6aG5CiAgICAgIHRoaXMuaW1wb3J0T3B0aW9ucyA9IHsKICAgICAgICByZXZlcnNlOiBmYWxzZSwKICAgICAgICBhbGxvd0R1cGxpY2F0ZTogZmFsc2UKICAgICAgfQogICAgfSwKCiAgICAvLyDmmL7npLrmlofmoaPlr7zlhaXlr7nor53moYYKICAgIHNob3dEb2N1bWVudEltcG9ydERpYWxvZygpIHsKICAgICAgLy8g5riF6Zmk5LiK5LiA5qyh55qE5LiK5Lyg54q25oCB5ZKM5YaF5a65CiAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZQogICAgICB0aGlzLmlzUGFyc2luZyA9IGZhbHNlCgogICAgICAvLyDmuIXpmaTkuIrkvKDnu4Tku7bnmoTmlofku7bliJfooagKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIGNvbnN0IHVwbG9hZENvbXBvbmVudCA9IHRoaXMuJHJlZnMuZG9jdW1lbnRVcGxvYWQKICAgICAgICBpZiAodXBsb2FkQ29tcG9uZW50KSB7CiAgICAgICAgICB1cGxvYWRDb21wb25lbnQuY2xlYXJGaWxlcygpCiAgICAgICAgfQogICAgICB9KQoKICAgICAgdGhpcy5kb2N1bWVudEltcG9ydERpYWxvZ1Zpc2libGUgPSB0cnVlCgogICAgfSwKCiAgICAvLyDmmL7npLrop4TojIPlr7nor53moYYKICAgIHNob3dSdWxlc0RpYWxvZygpIHsKICAgICAgdGhpcy5hY3RpdmVSdWxlVGFiID0gJ2V4YW1wbGVzJyAvLyDpu5jorqTmmL7npLrojIPkvovmoIfnrb7pobUKICAgICAgdGhpcy5ydWxlc0RpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICB9LAoKICAgIC8vIOWwhuiMg+S+i+WkjeWItuWIsOe8lui+keWMuiAtIOWPquS/neeVmeWJjTPpopjvvJrljZXpgInjgIHlpJrpgInjgIHliKTmlq0KICAgIGNvcHlFeGFtcGxlVG9FZGl0b3IoKSB7CiAgICAgIC8vIOS9v+eUqOi+k+WFpeiMg+S+i+agh+etvumhtemHjOeahOWJjTPpopjlhoXlrrnvvIzovazmjaLkuLpIVE1M5qC85byPCiAgICAgIGNvbnN0IGh0bWxUZW1wbGF0ZSA9IGAKPHA+MS7vvIggIO+8ieaYr+aIkeWbveacgOaXqeeahOivl+atjOaAu+mbhu+8jOWPiOensOS9nCLor5fkuInnmb4i44CCPC9wPgo8cD5BLuOAiuW3puS8oOOAizwvcD4KPHA+Qi7jgIrnprvpqprjgIs8L3A+CjxwPkMu44CK5Z2b57uP44CLPC9wPgo8cD5ELuOAiuivl+e7j+OAizwvcD4KPHA+562U5qGI77yaRDwvcD4KPHA+6Kej5p6Q77ya6K+X57uP5piv5oiR5Zu95pyA5pep55qE6K+X5q2M5oC76ZuG44CCPC9wPgo8cD7pmr7luqbvvJrkuK3nrYk8L3A+CjxwPjxicj48L3A+Cgo8cD4yLuS4reWNjuS6uuawkeWFseWSjOWbveeahOaIkOeri++8jOagh+W/l+edgO+8iCDvvInjgII8L3A+CjxwPkEu5Lit5Zu95paw5rCR5Li75Li75LmJ6Z2p5ZG95Y+W5b6X5LqG5Z+65pys6IOc5YipPC9wPgo8cD5CLuS4reWbveeOsOS7o+WPsueahOW8gOWnizwvcD4KPHA+Qy7ljYrmrpbmsJHlnLDljYrlsIHlu7rnpL7kvJrnmoTnu5PmnZ88L3A+CjxwPkQu5Lit5Zu96L+b5YWl56S+5Lya5Li75LmJ56S+5LyaPC9wPgo8cD7nrZTmoYjvvJpBQkM8L3A+CjxwPuino+aekO+8muaWsOS4reWbveeahOaIkOeri++8jOagh+W/l+edgOaIkeWbveaWsOawkeS4u+S4u+S5iemdqeWRvemYtuauteeahOWfuuacrOe7k+adn+WSjOekvuS8muS4u+S5iemdqeWRvemYtuauteeahOW8gOWni+OAgjwvcD4KPHA+PGJyPjwvcD4KCjxwPjMu5YWD5p2C5Ymn55qE5Zub5aSn5oKy5Ymn5piv77ya5YWz5rGJ5Y2/55qE44CK56qm5ail5Yak44CL77yM6ams6Ie06L+c55qE44CK5rGJ5a6r56eL44CL77yM55m95py055qE44CK5qKn5qGQ6Zuo44CL5ZKM6YOR5YWJ56WW55qE44CK6LW15rCP5a2k5YS/44CL44CCPC9wPgo8cD7nrZTmoYjvvJrplJnor688L3A+CjxwPuino+aekO+8muWFg+adguWJp+OAiui1teawj+WtpOWEv+OAi+WFqOWQjeOAiuWGpOaKpeWGpOi1teawj+WtpOWEv+OAi++8jOS4uue6quWQm+elpeaJgOS9nOOAgjwvcD4KICAgICAgYC50cmltKCkKCiAgICAgIC8vIOebtOaOpeiuvue9ruWIsOWvjOaWh+acrOe8lui+keWZqAogICAgICBpZiAodGhpcy5yaWNoRWRpdG9yICYmIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQpIHsKICAgICAgICB0aGlzLnJpY2hFZGl0b3Iuc2V0RGF0YShodG1sVGVtcGxhdGUpCgogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWmguaenOe8lui+keWZqOacquWIneWni+WMlu+8jOetieW+heWIneWni+WMluWQjuWGjeiuvue9rgogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIGlmICh0aGlzLnJpY2hFZGl0b3IgJiYgdGhpcy5lZGl0b3JJbml0aWFsaXplZCkgewogICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3Iuc2V0RGF0YShodG1sVGVtcGxhdGUpCgogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0KCiAgICAgIC8vIOWFs+mXreWvueivneahhgogICAgICB0aGlzLnJ1bGVzRGlhbG9nVmlzaWJsZSA9IGZhbHNlCgogICAgICAvLyDmj5DnpLrnlKjmiLcKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfovpPlhaXojIPkvovlt7LloavlhYXliLDnvJbovpHljLrvvIzlj7PkvqflsIboh6rliqjop6PmnpAnKQoKCiAgICB9LAoKCgogICAgLy8g5LiL6L29V29yZOaooeadvwogICAgZG93bmxvYWRXb3JkVGVtcGxhdGUoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ2Jpei9xdWVzdGlvbkJhbmsvZG93bmxvYWRXb3JkVGVtcGxhdGUnLCB7fSwgYOmimOebruWvvOWFpVdvcmTmqKHmnb8uZG9jeGApCiAgICB9LAoKICAgIC8vIOS4iuS8oOWJjeajgOafpQogICAgYmVmb3JlVXBsb2FkKGZpbGUpIHsKCgogICAgICBjb25zdCBpc1ZhbGlkVHlwZSA9IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC53b3JkcHJvY2Vzc2luZ21sLmRvY3VtZW50JyB8fAogICAgICAgICAgICAgICAgICAgICAgICAgZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LnNwcmVhZHNoZWV0bWwuc2hlZXQnIHx8CiAgICAgICAgICAgICAgICAgICAgICAgICBmaWxlLm5hbWUuZW5kc1dpdGgoJy5kb2N4JykgfHwgZmlsZS5uYW1lLmVuZHNXaXRoKCcueGxzeCcpCiAgICAgIGNvbnN0IGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwCgogICAgICBpZiAoIWlzVmFsaWRUeXBlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5LygIC5kb2N4IOaIliAueGxzeCDmoLzlvI/nmoTmlofku7YhJykKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQogICAgICBpZiAoIWlzTHQxME0pIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDmlofku7blpKflsI/kuI3og73otoXov4cgMTBNQiEnKQogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CgogICAgICAvLyDmm7TmlrDkuIrkvKDmlbDmja4KICAgICAgdGhpcy51cGxvYWREYXRhLmJhbmtJZCA9IHRoaXMuYmFua0lkCgogICAgICAvLyDorr7nva7kuIrkvKDnirbmgIEKICAgICAgdGhpcy5pc1VwbG9hZGluZyA9IHRydWUKICAgICAgdGhpcy5pc1BhcnNpbmcgPSBmYWxzZQoKCgogICAgICByZXR1cm4gdHJ1ZQogICAgfSwKCiAgICAvLyDkuIrkvKDmiJDlip8KICAgIGhhbmRsZVVwbG9hZFN1Y2Nlc3MocmVzcG9uc2UpIHsKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgIC8vIOS4iuS8oOWujOaIkO+8jOW8gOWni+ino+aekAogICAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZQogICAgICAgIHRoaXMuaXNQYXJzaW5nID0gdHJ1ZQoKCgogICAgICAgIC8vIOa4hemZpOS5i+WJjeeahOino+aekOe7k+aenO+8jOehruS/neW5suWHgOeahOW8gOWniwogICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KCiAgICAgICAgLy8g5bu26L+f5YWz6Zet5a+56K+d5qGG77yM6K6p55So5oi355yL5Yiw6Kej5p6Q5Yqo55S7CiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICB0aGlzLmRvY3VtZW50SW1wb3J0RGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgICB0aGlzLmlzUGFyc2luZyA9IGZhbHNlCiAgICAgICAgfSwgMTUwMCkKCiAgICAgICAgLy8g6K6+572u5qCH5b+X5L2N77yM6YG/5YWN6Kem5Y+R5YmN56uv6YeN5paw6Kej5p6QCiAgICAgICAgdGhpcy5pc1NldHRpbmdGcm9tQmFja2VuZCA9IHRydWUKCiAgICAgICAgLy8g5bCG6Kej5p6Q57uT5p6c5pi+56S65Zyo5Y+z5L6nCiAgICAgICAgaWYgKHJlc3BvbnNlLnF1ZXN0aW9ucyAmJiByZXNwb25zZS5xdWVzdGlvbnMubGVuZ3RoID4gMCkgewogICAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSByZXNwb25zZS5xdWVzdGlvbnMubWFwKHF1ZXN0aW9uID0+ICh7CiAgICAgICAgICAgIC4uLnF1ZXN0aW9uLAogICAgICAgICAgICBjb2xsYXBzZWQ6IGZhbHNlICAvLyDpu5jorqTlsZXlvIAKICAgICAgICAgIH0pKQogICAgICAgICAgLy8g6YeN572u5YWo6YOo5bGV5byA54q25oCBCiAgICAgICAgICB0aGlzLmFsbEV4cGFuZGVkID0gdHJ1ZQogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHJlc3BvbnNlLmVycm9ycyB8fCBbXQoKICAgICAgICAgIC8vIOaYvuekuuivpue7hueahOino+aekOe7k+aenAogICAgICAgICAgY29uc3QgZXJyb3JDb3VudCA9IHJlc3BvbnNlLmVycm9ycyA/IHJlc3BvbnNlLmVycm9ycy5sZW5ndGggOiAwCiAgICAgICAgICBpZiAoZXJyb3JDb3VudCA+IDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/op6PmnpDlh7ogJHtyZXNwb25zZS5xdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm67vvIzmnIkgJHtlcnJvckNvdW50fSDkuKrplJnor6/miJborablkYpgKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/op6PmnpDlh7ogJHtyZXNwb25zZS5xdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm65gKQogICAgICAgICAgfQoKCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+acquino+aekOWHuuS7u+S9lemimOebru+8jOivt+ajgOafpeaWh+S7tuagvOW8jycpCiAgICAgICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gcmVzcG9uc2UuZXJyb3JzIHx8IFsn5pyq6IO96Kej5p6Q5Ye66aKY55uu5YaF5a65J10KCgogICAgICAgIH0KCiAgICAgICAgLy8g5bCG5Y6f5aeL5YaF5a655aGr5YWF5Yiw5a+M5paH5pys57yW6L6R5Zmo5LitCiAgICAgICAgaWYgKHJlc3BvbnNlLm9yaWdpbmFsQ29udGVudCkgewogICAgICAgICAgdGhpcy5zZXRFZGl0b3JDb250ZW50KHJlc3BvbnNlLm9yaWdpbmFsQ29udGVudCkKICAgICAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gcmVzcG9uc2Uub3JpZ2luYWxDb250ZW50CiAgICAgICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSByZXNwb25zZS5vcmlnaW5hbENvbnRlbnQgLy8g5Yid5aeL5YyWSFRNTOWGheWuuQogICAgICAgICAgdGhpcy5sYXN0UGFyc2VkQ29udGVudCA9IHJlc3BvbnNlLm9yaWdpbmFsQ29udGVudCAvLyDorrDlvZXlt7Lop6PmnpDnmoTlhoXlrrkKCiAgICAgICAgfQoKICAgICAgICAvLyDlu7bov5/ph43nva7moIflv5fkvY3vvIznoa7kv53miYDmnInlvILmraXmk43kvZzlrozmiJAKICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgIHRoaXMuaXNTZXR0aW5nRnJvbUJhY2tlbmQgPSBmYWxzZQogICAgICAgIH0sIDUwMDApIC8vIOW7tumVv+WIsDXnp5LvvIznoa7kv53nvJbovpHlmajlhoXlrrnnqLPlrpoKICAgICAgfSBlbHNlIHsKCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+aWh+S7tuS4iuS8oOWksei0pScpCiAgICAgICAgLy8g6YeN572u54q25oCBCiAgICAgICAgdGhpcy5pc1VwbG9hZGluZyA9IGZhbHNlCiAgICAgICAgdGhpcy5pc1BhcnNpbmcgPSBmYWxzZQogICAgICB9CiAgICB9LAoKICAgIC8vIOS4iuS8oOWksei0pQogICAgaGFuZGxlVXBsb2FkRXJyb3IoKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuS4iuS8oOWksei0pe+8jOivt+ajgOafpee9kee7nOi/nuaOpeaIluiBlOezu+euoeeQhuWRmCcpCgogICAgICAvLyDph43nva7nirbmgIEKICAgICAgdGhpcy5pc1VwbG9hZGluZyA9IGZhbHNlCiAgICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2UKICAgIH0sCgoKCiAgICAvLyDliIfmjaLpopjnm67lsZXlvIAv5pS26LW3CiAgICB0b2dnbGVRdWVzdGlvbihpbmRleCkgewogICAgICBjb25zdCBxdWVzdGlvbiA9IHRoaXMucGFyc2VkUXVlc3Rpb25zW2luZGV4XQogICAgICB0aGlzLiRzZXQocXVlc3Rpb24sICdjb2xsYXBzZWQnLCAhcXVlc3Rpb24uY29sbGFwc2VkKQogICAgfSwKCiAgICAvLyDlhajpg6jlsZXlvIAv5pS26LW3CiAgICB0b2dnbGVBbGxRdWVzdGlvbnMoKSB7CiAgICAgIHRoaXMuYWxsRXhwYW5kZWQgPSAhdGhpcy5hbGxFeHBhbmRlZAogICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucy5mb3JFYWNoKHF1ZXN0aW9uID0+IHsKICAgICAgICB0aGlzLiRzZXQocXVlc3Rpb24sICdjb2xsYXBzZWQnLCAhdGhpcy5hbGxFeHBhbmRlZCkKICAgICAgfSkKCiAgICB9LAoKICAgIC8vIOehruiupOWvvOWFpQogICAgY29uZmlybUltcG9ydCgpIHsKICAgICAgaWYgKHRoaXMucGFyc2VkUXVlc3Rpb25zLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5rKh5pyJ5Y+v5a+85YWl55qE6aKY55uuJykKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgLy8g5p6E5bu656Gu6K6k5L+h5oGvCiAgICAgIGxldCBjb25maXJtTWVzc2FnZSA9IGDnoa7orqTlr7zlhaUgJHt0aGlzLnBhcnNlZFF1ZXN0aW9ucy5sZW5ndGh9IOmBk+mimOebruWQl++8n2AKICAgICAgbGV0IG9wdGlvbk1lc3NhZ2VzID0gW10KCiAgICAgIGlmICh0aGlzLmltcG9ydE9wdGlvbnMucmV2ZXJzZSkgewogICAgICAgIG9wdGlvbk1lc3NhZ2VzLnB1c2goJ+WwhuaMieWAkuW6j+WvvOWFpScpCiAgICAgIH0KICAgICAgaWYgKHRoaXMuaW1wb3J0T3B0aW9ucy5hbGxvd0R1cGxpY2F0ZSkgewogICAgICAgIG9wdGlvbk1lc3NhZ2VzLnB1c2goJ+WFgeiuuOmHjeWkjemimOebricpCiAgICAgIH0KCiAgICAgIGlmIChvcHRpb25NZXNzYWdlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgY29uZmlybU1lc3NhZ2UgKz0gYFxuXG7lr7zlhaXpgInpobnvvJoke29wdGlvbk1lc3NhZ2VzLmpvaW4oJ++8jCcpfWAKICAgICAgfQoKICAgICAgdGhpcy4kY29uZmlybShjb25maXJtTWVzc2FnZSwgJ+ehruiupOWvvOWFpScsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumuWvvOWFpScsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgIGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogZmFsc2UKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5pbXBvcnRRdWVzdGlvbnMoKQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkKICAgIH0sCgogICAgLy8g5a+85YWl6aKY55uuCiAgICBhc3luYyBpbXBvcnRRdWVzdGlvbnMoKSB7CiAgICAgIHRoaXMuaW1wb3J0aW5nUXVlc3Rpb25zID0gdHJ1ZQogICAgICB0aGlzLmltcG9ydFByb2dyZXNzID0gMAoKICAgICAgdHJ5IHsKICAgICAgICAvLyDlpITnkIblr7zlhaXpgInpobkKICAgICAgICBsZXQgcXVlc3Rpb25zVG9JbXBvcnQgPSBbLi4udGhpcy5wYXJzZWRRdWVzdGlvbnNdCgogICAgICAgIGlmICh0aGlzLmltcG9ydE9wdGlvbnMucmV2ZXJzZSkgewogICAgICAgICAgcXVlc3Rpb25zVG9JbXBvcnQucmV2ZXJzZSgpCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+W3suaMieWAkuW6j+aOkuWIl+mimOebricpCiAgICAgICAgfQoKICAgICAgICAvLyDmqKHmi5/ov5vluqbmm7TmlrAKICAgICAgICB0aGlzLmltcG9ydFByb2dyZXNzID0gMTAKCiAgICAgICAgLy8g6LCD55So5a6e6ZmF55qE5a+85YWlQVBJCiAgICAgICAgY29uc3QgaW1wb3J0RGF0YSA9IHsKICAgICAgICAgIGJhbmtJZDogdGhpcy5iYW5rSWQsCiAgICAgICAgICBxdWVzdGlvbnM6IHF1ZXN0aW9uc1RvSW1wb3J0LAogICAgICAgICAgYWxsb3dEdXBsaWNhdGU6IHRoaXMuaW1wb3J0T3B0aW9ucy5hbGxvd0R1cGxpY2F0ZSwKICAgICAgICAgIHJldmVyc2U6IHRoaXMuaW1wb3J0T3B0aW9ucy5yZXZlcnNlCiAgICAgICAgfQoKICAgICAgICB0aGlzLmltcG9ydFByb2dyZXNzID0gMzAKCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBiYXRjaEltcG9ydFF1ZXN0aW9ucyhpbXBvcnREYXRhKQoKICAgICAgICB0aGlzLmltcG9ydFByb2dyZXNzID0gODAKCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy5pbXBvcnRQcm9ncmVzcyA9IDEwMAoKICAgICAgICAgIC8vIOaYvuekuuivpue7hueahOWvvOWFpee7k+aenAogICAgICAgICAgY29uc3QgcmVzdWx0ID0gcmVzcG9uc2UuZGF0YSB8fCB7fQogICAgICAgICAgY29uc3Qgc3VjY2Vzc0NvdW50ID0gcmVzdWx0LnN1Y2Nlc3NDb3VudCB8fCAwCiAgICAgICAgICBjb25zdCBmYWlsQ291bnQgPSByZXN1bHQuZmFpbENvdW50IHx8IDAKICAgICAgICAgIGNvbnN0IHNraXBwZWRDb3VudCA9IHJlc3VsdC5za2lwcGVkQ291bnQgfHwgMAoKICAgICAgICAgIC8vIOaehOW7uue7k+aenOa2iOaBrwogICAgICAgICAgbGV0IHJlc3VsdE1lc3NhZ2UgPSBg5a+85YWl5a6M5oiQ77ya5oiQ5YqfICR7c3VjY2Vzc0NvdW50fSDpgZNgCgogICAgICAgICAgaWYgKGZhaWxDb3VudCA+IDApIHsKICAgICAgICAgICAgcmVzdWx0TWVzc2FnZSArPSBg77yM5aSx6LSlICR7ZmFpbENvdW50fSDpgZNgCiAgICAgICAgICB9CgogICAgICAgICAgaWYgKHNraXBwZWRDb3VudCA+IDApIHsKICAgICAgICAgICAgcmVzdWx0TWVzc2FnZSArPSBg77yM6Lez6L+H6YeN5aSNICR7c2tpcHBlZENvdW50fSDpgZNgCiAgICAgICAgICB9CgogICAgICAgICAgcmVzdWx0TWVzc2FnZSArPSAnIOmimOebricKCiAgICAgICAgICAvLyDmoLnmja7nu5PmnpznsbvlnovmmL7npLrkuI3lkIznmoTmtojmga8KICAgICAgICAgIGlmIChmYWlsQ291bnQgPiAwIHx8IHNraXBwZWRDb3VudCA+IDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3VsdE1lc3NhZ2UpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzdWx0TWVzc2FnZSkKICAgICAgICAgIH0KCiAgICAgICAgICAvLyDlpoLmnpzmnInplJnor6/kv6Hmga/vvIzmmL7npLror6bmg4UKICAgICAgICAgIGlmIChyZXN1bHQuZXJyb3JzICYmIHJlc3VsdC5lcnJvcnMubGVuZ3RoID4gMCkgewogICAgICAgICAgICBjb25zb2xlLndhcm4oJ+WvvOWFpeivpuaDhTonLCByZXN1bHQuZXJyb3JzKQoKICAgICAgICAgICAgLy8g5aaC5p6c5pyJ6Lez6L+H55qE6aKY55uu77yM5Y+v5Lul5pi+56S65pu06K+m57uG55qE5L+h5oGvCiAgICAgICAgICAgIGlmIChza2lwcGVkQ291bnQgPiAwKSB7CiAgICAgICAgICAgICAgY29uc3Qgc2tpcHBlZEVycm9ycyA9IHJlc3VsdC5lcnJvcnMuZmlsdGVyKGVycm9yID0+IGVycm9yLmluY2x1ZGVzKCfph43lpI3ot7Pov4cnKSkKICAgICAgICAgICAgICBpZiAoc2tpcHBlZEVycm9ycy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmluZm8oJ+i3s+i/h+eahOmHjeWkjemimOebrjonLCBza2lwcGVkRXJyb3JzKQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubXNnIHx8ICflr7zlhaXlpLHotKUnKQogICAgICAgIH0KCiAgICAgICAgLy8g5riF55CG54q25oCB5bm25YWz6Zet5oq95bGJCiAgICAgICAgdGhpcy5pbXBvcnREcmF3ZXJWaXNpYmxlID0gZmFsc2UKICAgICAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9ICcnCiAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gJycKICAgICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCgogICAgICAgIC8vIOWIt+aWsOaVsOaNrgogICAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKQoKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCflr7zlhaXpopjnm67lpLHotKU6JywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5a+85YWl5aSx6LSlOiAnICsgKGVycm9yLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKQogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMuaW1wb3J0aW5nUXVlc3Rpb25zID0gZmFsc2UKICAgICAgICB0aGlzLmltcG9ydFByb2dyZXNzID0gMAogICAgICB9CiAgICB9LAoKICAgIC8vIOagvOW8j+WMlui/m+W6puaYvuekugogICAgZm9ybWF0UHJvZ3Jlc3MocGVyY2VudGFnZSkgewogICAgICBpZiAocGVyY2VudGFnZSA9PT0gMTAwKSB7CiAgICAgICAgcmV0dXJuICflr7zlhaXlrozmiJAnCiAgICAgIH0gZWxzZSBpZiAocGVyY2VudGFnZSA+PSA4MCkgewogICAgICAgIHJldHVybiAn5q2j5Zyo5L+d5a2YLi4uJwogICAgICB9IGVsc2UgaWYgKHBlcmNlbnRhZ2UgPj0gMzApIHsKICAgICAgICByZXR1cm4gJ+ato+WcqOWkhOeQhi4uLicKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJ+WHhuWkh+S4rS4uLicKICAgICAgfQogICAgfSwKCiAgICAvLyDliJ3lp4vljJblr4zmlofmnKznvJbovpHlmagKICAgIGluaXRSaWNoRWRpdG9yKCkgewogICAgICBpZiAodGhpcy5lZGl0b3JJbml0aWFsaXplZCkgewogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDmo4Dmn6VDS0VkaXRvcuaYr+WQpuWPr+eUqAogICAgICBpZiAoIXdpbmRvdy5DS0VESVRPUikgewogICAgICAgIHRoaXMuZmFsbGJhY2tUb1RleHRhcmVhKCkKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICAvLyDlpoLmnpznvJbovpHlmajlt7LlrZjlnKjvvIzlhYjplIDmr4EKICAgICAgICBpZiAodGhpcy5yaWNoRWRpdG9yKSB7CiAgICAgICAgICB0aGlzLnJpY2hFZGl0b3IuZGVzdHJveSgpCiAgICAgICAgICB0aGlzLnJpY2hFZGl0b3IgPSBudWxsCiAgICAgICAgfQoKICAgICAgICAvLyDnoa7kv53lrrnlmajlrZjlnKgKICAgICAgICBjb25zdCBlZGl0b3JDb250YWluZXIgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgncmljaC1lZGl0b3InKQogICAgICAgIGlmICghZWRpdG9yQ29udGFpbmVyKSB7CiAgICAgICAgICByZXR1cm4KICAgICAgICB9CgogICAgICAgIC8vIOWIm+W7unRleHRhcmVh5YWD57SgCiAgICAgICAgZWRpdG9yQ29udGFpbmVyLmlubmVySFRNTCA9ICc8dGV4dGFyZWEgaWQ9InJpY2gtZWRpdG9yLXRleHRhcmVhIiBuYW1lPSJyaWNoLWVkaXRvci10ZXh0YXJlYSI+PC90ZXh0YXJlYT4nCgogICAgICAgIC8vIOetieW+hURPTeabtOaWsOWQjuWIm+W7uue8lui+keWZqAogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIC8vIOajgOafpUNLRWRpdG9y5piv5ZCm5Y+v55SoCiAgICAgICAgICBpZiAoIXdpbmRvdy5DS0VESVRPUiB8fCAhd2luZG93LkNLRURJVE9SLnJlcGxhY2UpIHsKCiAgICAgICAgICAgIHRoaXMuc2hvd0ZhbGxiYWNrRWRpdG9yID0gdHJ1ZQogICAgICAgICAgICByZXR1cm4KICAgICAgICAgIH0KCiAgICAgICAgICB0cnkgewogICAgICAgICAgICAvLyDlhYjlsJ3or5XlrozmlbTphY3nva4KICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yID0gd2luZG93LkNLRURJVE9SLnJlcGxhY2UoJ3JpY2gtZWRpdG9yLXRleHRhcmVhJywgewogICAgICAgICAgICAgIGhlaWdodDogJ2NhbGMoMTAwdmggLSAyMDBweCknLCAvLyDlhajlsY/pq5jluqblh4/ljrvlpLTpg6jlkozlhbbku5blhYPntKDnmoTpq5jluqYKICAgICAgICAgICAgICB0b29sYmFyOiBbCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdzdHlsZXMnLCBpdGVtczogWydGb250U2l6ZSddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdiYXNpY3N0eWxlcycsIGl0ZW1zOiBbJ0JvbGQnLCAnSXRhbGljJywgJ1VuZGVybGluZScsICdTdHJpa2UnLCAnU3VwZXJzY3JpcHQnLCAnU3Vic2NyaXB0JywgJy0nLCAnUmVtb3ZlRm9ybWF0J10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ2NsaXBib2FyZCcsIGl0ZW1zOiBbJ0N1dCcsICdDb3B5JywgJ1Bhc3RlJywgJ1Bhc3RlVGV4dCddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdjb2xvcnMnLCBpdGVtczogWydUZXh0Q29sb3InLCAnQkdDb2xvciddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdwYXJhZ3JhcGgnLCBpdGVtczogWydKdXN0aWZ5TGVmdCcsICdKdXN0aWZ5Q2VudGVyJywgJ0p1c3RpZnlSaWdodCcsICdKdXN0aWZ5QmxvY2snXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAnZWRpdGluZycsIGl0ZW1zOiBbJ1VuZG8nLCAnUmVkbyddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdsaW5rcycsIGl0ZW1zOiBbJ0xpbmsnLCAnVW5saW5rJ10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ2luc2VydCcsIGl0ZW1zOiBbJ0ltYWdlJywgJ1NwZWNpYWxDaGFyJ10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ3Rvb2xzJywgaXRlbXM6IFsnTWF4aW1pemUnXSB9CiAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICByZW1vdmVCdXR0b25zOiAnJywKICAgICAgICAgICAgICBsYW5ndWFnZTogJ3poLWNuJywKICAgICAgICAgICAgICByZW1vdmVQbHVnaW5zOiAnZWxlbWVudHNwYXRoJywKICAgICAgICAgICAgICByZXNpemVfZW5hYmxlZDogZmFsc2UsCiAgICAgICAgICAgICAgZXh0cmFQbHVnaW5zOiAnZm9udCxjb2xvcmJ1dHRvbixqdXN0aWZ5LHNwZWNpYWxjaGFyLGltYWdlJywKICAgICAgICAgICAgICBhbGxvd2VkQ29udGVudDogdHJ1ZSwKICAgICAgICAgICAgICAvLyDlrZfkvZPlpKflsI/phY3nva4KICAgICAgICAgICAgICBmb250U2l6ZV9zaXplczogJzEyLzEycHg7MTQvMTRweDsxNi8xNnB4OzE4LzE4cHg7MjAvMjBweDsyMi8yMnB4OzI0LzI0cHg7MjYvMjZweDsyOC8yOHB4OzM2LzM2cHg7NDgvNDhweDs3Mi83MnB4JywKICAgICAgICAgICAgICBmb250U2l6ZV9kZWZhdWx0TGFiZWw6ICcxNHB4JywKICAgICAgICAgICAgICAvLyDpopzoibLphY3nva4KICAgICAgICAgICAgICBjb2xvckJ1dHRvbl9lbmFibGVNb3JlOiB0cnVlLAogICAgICAgICAgICAgIGNvbG9yQnV0dG9uX2NvbG9yczogJ0NGNUQ0RSw0NTQ1NDUsRkZGLENDQyxEREQsQ0NFQUVFLDY2QUIxNicsCiAgICAgICAgICAgICAgLy8g5Zu+5YOP5LiK5Lyg6YWN572uIC0g5Y+C6ICD5oKo5o+Q5L6b55qE5qCH5YeG6YWN572uCiAgICAgICAgICAgICAgZmlsZWJyb3dzZXJVcGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL2NvbW1vbi91cGxvYWRJbWFnZScsCiAgICAgICAgICAgICAgaW1hZ2VfcHJldmlld1RleHQ6ICcgJywKICAgICAgICAgICAgICAvLyDorr7nva7ln7rnoYDot6/lvoTvvIzorqnnm7jlr7not6/lvoTog73mraPnoa7op6PmnpDliLDlkI7nq6/mnI3liqHlmagKICAgICAgICAgICAgICBiYXNlSHJlZjogJ2h0dHA6Ly9sb2NhbGhvc3Q6ODgwMi8nLAogICAgICAgICAgICAgIC8vIOWbvuWDj+aPkuWFpemFjee9rgogICAgICAgICAgICAgIGltYWdlX3ByZXZpZXdUZXh0OiAn6aKE6KeI5Yy65Z+fJywKICAgICAgICAgICAgICBpbWFnZV9yZW1vdmVMaW5rQnlFbXB0eVVSTDogdHJ1ZSwKICAgICAgICAgICAgICAvLyDpmpDol4/kuI3pnIDopoHnmoTmoIfnrb7pobXvvIzlj6rkv53nlZnkuIrkvKDlkozlm77lg4/kv6Hmga8KICAgICAgICAgICAgICByZW1vdmVEaWFsb2dUYWJzOiAnaW1hZ2U6TGluaztpbWFnZTphZHZhbmNlZCcsCiAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgIGluc3RhbmNlUmVhZHk6IGZ1bmN0aW9uKGV2dCkgewogICAgICAgICAgICAgICAgICBjb25zdCBlZGl0b3IgPSBldnQuZWRpdG9yCiAgICAgICAgICAgICAgICAgIGVkaXRvci5vbignZGlhbG9nU2hvdycsIGZ1bmN0aW9uKGV2dCkgewogICAgICAgICAgICAgICAgICAgIGNvbnN0IGRpYWxvZyA9IGV2dC5kYXRhCiAgICAgICAgICAgICAgICAgICAgaWYgKGRpYWxvZy5nZXROYW1lKCkgPT09ICdpbWFnZScpIHsKICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjaGVja0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cmxGaWVsZCA9IGRpYWxvZy5nZXRDb250ZW50RWxlbWVudCgnaW5mbycsICd0eHRVcmwnKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHVybEZpZWxkICYmIHVybEZpZWxkLmdldFZhbHVlKCkgJiYgdXJsRmllbGQuZ2V0VmFsdWUoKS5zdGFydHNXaXRoKCcvJykpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChjaGVja0ludGVydmFsKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaWFsb2cuc2VsZWN0UGFnZSgnaW5mbycpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5b+955Wl6ZSZ6K+vCiAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICB9LCA1MDApCiAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gY2xlYXJJbnRlcnZhbChjaGVja0ludGVydmFsKSwgMTAwMDApCiAgICAgICAgICAgICAgICAgICAgICB9LCAxMDAwKQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICB0aGlzLmZhbGxiYWNrVG9UZXh0YXJlYSgpCiAgICAgICAgICAgIHJldHVybgogICAgICAgICAgfQoKICAgICAgICAgIC8vIOebkeWQrOWGheWuueWPmOWMliAtIOS9v+eUqOmYsuaKluS8mOWMluaAp+iDvQogICAgICAgICAgaWYgKHRoaXMucmljaEVkaXRvciAmJiB0aGlzLnJpY2hFZGl0b3Iub24pIHsKICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLm9uKCdjaGFuZ2UnLCAoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5kZWJvdW5jZUVkaXRvckNvbnRlbnRDaGFuZ2UoKQogICAgICAgICAgICB9KQoKICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLm9uKCdrZXknLCAoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5kZWJvdW5jZUVkaXRvckNvbnRlbnRDaGFuZ2UoKQogICAgICAgICAgICB9KQoKICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLm9uKCdpbnN0YW5jZVJlYWR5JywgKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQgPSB0cnVlCiAgICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLnNldERhdGEoJycpCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfSkKCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgdGhpcy5mYWxsYmFja1RvVGV4dGFyZWEoKQogICAgICB9CiAgICB9LAoKICAgIC8vIOWkhOeQhue8lui+keWZqOWGheWuueWPmOWMlu+8iOmYsuaKluWQjuaJp+ihjO+8iQogICAgaGFuZGxlRWRpdG9yQ29udGVudENoYW5nZURlYm91bmNlZCgpIHsKICAgICAgaWYgKCF0aGlzLnJpY2hFZGl0b3IgfHwgIXRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQpIHsKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCByYXdDb250ZW50ID0gdGhpcy5yaWNoRWRpdG9yLmdldERhdGEoKQogICAgICAgIGNvbnN0IGNvbnRlbnRXaXRoUmVsYXRpdmVVcmxzID0gdGhpcy5jb252ZXJ0VXJsc1RvUmVsYXRpdmUocmF3Q29udGVudCkKICAgICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSB0aGlzLnByZXNlcnZlUmljaFRleHRGb3JtYXR0aW5nKGNvbnRlbnRXaXRoUmVsYXRpdmVVcmxzKQogICAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gdGhpcy5zdHJpcEh0bWxUYWdzS2VlcEltYWdlcyhjb250ZW50V2l0aFJlbGF0aXZlVXJscykKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLndhcm4oJ+e8lui+keWZqOWGheWuueWkhOeQhuWksei0pTonLCBlcnJvcikKICAgICAgfQogICAgfSwKCiAgICAvLyDlm57pgIDliLDmma7pgJrmlofmnKzmoYYKICAgIGZhbGxiYWNrVG9UZXh0YXJlYSgpIHsKICAgICAgY29uc3QgZWRpdG9yQ29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3JpY2gtZWRpdG9yJykKICAgICAgaWYgKGVkaXRvckNvbnRhaW5lcikgewogICAgICAgIGNvbnN0IHRleHRhcmVhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgndGV4dGFyZWEnKQogICAgICAgIHRleHRhcmVhLmNsYXNzTmFtZSA9ICdmYWxsYmFjay10ZXh0YXJlYScKICAgICAgICB0ZXh0YXJlYS5wbGFjZWhvbGRlciA9ICfor7flnKjmraTlpITnspjotLTmiJbovpPlhaXpopjnm67lhoXlrrkuLi4nCiAgICAgICAgdGV4dGFyZWEudmFsdWUgPSAnJyAvLyDnoa7kv53mlofmnKzmoYbkuLrnqboKICAgICAgICB0ZXh0YXJlYS5zdHlsZS5jc3NUZXh0ID0gJ3dpZHRoOiAxMDAlOyBoZWlnaHQ6IDQwMHB4OyBib3JkZXI6IDFweCBzb2xpZCAjZGRkOyBwYWRkaW5nOiAxMHB4OyBmb250LWZhbWlseTogIkNvdXJpZXIgTmV3IiwgbW9ub3NwYWNlOyBmb250LXNpemU6IDE0cHg7IGxpbmUtaGVpZ2h0OiAxLjY7IHJlc2l6ZTogbm9uZTsnCgogICAgICAgIC8vIOebkeWQrOWGheWuueWPmOWMliAtIOS9v+eUqOmYsuaKluS8mOWMluaAp+iDvQogICAgICAgIHRleHRhcmVhLmFkZEV2ZW50TGlzdGVuZXIoJ2lucHV0JywgKGUpID0+IHsKICAgICAgICAgIC8vIOeri+WNs+abtOaWsOWGheWuue+8jOS9humYsuaKluino+aekAogICAgICAgICAgdGhpcy5kb2N1bWVudENvbnRlbnQgPSBlLnRhcmdldC52YWx1ZQogICAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gZS50YXJnZXQudmFsdWUKICAgICAgICB9KQoKICAgICAgICBlZGl0b3JDb250YWluZXIuaW5uZXJIVE1MID0gJycKICAgICAgICBlZGl0b3JDb250YWluZXIuYXBwZW5kQ2hpbGQodGV4dGFyZWEpCiAgICAgICAgdGhpcy5lZGl0b3JJbml0aWFsaXplZCA9IHRydWUKICAgICAgfQogICAgfSwKCgoKICAgIC8vIOiuvue9rue8lui+keWZqOWGheWuuQogICAgc2V0RWRpdG9yQ29udGVudChjb250ZW50KSB7CiAgICAgIGlmICh0aGlzLnJpY2hFZGl0b3IgJiYgdGhpcy5lZGl0b3JJbml0aWFsaXplZCkgewogICAgICAgIHRoaXMucmljaEVkaXRvci5zZXREYXRhKGNvbnRlbnQpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5kb2N1bWVudENvbnRlbnQgPSBjb250ZW50CiAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gY29udGVudAogICAgICB9CiAgICB9LAoKCgogICAgLy8g6Ziy5oqW5Ye95pWwIC0g5LyY5YyW54mI5pys77yM5pSv5oyB5Y+W5raICiAgICBkZWJvdW5jZShmdW5jLCB3YWl0KSB7CiAgICAgIGxldCB0aW1lb3V0CiAgICAgIGNvbnN0IGRlYm91bmNlZCA9IGZ1bmN0aW9uIGV4ZWN1dGVkRnVuY3Rpb24oLi4uYXJncykgewogICAgICAgIGNvbnN0IGxhdGVyID0gKCkgPT4gewogICAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpCiAgICAgICAgICB0aW1lb3V0ID0gbnVsbAogICAgICAgICAgZnVuYy5hcHBseSh0aGlzLCBhcmdzKQogICAgICAgIH0KICAgICAgICBjbGVhclRpbWVvdXQodGltZW91dCkKICAgICAgICB0aW1lb3V0ID0gc2V0VGltZW91dChsYXRlciwgd2FpdCkKICAgICAgfQoKICAgICAgLy8g5re75Yqg5Y+W5raI5pa55rOVCiAgICAgIGRlYm91bmNlZC5jYW5jZWwgPSBmdW5jdGlvbigpIHsKICAgICAgICBjbGVhclRpbWVvdXQodGltZW91dCkKICAgICAgICB0aW1lb3V0ID0gbnVsbAogICAgICB9CgogICAgICByZXR1cm4gZGVib3VuY2VkCiAgICB9LAoKICAgIC8vIOWwhue8lui+keWZqOWGheWuueS4reeahOWujOaVtFVSTOi9rOaNouS4uuebuOWvuei3r+W+hAogICAgY29udmVydFVybHNUb1JlbGF0aXZlKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50KSByZXR1cm4gY29udGVudAoKICAgICAgLy8g5Yy56YWN5b2T5YmN5Z+f5ZCN55qE5a6M5pW0VVJM5bm26L2s5o2i5Li655u45a+56Lev5b6ECiAgICAgIGNvbnN0IGN1cnJlbnRPcmlnaW4gPSB3aW5kb3cubG9jYXRpb24ub3JpZ2luCiAgICAgIGNvbnN0IHVybFJlZ2V4ID0gbmV3IFJlZ0V4cChjdXJyZW50T3JpZ2luLnJlcGxhY2UoL1suKis/XiR7fSgpfFtcXVxcXS9nLCAnXFwkJicpICsgJygvW14iXCdcXHM+XSopJywgJ2cnKQoKICAgICAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSh1cmxSZWdleCwgJyQxJykKICAgIH0sCgogICAgLy8g6Kej5p6Q5paH5qGjCiAgICBwYXJzZURvY3VtZW50KCkgewogICAgICBpZiAoIXRoaXMuZG9jdW1lbnRDb250ZW50LnRyaW0oKSkgewogICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBwYXJzZVJlc3VsdCA9IHRoaXMucGFyc2VRdWVzdGlvbkNvbnRlbnQodGhpcy5kb2N1bWVudENvbnRlbnQpCiAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBwYXJzZVJlc3VsdC5xdWVzdGlvbnMubWFwKHF1ZXN0aW9uID0+ICh7CiAgICAgICAgICAuLi5xdWVzdGlvbiwKICAgICAgICAgIGNvbGxhcHNlZDogZmFsc2UKICAgICAgICB9KSkKICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gcGFyc2VSZXN1bHQuZXJyb3JzCiAgICAgICAgLy8g6K6w5b2V6Kej5p6Q5oiQ5Yqf55qE5YaF5a6577yM6YG/5YWN6YeN5aSN6Kej5p6QCiAgICAgICAgdGhpcy5sYXN0UGFyc2VkQ29udGVudCA9IHRoaXMuZG9jdW1lbnRDb250ZW50CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFsn6Kej5p6Q5aSx6LSl77yaJyArIGVycm9yLm1lc3NhZ2VdCiAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBbXQogICAgICB9CiAgICB9LAoKICAgIC8vIOino+aekOmimOebruWGheWuuSAtIOS8mOWMlueJiOacrO+8jOabtOWKoOWBpeWjrgogICAgcGFyc2VRdWVzdGlvbkNvbnRlbnQoY29udGVudCkgewogICAgICBjb25zdCBxdWVzdGlvbnMgPSBbXQogICAgICBjb25zdCBlcnJvcnMgPSBbXQoKICAgICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewoKICAgICAgICByZXR1cm4geyBxdWVzdGlvbnMsIGVycm9yczogWyfop6PmnpDlhoXlrrnkuLrnqbrmiJbmoLzlvI/kuI3mraPnoa4nXSB9CiAgICAgIH0KCiAgICAgIHRyeSB7CgoKICAgICAgICBjb25zdCB0ZXh0Q29udGVudCA9IHRoaXMuc3RyaXBIdG1sVGFnc0tlZXBJbWFnZXMoY29udGVudCkKCiAgICAgICAgaWYgKCF0ZXh0Q29udGVudCB8fCB0ZXh0Q29udGVudC50cmltKCkubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICByZXR1cm4geyBxdWVzdGlvbnMsIGVycm9yczogWyflpITnkIblkI7nmoTlhoXlrrnkuLrnqbonXSB9CiAgICAgICAgfQoKICAgICAgICBjb25zdCBsaW5lcyA9IHRleHRDb250ZW50LnNwbGl0KCdcbicpLm1hcChsaW5lID0+IGxpbmUudHJpbSgpKS5maWx0ZXIobGluZSA9PiBsaW5lLmxlbmd0aCA+IDApCgogICAgICAgIGlmIChsaW5lcy5sZW5ndGggPT09IDApIHsKICAgICAgICAgIHJldHVybiB7IHF1ZXN0aW9ucywgZXJyb3JzOiBbJ+ayoeacieacieaViOeahOWGheWuueihjCddIH0KICAgICAgICB9CgoKCiAgICAgICAgbGV0IGN1cnJlbnRRdWVzdGlvbkxpbmVzID0gW10KICAgICAgICBsZXQgcXVlc3Rpb25OdW1iZXIgPSAwCgogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXQoKICAgICAgICAgIC8vIOajgOafpeaYr+WQpuaYr+mimOebruW8gOWni+ihjO+8muaVsOWtl+OAgVvpopjnm67nsbvlnotdIOaIliBb6aKY55uu57G75Z6LXQogICAgICAgICAgY29uc3QgaXNRdWVzdGlvblN0YXJ0ID0gdGhpcy5pc1F1ZXN0aW9uU3RhcnRMaW5lKGxpbmUpIHx8IHRoaXMuaXNRdWVzdGlvblR5cGVTdGFydChsaW5lKQoKICAgICAgICAgIGlmIChpc1F1ZXN0aW9uU3RhcnQpIHsKICAgICAgICAgICAgLy8g5aaC5p6c5LmL5YmN5pyJ6aKY55uu5YaF5a6577yM5YWI5aSE55CG5LmL5YmN55qE6aKY55uuCiAgICAgICAgICAgIGlmIChjdXJyZW50UXVlc3Rpb25MaW5lcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgIGNvbnN0IHF1ZXN0aW9uVGV4dCA9IGN1cnJlbnRRdWVzdGlvbkxpbmVzLmpvaW4oJ1xuJykKICAgICAgICAgICAgICAgIGNvbnN0IHBhcnNlZFF1ZXN0aW9uID0gdGhpcy5wYXJzZVF1ZXN0aW9uRnJvbUxpbmVzKHF1ZXN0aW9uVGV4dCwgcXVlc3Rpb25OdW1iZXIpCiAgICAgICAgICAgICAgICBpZiAocGFyc2VkUXVlc3Rpb24pIHsKICAgICAgICAgICAgICAgICAgcXVlc3Rpb25zLnB1c2gocGFyc2VkUXVlc3Rpb24pCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgIGVycm9ycy5wdXNoKGDnrKwgJHtxdWVzdGlvbk51bWJlcn0g6aKY6Kej5p6Q5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC8vIOW8gOWni+aWsOmimOebrgogICAgICAgICAgICBjdXJyZW50UXVlc3Rpb25MaW5lcyA9IFtsaW5lXQogICAgICAgICAgICBxdWVzdGlvbk51bWJlcisrCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyDlpoLmnpzlvZPliY3lnKjlpITnkIbpopjnm67kuK3vvIzmt7vliqDliLDlvZPliY3popjnm64KICAgICAgICAgICAgaWYgKGN1cnJlbnRRdWVzdGlvbkxpbmVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICBjdXJyZW50UXVlc3Rpb25MaW5lcy5wdXNoKGxpbmUpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC8vIOWkhOeQhuacgOWQjuS4gOS4qumimOebrgogICAgICAgIGlmIChjdXJyZW50UXVlc3Rpb25MaW5lcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICBjb25zdCBxdWVzdGlvblRleHQgPSBjdXJyZW50UXVlc3Rpb25MaW5lcy5qb2luKCdcbicpCiAgICAgICAgICAgIGNvbnN0IHBhcnNlZFF1ZXN0aW9uID0gdGhpcy5wYXJzZVF1ZXN0aW9uRnJvbUxpbmVzKHF1ZXN0aW9uVGV4dCwgcXVlc3Rpb25OdW1iZXIpCiAgICAgICAgICAgIGlmIChwYXJzZWRRdWVzdGlvbikgewogICAgICAgICAgICAgIHF1ZXN0aW9ucy5wdXNoKHBhcnNlZFF1ZXN0aW9uKQogICAgICAgICAgICB9CiAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICBlcnJvcnMucHVzaChg56ysICR7cXVlc3Rpb25OdW1iZXJ9IOmimOino+aekOWksei0pTogJHtlcnJvci5tZXNzYWdlfWApCiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBlcnJvcnMucHVzaChg5paH5qGj6Kej5p6Q5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkKICAgICAgfQoKICAgICAgcmV0dXJuIHsgcXVlc3Rpb25zLCBlcnJvcnMgfQogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrpopjnm67lvIDlp4vooYwgLSDmjInnhafovpPlhaXop4TojIMKICAgIGlzUXVlc3Rpb25TdGFydExpbmUobGluZSkgewogICAgICAvLyDop4TojIPvvJrmr4/popjliY3pnaLpnIDopoHliqDkuIrpopjlj7fmoIfor4bvvIzpopjlj7flkI7pnaLpnIDopoHliqDkuIrnrKblj7fvvIg677ya44CBLu+8ju+8iQogICAgICAvLyDljLnphY3moLzlvI/vvJrmlbDlrZcgKyDnrKblj7coOu+8muOAgS7vvI4pICsg5Y+v6YCJ56m65qC8CiAgICAgIC8vIOS+i+Wmgu+8mjEuIDHjgIEgMe+8miAx77yOIOetiQogICAgICByZXR1cm4gL15cZCtbLjrvvJrvvI7jgIFdXHMqLy50ZXN0KGxpbmUpCiAgICB9LAoKICAgIC8vIOWIpOaWreaYr+WQpuS4uumimOWei+agh+azqOW8gOWni+ihjAogICAgaXNRdWVzdGlvblR5cGVTdGFydChsaW5lKSB7CiAgICAgIC8vIOWMuemFjeagvOW8j++8mlvpopjnm67nsbvlnotdCiAgICAgIC8vIOS+i+Wmgu+8mlvljZXpgInpophdIFvlpJrpgInpophdIFvliKTmlq3pophdIOetiQogICAgICByZXR1cm4gL15cWy4qP+mimFxdLy50ZXN0KGxpbmUpCiAgICB9LAoKICAgIC8vIOS7juihjOaVsOe7hOino+aekOWNleS4qumimOebriAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgcGFyc2VRdWVzdGlvbkZyb21MaW5lcyhxdWVzdGlvblRleHQpIHsKICAgICAgY29uc3QgbGluZXMgPSBxdWVzdGlvblRleHQuc3BsaXQoJ1xuJykubWFwKGxpbmUgPT4gbGluZS50cmltKCkpLmZpbHRlcihsaW5lID0+IGxpbmUubGVuZ3RoID4gMCkKCiAgICAgIGlmIChsaW5lcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+mimOebruWGheWuueS4uuepuicpCiAgICAgIH0KCiAgICAgIGxldCBxdWVzdGlvblR5cGUgPSAnanVkZ21lbnQnIC8vIOm7mOiupOWIpOaWremimAogICAgICBsZXQgcXVlc3Rpb25Db250ZW50ID0gJycKICAgICAgbGV0IGNvbnRlbnRTdGFydEluZGV4ID0gMAoKICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ6aKY5Z6L5qCH5rOo77yI5aaCIFvljZXpgInpophd44CBW+WkmumAiemimF3jgIFb5Yik5pat6aKYXe+8iQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2ldCiAgICAgICAgY29uc3QgdHlwZU1hdGNoID0gbGluZS5tYXRjaCgvXFsoLio/6aKYKVxdLykKICAgICAgICBpZiAodHlwZU1hdGNoKSB7CiAgICAgICAgICBjb25zdCB0eXBlVGV4dCA9IHR5cGVNYXRjaFsxXQoKICAgICAgICAgIC8vIOi9rOaNoumimOebruexu+WeiwogICAgICAgICAgaWYgKHR5cGVUZXh0LmluY2x1ZGVzKCfliKTmlq0nKSkgewogICAgICAgICAgICBxdWVzdGlvblR5cGUgPSAnanVkZ21lbnQnCiAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVUZXh0LmluY2x1ZGVzKCfljZXpgIknKSkgewogICAgICAgICAgICBxdWVzdGlvblR5cGUgPSAnc2luZ2xlJwogICAgICAgICAgfSBlbHNlIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn5aSa6YCJJykpIHsKICAgICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ211bHRpcGxlJwogICAgICAgICAgfSBlbHNlIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn5aGr56m6JykpIHsKICAgICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ2ZpbGwnCiAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVUZXh0LmluY2x1ZGVzKCfnroDnrZQnKSkgewogICAgICAgICAgICBxdWVzdGlvblR5cGUgPSAnZXNzYXknCiAgICAgICAgICB9CgogICAgICAgICAgLy8g5aaC5p6c6aKY5Z6L5qCH5rOo5ZKM6aKY55uu5YaF5a655Zyo5ZCM5LiA6KGMCiAgICAgICAgICBjb25zdCByZW1haW5pbmdDb250ZW50ID0gbGluZS5yZXBsYWNlKC9cWy4qP+mimFxdLywgJycpLnRyaW0oKQogICAgICAgICAgaWYgKHJlbWFpbmluZ0NvbnRlbnQpIHsKICAgICAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gcmVtYWluaW5nQ29udGVudAogICAgICAgICAgICBjb250ZW50U3RhcnRJbmRleCA9IGkgKyAxCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjb250ZW50U3RhcnRJbmRleCA9IGkgKyAxCiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5aaC5p6c5rKh5pyJ5om+5Yiw6aKY5Z6L5qCH5rOo77yM5LuO56ys5LiA6KGM5byA5aeL6Kej5p6QCiAgICAgIGlmIChjb250ZW50U3RhcnRJbmRleCA9PT0gMCkgewogICAgICAgIGNvbnRlbnRTdGFydEluZGV4ID0gMAogICAgICB9CgogICAgICAvLyDmj5Dlj5bpopjnm67lhoXlrrnvvIjku47popjlj7fooYzlvIDlp4vvvIkKICAgICAgZm9yIChsZXQgaSA9IGNvbnRlbnRTdGFydEluZGV4OyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgLy8g5aaC5p6c5piv6aKY5Y+36KGM77yM5o+Q5Y+W6aKY55uu5YaF5a6577yI56e76Zmk6aKY5Y+377yJCiAgICAgICAgaWYgKHRoaXMuaXNRdWVzdGlvblN0YXJ0TGluZShsaW5lKSkgewogICAgICAgICAgLy8g56e76Zmk6aKY5Y+377yM5o+Q5Y+W6aKY55uu5YaF5a65CiAgICAgICAgICBxdWVzdGlvbkNvbnRlbnQgPSBsaW5lLnJlcGxhY2UoL15cZCtbLjrvvJrvvI7jgIFdXHMqLywgJycpLnRyaW0oKQogICAgICAgICAgY29udGVudFN0YXJ0SW5kZXggPSBpICsgMQogICAgICAgICAgYnJlYWsKICAgICAgICB9IGVsc2UgaWYgKCFxdWVzdGlvbkNvbnRlbnQpIHsKICAgICAgICAgIC8vIOWmguaenOi/mOayoeaciemimOebruWGheWuue+8jOW9k+WJjeihjOWwseaYr+mimOebruWGheWuuQogICAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gbGluZQogICAgICAgICAgY29udGVudFN0YXJ0SW5kZXggPSBpICsgMQogICAgICAgICAgYnJlYWsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOe7p+e7reaUtumbhumimOebruWGheWuue+8iOebtOWIsOmBh+WIsOmAiemhueaIluetlOahiO+8iQogICAgICBmb3IgKGxldCBpID0gY29udGVudFN0YXJ0SW5kZXg7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXQoKICAgICAgICAvLyDlpoLmnpzpgYfliLDpgInpobnooYzjgIHnrZTmoYjooYzjgIHop6PmnpDooYzmiJbpmr7luqbooYzvvIzlgZzmraLmlLbpm4bpopjnm67lhoXlrrkKICAgICAgICBpZiAodGhpcy5pc09wdGlvbkxpbmUobGluZSkgfHwgdGhpcy5pc0Fuc3dlckxpbmUobGluZSkgfHwKICAgICAgICAgICAgdGhpcy5pc0V4cGxhbmF0aW9uTGluZShsaW5lKSB8fCB0aGlzLmlzRGlmZmljdWx0eUxpbmUobGluZSkpIHsKICAgICAgICAgIGJyZWFrCiAgICAgICAgfQoKICAgICAgICAvLyDnu6fnu63mt7vliqDliLDpopjnm67lhoXlrrnvvIzkvYbopoHnoa7kv53kuI3ljIXlkKvpopjlj7cKICAgICAgICBsZXQgY2xlYW5MaW5lID0gbGluZQogICAgICAgIC8vIOWmguaenOi/meihjOi/mOWMheWQq+mimOWPt++8jOenu+mZpOWugwogICAgICAgIGlmICh0aGlzLmlzUXVlc3Rpb25TdGFydExpbmUobGluZSkpIHsKICAgICAgICAgIGNsZWFuTGluZSA9IGxpbmUucmVwbGFjZSgvXlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpCiAgICAgICAgfQoKICAgICAgICBpZiAoY2xlYW5MaW5lKSB7CiAgICAgICAgICBpZiAocXVlc3Rpb25Db250ZW50KSB7CiAgICAgICAgICAgIHF1ZXN0aW9uQ29udGVudCArPSAnXG4nICsgY2xlYW5MaW5lCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBxdWVzdGlvbkNvbnRlbnQgPSBjbGVhbkxpbmUKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIGlmICghcXVlc3Rpb25Db250ZW50KSB7CiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfml6Dms5Xmj5Dlj5bpopjnm67lhoXlrrknKQogICAgICB9CgogICAgICAvLyDmnIDnu4jmuIXnkIbvvJrnoa7kv53popjnm67lhoXlrrnkuI3ljIXlkKvpopjlj7cKICAgICAgbGV0IGZpbmFsUXVlc3Rpb25Db250ZW50ID0gcXVlc3Rpb25Db250ZW50LnRyaW0oKQogICAgICAvLyDkvb/nlKjmm7TlvLrnmoTmuIXnkIbpgLvovpHvvIzlpJrmrKHmuIXnkIbnoa7kv53lvbvlupXnp7vpmaTpopjlj7cKICAgICAgd2hpbGUgKC9eXHMqXGQrWy4677ya77yO44CBXS8udGVzdChmaW5hbFF1ZXN0aW9uQ29udGVudCkpIHsKICAgICAgICBmaW5hbFF1ZXN0aW9uQ29udGVudCA9IGZpbmFsUXVlc3Rpb25Db250ZW50LnJlcGxhY2UoL15ccypcZCtbLjrvvJrvvI7jgIFdXHMqLywgJycpLnRyaW0oKQogICAgICB9CgogICAgICAvLyDpop3lpJbmuIXnkIbvvJrnp7vpmaTlj6/og73nmoRIVE1M5qCH562+5YaF55qE6aKY5Y+3CiAgICAgIGlmIChmaW5hbFF1ZXN0aW9uQ29udGVudC5pbmNsdWRlcygnPCcpKSB7CiAgICAgICAgZmluYWxRdWVzdGlvbkNvbnRlbnQgPSB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKGZpbmFsUXVlc3Rpb25Db250ZW50KQogICAgICB9CgogICAgICBjb25zdCBxdWVzdGlvbiA9IHsKICAgICAgICBxdWVzdGlvblR5cGU6IHF1ZXN0aW9uVHlwZSwKICAgICAgICB0eXBlOiBxdWVzdGlvblR5cGUsCiAgICAgICAgdHlwZU5hbWU6IHRoaXMuZ2V0VHlwZURpc3BsYXlOYW1lKHF1ZXN0aW9uVHlwZSksCiAgICAgICAgcXVlc3Rpb25Db250ZW50OiBmaW5hbFF1ZXN0aW9uQ29udGVudCwKICAgICAgICBjb250ZW50OiBmaW5hbFF1ZXN0aW9uQ29udGVudCwKICAgICAgICBkaWZmaWN1bHR5OiAnJywgLy8g5LiN6K6+572u6buY6K6k5YC8CiAgICAgICAgZXhwbGFuYXRpb246ICcnLAogICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgIGNvcnJlY3RBbnN3ZXI6ICcnLAogICAgICAgIGNvbGxhcHNlZDogZmFsc2UgIC8vIOm7mOiupOWxleW8gAogICAgICB9CgogICAgICAvLyDop6PmnpDpgInpobnvvIjlr7nkuo7pgInmi6npopjvvIkKICAgICAgY29uc3Qgb3B0aW9uUmVzdWx0ID0gdGhpcy5wYXJzZU9wdGlvbnNGcm9tTGluZXMobGluZXMsIDApCiAgICAgIHF1ZXN0aW9uLm9wdGlvbnMgPSBvcHRpb25SZXN1bHQub3B0aW9ucwoKICAgICAgLy8g5qC55o2u6YCJ6aG55pWw6YeP5o6o5pat6aKY55uu57G75Z6L77yI5aaC5p6c5LmL5YmN5rKh5pyJ5piO56Gu5qCH5rOo77yJCiAgICAgIGlmIChxdWVzdGlvblR5cGUgPT09ICdqdWRnbWVudCcgJiYgcXVlc3Rpb24ub3B0aW9ucy5sZW5ndGggPiAwKSB7CiAgICAgICAgLy8g5aaC5p6c5pyJ6YCJ6aG577yM5o6o5pat5Li66YCJ5oup6aKYCiAgICAgICAgcXVlc3Rpb25UeXBlID0gJ3NpbmdsZScgIC8vIOm7mOiupOS4uuWNlemAiemimAogICAgICAgIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSA9IHF1ZXN0aW9uVHlwZQogICAgICAgIHF1ZXN0aW9uLnR5cGUgPSBxdWVzdGlvblR5cGUKICAgICAgICBxdWVzdGlvbi50eXBlTmFtZSA9IHRoaXMuZ2V0VHlwZURpc3BsYXlOYW1lKHF1ZXN0aW9uVHlwZSkKICAgICAgfQoKICAgICAgLy8g6Kej5p6Q562U5qGI44CB6Kej5p6Q44CB6Zq+5bqmCiAgICAgIHRoaXMucGFyc2VRdWVzdGlvbk1ldGFGcm9tTGluZXMobGluZXMsIHF1ZXN0aW9uKQoKICAgICAgLy8g5qC55o2u562U5qGI6ZW/5bqm6L+b5LiA5q2l5o6o5pat6YCJ5oup6aKY57G75Z6LCiAgICAgIGlmIChxdWVzdGlvblR5cGUgPT09ICdzaW5nbGUnICYmIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIgJiYgcXVlc3Rpb24uY29ycmVjdEFuc3dlci5sZW5ndGggPiAxKSB7CiAgICAgICAgLy8g5aaC5p6c562U5qGI5YyF5ZCr5aSa5Liq5a2X5q+N77yM5o6o5pat5Li65aSa6YCJ6aKYCiAgICAgICAgaWYgKC9eW0EtWl17Mix9JC8udGVzdChxdWVzdGlvbi5jb3JyZWN0QW5zd2VyKSkgewogICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ211bHRpcGxlJwogICAgICAgICAgcXVlc3Rpb24ucXVlc3Rpb25UeXBlID0gcXVlc3Rpb25UeXBlCiAgICAgICAgICBxdWVzdGlvbi50eXBlID0gcXVlc3Rpb25UeXBlCiAgICAgICAgICBxdWVzdGlvbi50eXBlTmFtZSA9IHRoaXMuZ2V0VHlwZURpc3BsYXlOYW1lKHF1ZXN0aW9uVHlwZSkKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOacgOe7iOa4heeQhu+8muehruS/nemimOebruWGheWuueWujOWFqOayoeaciemimOWPt+WSjOmimOWei+agh+ivhgogICAgICBxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQgPSB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCkKICAgICAgcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50ID0gdGhpcy5yZW1vdmVRdWVzdGlvblR5cGUocXVlc3Rpb24ucXVlc3Rpb25Db250ZW50KQogICAgICBxdWVzdGlvbi5jb250ZW50ID0gcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50CgogICAgICByZXR1cm4gcXVlc3Rpb24KICAgIH0sCgogICAgLy8g5Yik5pat5piv5ZCm5Li66YCJ6aG56KGMIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBpc09wdGlvbkxpbmUobGluZSkgewogICAgICAvLyDop4TojIPvvJrpgInpobnmoLzlvI/vvIhBOu+8ie+8jOWtl+avjeWPr+S7peS4ukHliLBa55qE5Lu75oSP5aSn5bCP5YaZ5a2X5q+N77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6IjrvvJrjgIEu77yOIuWFtuS4reS5i+S4gAogICAgICAvLyDkuKXmoLzpqozor4HvvJrpgb/lhY3or6/lsIbpopjnm67lhoXlrrnkuK3nmoTlrZfmr40r56ym5Y+36K+G5Yir5Li66YCJ6aG5CiAgICAgIGlmICghbGluZSB8fCBsaW5lLmxlbmd0aCA+IDIwMCkgewogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CgogICAgICBjb25zdCBtYXRjaCA9IGxpbmUubWF0Y2goL14oW0EtWmEtel0pWy4677ya77yO44CBXVxzKiguKikvKQogICAgICBpZiAobWF0Y2gpIHsKICAgICAgICBjb25zdCBvcHRpb25LZXkgPSBtYXRjaFsxXS50b1VwcGVyQ2FzZSgpCiAgICAgICAgY29uc3Qgb3B0aW9uQ29udGVudCA9IG1hdGNoWzJdID8gbWF0Y2hbMl0udHJpbSgpIDogJycKCiAgICAgICAgLy8g5Lil5qC86aqM6K+B5p2h5Lu277yaCiAgICAgICAgLy8gMS4g6YCJ6aG55a2X5q+N5b+F6aG75pivQS1a5Y2V5Liq5a2X5q+NCiAgICAgICAgLy8gMi4g6YCJ6aG55YaF5a656ZW/5bqm5ZCI55CG77yIMS0xMDDlrZfnrKbvvIkKICAgICAgICAvLyAzLiDmjpLpmaTmmI7mmL7nmoTpopjnm67lhoXlrrnmj4/ov7DvvIjlpoLljIXlkKsi6KGo56S6IuOAgSLmlbDmja4i562J6K+N5rGH55qE6ZW/5Y+l77yJCiAgICAgICAgaWYgKC9eW0EtWl0kLy50ZXN0KG9wdGlvbktleSkgJiYgb3B0aW9uQ29udGVudC5sZW5ndGggPiAwICYmIG9wdGlvbkNvbnRlbnQubGVuZ3RoIDw9IDEwMCkgewogICAgICAgICAgLy8g5o6S6Zmk5piO5pi+55qE6aKY55uu5YaF5a655o+P6L+wCiAgICAgICAgICBjb25zdCBleGNsdWRlUGF0dGVybnMgPSBbCiAgICAgICAgICAgIC/ooajnpLouKj/mlbDmja4vLCAgICAgLy8g5o6S6ZmkIuihqOekui4uLuaVsOaNriLov5nnsbvmj4/ov7AKICAgICAgICAgICAgL+S4gOiIrOeUqC4qP+aIli8sICAgICAgLy8g5o6S6ZmkIuS4gOiIrOeUqC4uLuaIliLov5nnsbvmj4/ov7AKICAgICAgICAgICAgL+mAmuW4uC4qP+adpS8sICAgICAgIC8vIOaOkumZpCLpgJrluLguLi7mnaUi6L+Z57G75o+P6L+wCiAgICAgICAgICAgIC/lj6/ku6UuKj/ov5vooYwvLCAgICAgLy8g5o6S6ZmkIuWPr+S7pS4uLui/m+ihjCLov5nnsbvmj4/ov7AKICAgICAgICAgICAgLy4qP+WdkOaghy4qP+ihqOekui8gICAvLyDmjpLpmaQi5Z2Q5qCHLi4u6KGo56S6Iui/meexu+aPj+i/sAogICAgICAgICAgXQoKICAgICAgICAgIGNvbnN0IGlzRGVzY3JpcHRpdmVUZXh0ID0gZXhjbHVkZVBhdHRlcm5zLnNvbWUocGF0dGVybiA9PiBwYXR0ZXJuLnRlc3Qob3B0aW9uQ29udGVudCkpCiAgICAgICAgICByZXR1cm4gIWlzRGVzY3JpcHRpdmVUZXh0CiAgICAgICAgfQogICAgICB9CiAgICAgIHJldHVybiBmYWxzZQogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrnrZTmoYjooYwgLSDmjInnhafovpPlhaXop4TojIMKICAgIGlzQW5zd2VyTGluZShsaW5lKSB7CiAgICAgIC8vIOinhOiMg++8muaYvuW8j+agh+azqOagvOW8j++8iOetlOahiO+8mu+8ie+8jOWGkuWPt+WPr+S7peabv+aNouS4uiAiOu+8muOAgSLlhbbkuK3kuYvkuIAKICAgICAgcmV0dXJuIC9e562U5qGIWy4677ya44CBXVxzKi8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrop6PmnpDooYwgLSDmjInnhafovpPlhaXop4TojIMKICAgIGlzRXhwbGFuYXRpb25MaW5lKGxpbmUpIHsKICAgICAgLy8g6KeE6IyD77ya6Kej5p6Q5qC85byP77yI6Kej5p6Q77ya77yJ77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6ICI677ya44CBIuWFtuS4reS5i+S4gAogICAgICByZXR1cm4gL17op6PmnpBbLjrvvJrjgIFdXHMqLy50ZXN0KGxpbmUpCiAgICB9LAoKICAgIC8vIOWIpOaWreaYr+WQpuS4uumavuW6puihjCAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgaXNEaWZmaWN1bHR5TGluZShsaW5lKSB7CiAgICAgIC8vIOinhOiMg++8mumavuW6puagvOW8j++8iOmavuW6pu+8mu+8ie+8jOWGkuWPt+WPr+S7peabv+aNouS4uiAiOu+8muOAgSLlhbbkuK3kuYvkuIAKICAgICAgcmV0dXJuIC9e6Zq+5bqmWy4677ya44CBXVxzKi8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDojrflj5bpopjnm67nsbvlnovmmL7npLrlkI3np7AKICAgIGdldFR5cGVEaXNwbGF5TmFtZSh0eXBlKSB7CiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7CiAgICAgICAgJ2p1ZGdtZW50JzogJ+WIpOaWremimCcsCiAgICAgICAgJ3NpbmdsZSc6ICfljZXpgInpopgnLAogICAgICAgICdtdWx0aXBsZSc6ICflpJrpgInpopgnLAogICAgICAgICdmaWxsJzogJ+Whq+epuumimCcsCiAgICAgICAgJ2Vzc2F5JzogJ+eugOetlOmimCcKICAgICAgfQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCAn5Yik5pat6aKYJwogICAgfSwKCiAgICAvLyDlpITnkIblm77niYfot6/lvoTvvIzlsIbnm7jlr7not6/lvoTovazmjaLkuLrlrozmlbTot6/lvoQKICAgIHByb2Nlc3NJbWFnZVBhdGhzKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewogICAgICAgIHJldHVybiAnJwogICAgICB9CgogICAgICB0cnkgewogICAgICAgIGNvbnN0IHByb2Nlc3NlZENvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxpbWcoW14+XSo/KXNyYz0iKFteIl0qPykiKFtePl0qPyk+L2csIChtYXRjaCwgYmVmb3JlLCBzcmMsIGFmdGVyKSA9PiB7CiAgICAgICAgICBpZiAoIXNyYykgcmV0dXJuIG1hdGNoCgogICAgICAgICAgaWYgKHNyYy5zdGFydHNXaXRoKCdodHRwOi8vJykgfHwgc3JjLnN0YXJ0c1dpdGgoJ2h0dHBzOi8vJykgfHwgc3JjLnN0YXJ0c1dpdGgoJ2RhdGE6JykpIHsKICAgICAgICAgICAgcmV0dXJuIG1hdGNoCiAgICAgICAgICB9CgogICAgICAgICAgY29uc3QgZnVsbFNyYyA9ICdodHRwOi8vbG9jYWxob3N0Ojg4MDInICsgKHNyYy5zdGFydHNXaXRoKCcvJykgPyBzcmMgOiAnLycgKyBzcmMpCiAgICAgICAgICByZXR1cm4gYDxpbWcke2JlZm9yZX1zcmM9IiR7ZnVsbFNyY30iJHthZnRlcn0+YAogICAgICAgIH0pCgogICAgICAgIHJldHVybiBwcm9jZXNzZWRDb250ZW50CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgcmV0dXJuIGNvbnRlbnQKICAgICAgfQogICAgfSwKCiAgICAvLyDkv53nlZnlr4zmlofmnKzmoLzlvI/nlKjkuo7pooTop4jmmL7npLoKICAgIHByZXNlcnZlUmljaFRleHRGb3JtYXR0aW5nKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewogICAgICAgIHJldHVybiAnJwogICAgICB9CgogICAgICB0cnkgewogICAgICAgIC8vIOS/neeVmeW4uOeUqOeahOWvjOaWh+acrOagvOW8j+agh+etvgogICAgICAgIGxldCBwcm9jZXNzZWRDb250ZW50ID0gY29udGVudAogICAgICAgICAgLy8g6L2s5o2i55u45a+56Lev5b6E55qE5Zu+54mHCiAgICAgICAgICAucmVwbGFjZSgvPGltZyhbXj5dKj8pc3JjPSIoW14iXSo/KSIoW14+XSo/KT4vZ2ksIChtYXRjaCwgYmVmb3JlLCBzcmMsIGFmdGVyKSA9PiB7CiAgICAgICAgICAgIGlmICghc3JjLnN0YXJ0c1dpdGgoJ2h0dHAnKSAmJiAhc3JjLnN0YXJ0c1dpdGgoJ2RhdGE6JykpIHsKICAgICAgICAgICAgICBjb25zdCBmdWxsU3JjID0gdGhpcy5wcm9jZXNzSW1hZ2VQYXRocyhzcmMpCiAgICAgICAgICAgICAgcmV0dXJuIGA8aW1nJHtiZWZvcmV9c3JjPSIke2Z1bGxTcmN9IiR7YWZ0ZXJ9PmAKICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gbWF0Y2gKICAgICAgICAgIH0pCiAgICAgICAgICAvLyDkv53nlZnmrrXokL3nu5PmnoQKICAgICAgICAgIC5yZXBsYWNlKC88cFtePl0qPi9naSwgJzxwPicpCiAgICAgICAgICAucmVwbGFjZSgvPFwvcD4vZ2ksICc8L3A+JykKICAgICAgICAgIC8vIOS/neeVmeaNouihjAogICAgICAgICAgLnJlcGxhY2UoLzxiclxzKlwvPz4vZ2ksICc8YnI+JykKICAgICAgICAgIC8vIOa4heeQhuWkmuS9meeahOepuueZveauteiQvQogICAgICAgICAgLnJlcGxhY2UoLzxwPlxzKjxcL3A+L2dpLCAnJykKICAgICAgICAgIC5yZXBsYWNlKC8oPHA+W1xzXG5dKjxcL3A+KS9naSwgJycpCgogICAgICAgIHJldHVybiBwcm9jZXNzZWRDb250ZW50LnRyaW0oKQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHJldHVybiBjb250ZW50CiAgICAgIH0KICAgIH0sCgogICAgLy8g56e76ZmkSFRNTOagh+etvuS9huS/neeVmeWbvueJh+agh+etvgogICAgc3RyaXBIdG1sVGFnc0tlZXBJbWFnZXMoY29udGVudCkgewogICAgICBpZiAoIWNvbnRlbnQgfHwgdHlwZW9mIGNvbnRlbnQgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgaW1hZ2VzID0gW10KICAgICAgICBsZXQgaW1hZ2VJbmRleCA9IDAKICAgICAgICBjb25zdCBjb250ZW50V2l0aFBsYWNlaG9sZGVycyA9IGNvbnRlbnQucmVwbGFjZSgvPGltZ1tePl0qPi9naSwgKG1hdGNoKSA9PiB7CiAgICAgICAgICBpbWFnZXMucHVzaChtYXRjaCkKICAgICAgICAgIHJldHVybiBgXG5fX0lNQUdFX1BMQUNFSE9MREVSXyR7aW1hZ2VJbmRleCsrfV9fXG5gCiAgICAgICAgfSkKCiAgICAgICAgbGV0IHRleHRDb250ZW50ID0gY29udGVudFdpdGhQbGFjZWhvbGRlcnMKICAgICAgICAgIC5yZXBsYWNlKC88YnJccypcLz8+L2dpLCAnXG4nKQogICAgICAgICAgLnJlcGxhY2UoLzxcL3A+L2dpLCAnXG4nKQogICAgICAgICAgLnJlcGxhY2UoLzxwW14+XSo+L2dpLCAnXG4nKQogICAgICAgICAgLnJlcGxhY2UoLzxbXj5dKj4vZywgJycpCiAgICAgICAgICAucmVwbGFjZSgvXG5ccypcbi9nLCAnXG4nKQogICAgICAgICAgLy8g5aSE55CGSFRNTOWunuS9k+Wtl+espgogICAgICAgICAgLnJlcGxhY2UoLyZuYnNwOy9nLCAnICcpICAgICAgLy8g6Z2e5pat6KGM56m65qC8CiAgICAgICAgICAucmVwbGFjZSgvJmFtcDsvZywgJyYnKSAgICAgICAvLyAm56ym5Y+3CiAgICAgICAgICAucmVwbGFjZSgvJmx0Oy9nLCAnPCcpICAgICAgICAvLyDlsI/kuo7lj7cKICAgICAgICAgIC5yZXBsYWNlKC8mZ3Q7L2csICc+JykgICAgICAgIC8vIOWkp+S6juWPtwogICAgICAgICAgLnJlcGxhY2UoLyZxdW90Oy9nLCAnIicpICAgICAgLy8g5Y+M5byV5Y+3CiAgICAgICAgICAucmVwbGFjZSgvJiMzOTsvZywgIiciKSAgICAgICAvLyDljZXlvJXlj7cKICAgICAgICAgIC5yZXBsYWNlKC8maGVsbGlwOy9nLCAnLi4uJykgIC8vIOecgeeVpeWPtwogICAgICAgICAgLnJlcGxhY2UoLyZtZGFzaDsvZywgJ+KAlCcpICAgICAvLyDplb/noLTmipjlj7cKICAgICAgICAgIC5yZXBsYWNlKC8mbmRhc2g7L2csICfigJMnKSAgICAgLy8g55+t56C05oqY5Y+3CiAgICAgICAgICAucmVwbGFjZSgvJmxkcXVvOy9nLCAnIicpICAgICAvLyDlt6blj4zlvJXlj7cKICAgICAgICAgIC5yZXBsYWNlKC8mcmRxdW87L2csICciJykgICAgIC8vIOWPs+WPjOW8leWPtwogICAgICAgICAgLnJlcGxhY2UoLyZsc3F1bzsvZywgIiciKSAgICAgLy8g5bem5Y2V5byV5Y+3CiAgICAgICAgICAucmVwbGFjZSgvJnJzcXVvOy9nLCAiJyIpICAgICAvLyDlj7PljZXlvJXlj7cKICAgICAgICAgIC5yZXBsYWNlKC9ccysvZywgJyAnKSAgICAgICAgIC8vIOWkmuS4quepuueZveWtl+espuabv+aNouS4uuWNleS4quepuuagvAoKICAgICAgICBsZXQgZmluYWxDb250ZW50ID0gdGV4dENvbnRlbnQKICAgICAgICBpbWFnZXMuZm9yRWFjaCgoaW1nLCBpbmRleCkgPT4gewogICAgICAgICAgY29uc3QgcGxhY2Vob2xkZXIgPSBgX19JTUFHRV9QTEFDRUhPTERFUl8ke2luZGV4fV9fYAogICAgICAgICAgaWYgKGZpbmFsQ29udGVudC5pbmNsdWRlcyhwbGFjZWhvbGRlcikpIHsKICAgICAgICAgICAgZmluYWxDb250ZW50ID0gZmluYWxDb250ZW50LnJlcGxhY2UocGxhY2Vob2xkZXIsIGltZykKICAgICAgICAgIH0KICAgICAgICB9KQoKICAgICAgICByZXR1cm4gZmluYWxDb250ZW50LnRyaW0oKQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHJldHVybiBjb250ZW50CiAgICAgIH0KICAgIH0sCgogICAgLy8g5LuO6KGM5pWw57uE6Kej5p6Q6YCJ6aG5IC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBwYXJzZU9wdGlvbnNGcm9tTGluZXMobGluZXMsIHN0YXJ0SW5kZXgpIHsKICAgICAgY29uc3Qgb3B0aW9ucyA9IFtdCgogICAgICBpZiAoIUFycmF5LmlzQXJyYXkobGluZXMpIHx8IHN0YXJ0SW5kZXggPCAwIHx8IHN0YXJ0SW5kZXggPj0gbGluZXMubGVuZ3RoKSB7CiAgICAgICAgcmV0dXJuIHsgb3B0aW9ucyB9CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgZm9yIChsZXQgaSA9IHN0YXJ0SW5kZXg7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2ldCgogICAgICAgICAgaWYgKCFsaW5lIHx8IHR5cGVvZiBsaW5lICE9PSAnc3RyaW5nJykgewogICAgICAgICAgICBjb250aW51ZQogICAgICAgICAgfQoKICAgICAgICAgIC8vIOS9v+eUqOS4peagvOeahOmAiemhueihjOmqjOivgemAu+i+kQogICAgICAgICAgaWYgKHRoaXMuaXNPcHRpb25MaW5lKGxpbmUpKSB7CiAgICAgICAgICAgIGNvbnN0IG9wdGlvbk1hdGNoID0gbGluZS5tYXRjaCgvXihbQS1aYS16XSlbLjrvvJrvvI7jgIFdXHMqKC4qKS8pCiAgICAgICAgICAgIGlmIChvcHRpb25NYXRjaCkgewogICAgICAgICAgICAgIGNvbnN0IG9wdGlvbktleSA9IG9wdGlvbk1hdGNoWzFdLnRvVXBwZXJDYXNlKCkKICAgICAgICAgICAgICBjb25zdCBvcHRpb25Db250ZW50ID0gb3B0aW9uTWF0Y2hbMl0gPyBvcHRpb25NYXRjaFsyXS50cmltKCkgOiAnJwoKICAgICAgICAgICAgICBpZiAob3B0aW9uS2V5ICYmIG9wdGlvbkNvbnRlbnQpIHsKICAgICAgICAgICAgICAgIG9wdGlvbnMucHVzaCh7CiAgICAgICAgICAgICAgICAgIG9wdGlvbktleTogb3B0aW9uS2V5LAogICAgICAgICAgICAgICAgICBsYWJlbDogb3B0aW9uS2V5LAogICAgICAgICAgICAgICAgICBvcHRpb25Db250ZW50OiBvcHRpb25Db250ZW50LAogICAgICAgICAgICAgICAgICBjb250ZW50OiBvcHRpb25Db250ZW50CiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmlzQW5zd2VyTGluZShsaW5lKSB8fCB0aGlzLmlzRXhwbGFuYXRpb25MaW5lKGxpbmUpIHx8IHRoaXMuaXNEaWZmaWN1bHR5TGluZShsaW5lKSkgewogICAgICAgICAgICAvLyDpgYfliLDnrZTmoYjjgIHop6PmnpDmiJbpmr7luqbooYzvvIzlgZzmraLop6PmnpDpgInpobkKICAgICAgICAgICAgYnJlYWsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIOinhOiMg++8mumAiemhueS4jumAiemhueS5i+mXtO+8jOWPr+S7peaNouihjO+8jOS5n+WPr+S7peWcqOWQjOS4gOihjAogICAgICAgICAgICAvLyDlpoLmnpzpgInpobnlnKjlkIzkuIDooYzvvIzpgInpobnkuYvpl7Toh7PlsJHpnIDopoHmnInkuIDkuKrnqbrmoLwKICAgICAgICAgICAgLy8g5L2G5piv6KaB6YG/5YWN6K+v5bCG6aKY55uu5YaF5a655Lit55qE5a2X5q+NK+espuWPt+ivhuWIq+S4uumAiemhuQogICAgICAgICAgICAvLyDlj6rmnInlvZPooYzplb/luqbovoPnn63kuJTkuI3ljIXlkKvmj4/ov7DmgKfmloflrZfml7bmiY3lsJ3or5Xop6PmnpDlpJrpgInpobkKICAgICAgICAgICAgaWYgKGxpbmUubGVuZ3RoIDwgNTAgJiYgIS/ooajnpLp85pWw5o2ufOS4gOiIrHzpgJrluLh85Y+v5LulLy50ZXN0KGxpbmUpKSB7CiAgICAgICAgICAgICAgY29uc3QgbXVsdGlwbGVPcHRpb25zTWF0Y2ggPSBsaW5lLm1hdGNoKC8oW0EtWl1bLjrvvJrvvI7jgIFdXHMqW15cc10rKD86XHMrW0EtWl1bLjrvvJrvvI7jgIFdXHMqW15cc10rKSopL2cpCiAgICAgICAgICAgICAgaWYgKG11bHRpcGxlT3B0aW9uc01hdGNoKSB7CiAgICAgICAgICAgICAgICAvLyDlpITnkIblkIzkuIDooYzlpJrkuKrpgInpobnnmoTmg4XlhrUKICAgICAgICAgICAgICAgIGNvbnN0IHNpbmdsZU9wdGlvbnMgPSBsaW5lLnNwbGl0KC9ccysoPz1bQS1aYS16XVsuOu+8mu+8juOAgV0pLykKICAgICAgICAgICAgICAgIGZvciAoY29uc3Qgc2luZ2xlT3B0aW9uIG9mIHNpbmdsZU9wdGlvbnMpIHsKICAgICAgICAgICAgICAgICAgaWYgKCFzaW5nbGVPcHRpb24pIGNvbnRpbnVlCgogICAgICAgICAgICAgICAgICAvLyDkvb/nlKjkuKXmoLznmoTpgInpobnpqozor4HpgLvovpEKICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNPcHRpb25MaW5lKHNpbmdsZU9wdGlvbikpIHsKICAgICAgICAgICAgICAgICAgICBjb25zdCBtYXRjaCA9IHNpbmdsZU9wdGlvbi5tYXRjaCgvXihbQS1aYS16XSlbLjrvvJrvvI7jgIFdXHMqKC4qKS8pCiAgICAgICAgICAgICAgICAgICAgaWYgKG1hdGNoKSB7CiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBvcHRpb25LZXkgPSBtYXRjaFsxXS50b1VwcGVyQ2FzZSgpCiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBvcHRpb25Db250ZW50ID0gbWF0Y2hbMl0gPyBtYXRjaFsyXS50cmltKCkgOiAnJwoKICAgICAgICAgICAgICAgICAgICAgIGlmIChvcHRpb25LZXkgJiYgb3B0aW9uQ29udGVudCkgewogICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zLnB1c2goewogICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbktleTogb3B0aW9uS2V5LAogICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBvcHRpb25LZXksCiAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0aW9uQ29udGVudDogb3B0aW9uQ29udGVudCwKICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiBvcHRpb25Db250ZW50CiAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIC8vIOW/veeVpemUmeivrwogICAgICB9CgogICAgICByZXR1cm4geyBvcHRpb25zIH0KICAgIH0sCgogICAgLy8g5LuO6KGM5pWw57uE6Kej5p6Q6aKY55uu5YWD5L+h5oGvIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBwYXJzZVF1ZXN0aW9uTWV0YUZyb21MaW5lcyhsaW5lcywgcXVlc3Rpb24pIHsKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXQoKICAgICAgICAvLyDop4TojIPvvJrmmL7lvI/moIfms6jmoLzlvI/vvIjnrZTmoYjvvJrvvInvvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLogIjrvvJrjgIEi5YW25Lit5LmL5LiACiAgICAgICAgY29uc3QgYW5zd2VyTWF0Y2ggPSBsaW5lLm1hdGNoKC9e562U5qGIWy4677ya44CBXVxzKiguKykvKQogICAgICAgIGlmIChhbnN3ZXJNYXRjaCkgewogICAgICAgICAgcXVlc3Rpb24uY29ycmVjdEFuc3dlciA9IHRoaXMucGFyc2VBbnN3ZXJWYWx1ZShhbnN3ZXJNYXRjaFsxXSwgcXVlc3Rpb24ucXVlc3Rpb25UeXBlKQogICAgICAgICAgY29udGludWUKICAgICAgICB9CgogICAgICAgIC8vIOinhOiMg++8muino+aekOagvOW8j++8iOino+aekO+8mu+8ie+8jOWGkuWPt+WPr+S7peabv+aNouS4uiAiOu+8muOAgSLlhbbkuK3kuYvkuIAKICAgICAgICBjb25zdCBleHBsYW5hdGlvbk1hdGNoID0gbGluZS5tYXRjaCgvXuino+aekFsuOu+8muOAgV1ccyooLispLykKICAgICAgICBpZiAoZXhwbGFuYXRpb25NYXRjaCkgewogICAgICAgICAgcXVlc3Rpb24uZXhwbGFuYXRpb24gPSBleHBsYW5hdGlvbk1hdGNoWzFdLnRyaW0oKQogICAgICAgICAgY29udGludWUKICAgICAgICB9CgogICAgICAgIC8vIOinhOiMg++8mumavuW6puagvOW8j++8iOmavuW6pu+8mu+8ie+8jOWPquaUr+aMgeeugOWNleOAgeS4reetieOAgeWbsOmavuS4ieS4que6p+WIqwogICAgICAgIGNvbnN0IGRpZmZpY3VsdHlNYXRjaCA9IGxpbmUubWF0Y2goL17pmr7luqZbLjrvvJrjgIFdXHMqKOeugOWNlXzkuK3nrYl85Zuw6Zq+fOS4rSkvKQogICAgICAgIGlmIChkaWZmaWN1bHR5TWF0Y2gpIHsKICAgICAgICAgIGxldCBkaWZmaWN1bHR5ID0gZGlmZmljdWx0eU1hdGNoWzFdCiAgICAgICAgICAvLyDmoIflh4bljJbpmr7luqblgLzvvJrlsIYi5LitIue7n+S4gOS4uiLkuK3nrYkiCiAgICAgICAgICBpZiAoZGlmZmljdWx0eSA9PT0gJ+S4rScpIHsKICAgICAgICAgICAgZGlmZmljdWx0eSA9ICfkuK3nrYknCiAgICAgICAgICB9CiAgICAgICAgICAvLyDlj6rmjqXlj5fmoIflh4bnmoTkuInkuKrpmr7luqbnuqfliKsKICAgICAgICAgIGlmIChbJ+eugOWNlScsICfkuK3nrYknLCAn5Zuw6Zq+J10uaW5jbHVkZXMoZGlmZmljdWx0eSkpIHsKICAgICAgICAgICAgcXVlc3Rpb24uZGlmZmljdWx0eSA9IGRpZmZpY3VsdHkKICAgICAgICAgIH0KICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDop4TojIPvvJrnrZTmoYjmlK/mjIHnm7TmjqXlnKjpopjlubLkuK3moIfms6jvvIzkvJjlhYjku6XmmL7lvI/moIfms6jnmoTnrZTmoYjkuLrlh4YKICAgICAgLy8g5aaC5p6c5rKh5pyJ5om+5Yiw5pi+5byP562U5qGI77yM5bCd6K+V5LuO6aKY55uu5YaF5a655Lit5o+Q5Y+WCiAgICAgIGlmICghcXVlc3Rpb24uY29ycmVjdEFuc3dlcikgewogICAgICAgIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIgPSB0aGlzLmV4dHJhY3RBbnN3ZXJGcm9tUXVlc3Rpb25Db250ZW50KHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCwgcXVlc3Rpb24ucXVlc3Rpb25UeXBlKQogICAgICB9CiAgICB9LAoKICAgIC8vIOS7jumimOW5suS4reaPkOWPluetlOahiCAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgZXh0cmFjdEFuc3dlckZyb21RdWVzdGlvbkNvbnRlbnQocXVlc3Rpb25Db250ZW50LCBxdWVzdGlvblR5cGUpIHsKICAgICAgaWYgKCFxdWVzdGlvbkNvbnRlbnQgfHwgdHlwZW9mIHF1ZXN0aW9uQ29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICAvLyDop4TojIPvvJrpopjlubLkuK3moLzlvI/vvIjjgJBB44CR77yJ77yM5ous5Y+35Y+v5Lul5pu/5o2i5Li65Lit6Iux5paH55qE5bCP5ous5Y+35oiW6ICF5Lit5ous5Y+3CiAgICAgICAgY29uc3QgcGF0dGVybnMgPSBbCiAgICAgICAgICAv44CQKFte44CRXSsp44CRL2csICAgIC8vIOS4reaWh+aWueaLrOWPtwogICAgICAgICAgL1xbKFteXF1dKylcXS9nLCAgIC8vIOiLseaWh+aWueaLrOWPtwogICAgICAgICAgL++8iChbXu+8iV0rKe+8iS9nLCAgICAvLyDkuK3mloflnIbmi6zlj7cKICAgICAgICAgIC9cKChbXildKylcKS9nICAgICAvLyDoi7HmloflnIbmi6zlj7cKICAgICAgICBdCgogICAgICAgIGZvciAoY29uc3QgcGF0dGVybiBvZiBwYXR0ZXJucykgewogICAgICAgICAgY29uc3QgbWF0Y2hlcyA9IHF1ZXN0aW9uQ29udGVudC5tYXRjaChwYXR0ZXJuKQogICAgICAgICAgaWYgKG1hdGNoZXMgJiYgbWF0Y2hlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIC8vIOaPkOWPluacgOWQjuS4gOS4quWMuemFjemhueS9nOS4uuetlOahiO+8iOmAmuW4uOetlOahiOWcqOmimOebruacq+Wwvu+8iQogICAgICAgICAgICBjb25zdCBsYXN0TWF0Y2ggPSBtYXRjaGVzW21hdGNoZXMubGVuZ3RoIC0gMV0KICAgICAgICAgICAgY29uc3QgYW5zd2VyID0gbGFzdE1hdGNoLnJlcGxhY2UoL1vjgJDjgJFcW1xd77yI77yJKCldL2csICcnKS50cmltKCkKCiAgICAgICAgICAgIGlmIChhbnN3ZXIpIHsKICAgICAgICAgICAgICByZXR1cm4gdGhpcy5wYXJzZUFuc3dlclZhbHVlKGFuc3dlciwgcXVlc3Rpb25UeXBlKQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgLy8g5b+955Wl6ZSZ6K+vCiAgICAgICAgfQoKICAgICAgcmV0dXJuICcnCiAgICB9LAoKICAgIC8vIOino+aekOetlOahiOWAvAogICAgcGFyc2VBbnN3ZXJWYWx1ZShhbnN3ZXJUZXh0LCBxdWVzdGlvblR5cGUpIHsKICAgICAgaWYgKCFhbnN3ZXJUZXh0IHx8IHR5cGVvZiBhbnN3ZXJUZXh0ICE9PSAnc3RyaW5nJykgewogICAgICAgIHJldHVybiAnJwogICAgICB9CgogICAgICB0cnkgewogICAgICAgIGNvbnN0IHRyaW1tZWRBbnN3ZXIgPSBhbnN3ZXJUZXh0LnRyaW0oKQoKICAgICAgICBpZiAoIXRyaW1tZWRBbnN3ZXIpIHsKICAgICAgICAgIHJldHVybiAnJwogICAgICAgIH0KCiAgICAgICAgaWYgKHF1ZXN0aW9uVHlwZSA9PT0gJ2p1ZGdtZW50JykgewogICAgICAgICAgLy8g5Yik5pat6aKY562U5qGI5aSE55CGIC0g5L+d5oyB5Y6f5aeL5qC85byP77yM5LiN6L2s5o2i5Li6dHJ1ZS9mYWxzZQogICAgICAgICAgcmV0dXJuIHRyaW1tZWRBbnN3ZXIKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g6YCJ5oup6aKY562U5qGI5aSE55CGCiAgICAgICAgICByZXR1cm4gdHJpbW1lZEFuc3dlci50b1VwcGVyQ2FzZSgpCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgcmV0dXJuIGFuc3dlclRleHQgfHwgJycKICAgICAgICB9CiAgICB9LAoKCgoKCiAgICAvLyDojrflj5bmoLzlvI/ljJbnmoTpopjnm67lhoXlrrnvvIjmlK/mjIHlr4zmlofmnKzmoLzlvI/vvIkKICAgIGdldEZvcm1hdHRlZFF1ZXN0aW9uQ29udGVudChxdWVzdGlvbikgewogICAgICBpZiAoIXF1ZXN0aW9uIHx8ICFxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgbGV0IGNvbnRlbnQgPSBxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQKCiAgICAgIC8vIOWmguaenOaciUhUTUzlhoXlrrnkuJTljIXlkKvlr4zmlofmnKzmoIfnrb7vvIzkvJjlhYjkvb/nlKhIVE1M5YaF5a65CiAgICAgIGlmICh0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgJiYgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50LmluY2x1ZGVzKCc8JykpIHsKICAgICAgICAvLyDku45IVE1M5YaF5a655Lit5o+Q5Y+W5a+55bqU55qE6aKY55uu5YaF5a65CiAgICAgICAgY29uc3QgaHRtbENvbnRlbnQgPSB0aGlzLmV4dHJhY3RRdWVzdGlvbkZyb21IdG1sKHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCwgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50KQogICAgICAgIGlmIChodG1sQ29udGVudCkgewogICAgICAgICAgY29udGVudCA9IGh0bWxDb250ZW50CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDmuIXnkIbpopjlj7fvvJrnoa7kv53popjnm67lhoXlrrnkuI3ku6XmlbDlrZcr56ym5Y+35byA5aS0CiAgICAgIGNvbnRlbnQgPSB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKGNvbnRlbnQpCgogICAgICAvLyDmuIXnkIbpopjlnovmoIfor4bvvJrnp7vpmaTpopjnm67lhoXlrrnlvIDlpLTnmoRb6aKY5Z6LXeagh+ivhgogICAgICBjb250ZW50ID0gdGhpcy5yZW1vdmVRdWVzdGlvblR5cGUoY29udGVudCkKCiAgICAgIHJldHVybiB0aGlzLnByb2Nlc3NJbWFnZVBhdGhzKGNvbnRlbnQpCiAgICB9LAoKICAgIC8vIOiOt+WPlumimOWei+WQjeensAogICAgZ2V0UXVlc3Rpb25UeXBlTmFtZSh0eXBlKSB7CiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7CiAgICAgICAgJ3NpbmdsZSc6ICfljZXpgInpopgnLAogICAgICAgICdtdWx0aXBsZSc6ICflpJrpgInpopgnLAogICAgICAgICdqdWRnbWVudCc6ICfliKTmlq3popgnCiAgICAgIH0KICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZV0gfHwgJ+acquefpScKICAgIH0sCgogICAgLy8g5riF55CG6aKY55uu5YaF5a655Lit55qE6aKY5Y+3CiAgICByZW1vdmVRdWVzdGlvbk51bWJlcihjb250ZW50KSB7CiAgICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gY29udGVudAogICAgICB9CgogICAgICAvLyDlpITnkIZIVE1M5YaF5a65CiAgICAgIGlmIChjb250ZW50LmluY2x1ZGVzKCc8JykpIHsKICAgICAgICAvLyDlr7nkuo5IVE1M5YaF5a6577yM6ZyA6KaB5riF55CG5qCH562+5YaF55qE6aKY5Y+3CiAgICAgICAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSgvPHBbXj5dKj4oXHMqXGQrWy4677ya77yO44CBXVxzKikoLio/KTxcL3A+L2dpLCAnPHA+JDI8L3A+JykKICAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL14oXHMqXGQrWy4677ya77yO44CBXVxzKikvLCAnJykgLy8g5riF55CG5byA5aS055qE6aKY5Y+3CiAgICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC8+XHMqXGQrWy4677ya77yO44CBXVxzKi9nLCAnPicpIC8vIOa4heeQhuagh+etvuWQjueahOmimOWPtwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWvueS6jue6r+aWh+acrOWGheWuue+8jOebtOaOpea4heeQhuW8gOWktOeahOmimOWPtwogICAgICAgIHJldHVybiBjb250ZW50LnJlcGxhY2UoL15ccypcZCtbLjrvvJrvvI7jgIFdXHMqLywgJycpLnRyaW0oKQogICAgICB9CiAgICB9LAoKICAgIC8vIOa4heeQhumimOebruWGheWuueS4reeahOmimOWei+agh+ivhgogICAgcmVtb3ZlUXVlc3Rpb25UeXBlKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewogICAgICAgIHJldHVybiBjb250ZW50CiAgICAgIH0KCiAgICAgIC8vIOWkhOeQhkhUTUzlhoXlrrkKICAgICAgaWYgKGNvbnRlbnQuaW5jbHVkZXMoJzwnKSkgewogICAgICAgIC8vIOWvueS6jkhUTUzlhoXlrrnvvIzmuIXnkIbmoIfnrb7lhoXnmoTpopjlnovmoIfor4YKICAgICAgICByZXR1cm4gY29udGVudC5yZXBsYWNlKC88cFtePl0qPihccypcWy4qP+mimFxdXHMqKSguKj8pPFwvcD4vZ2ksICc8cD4kMjwvcD4nKQogICAgICAgICAgICAgICAgICAgICAucmVwbGFjZSgvXihccypcWy4qP+mimFxdXHMqKS8sICcnKSAvLyDmuIXnkIblvIDlpLTnmoTpopjlnovmoIfor4YKICAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoLz5ccypcWy4qP+mimFxdXHMqL2csICc+JykgLy8g5riF55CG5qCH562+5ZCO55qE6aKY5Z6L5qCH6K+GCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5a+55LqO57qv5paH5pys5YaF5a6577yM5riF55CG5byA5aS055qE6aKY5Z6L5qCH6K+GCiAgICAgICAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSgvXlxzKlxbLio/6aKYXF1ccyovLCAnJykudHJpbSgpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5LuOSFRNTOWGheWuueS4reaPkOWPluWvueW6lOeahOmimOebruWGheWuuQogICAgZXh0cmFjdFF1ZXN0aW9uRnJvbUh0bWwocGxhaW5Db250ZW50LCBodG1sQ29udGVudCkgewogICAgICBpZiAoIXBsYWluQ29udGVudCB8fCAhaHRtbENvbnRlbnQpIHsKICAgICAgICByZXR1cm4gcGxhaW5Db250ZW50CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8g566A5Y2V55qE5Yy56YWN562W55Wl77ya5p+l5om+5YyF5ZCr6aKY55uu5YaF5a6555qESFRNTOauteiQvQogICAgICAgIGNvbnN0IHBsYWluVGV4dCA9IHBsYWluQ29udGVudC5yZXBsYWNlKC9eXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKCiAgICAgICAgLy8g5ZyoSFRNTOWGheWuueS4reafpeaJvuWMheWQq+i/meS4quaWh+acrOeahOauteiQvQogICAgICAgIGNvbnN0IHBhcmFncmFwaHMgPSBodG1sQ29udGVudC5tYXRjaCgvPHBbXj5dKj4uKj88XC9wPi9naSkgfHwgW10KCiAgICAgICAgZm9yIChjb25zdCBwYXJhZ3JhcGggb2YgcGFyYWdyYXBocykgewogICAgICAgICAgY29uc3QgcGFyYWdyYXBoVGV4dCA9IHBhcmFncmFwaC5yZXBsYWNlKC88W14+XSo+L2csICcnKS50cmltKCkKICAgICAgICAgIC8vIOa4heeQhuauteiQveaWh+acrOS4reeahOmimOWPt+WGjei/m+ihjOWMuemFjQogICAgICAgICAgY29uc3QgY2xlYW5QYXJhZ3JhcGhUZXh0ID0gcGFyYWdyYXBoVGV4dC5yZXBsYWNlKC9eXHMqXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKICAgICAgICAgIGlmIChjbGVhblBhcmFncmFwaFRleHQuaW5jbHVkZXMocGxhaW5UZXh0LnN1YnN0cmluZygwLCAyMCkpKSB7CiAgICAgICAgICAgIC8vIOaJvuWIsOWMuemFjeeahOauteiQve+8jOi/lOWbnkhUTUzmoLzlvI/vvIjkvYbopoHmuIXnkIbpopjlj7fvvIkKICAgICAgICAgICAgcmV0dXJuIHRoaXMucmVtb3ZlUXVlc3Rpb25OdW1iZXIocGFyYWdyYXBoKQogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgcmV0dXJuIHBsYWluQ29udGVudAogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHJldHVybiBwbGFpbkNvbnRlbnQKICAgICAgfQogICAgfSwKCgogICAgLy8g5pCc57SiCiAgICBoYW5kbGVTZWFyY2goKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgfSwKICAgIC8vIOmHjee9ruaQnOe0ogogICAgcmVzZXRTZWFyY2goKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucXVlc3Rpb25UeXBlID0gbnVsbAogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRpZmZpY3VsdHkgPSBudWxsCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucXVlc3Rpb25Db250ZW50ID0gbnVsbAogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuhBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;;AAGA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/biz/questionBank", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"handleBatchImportClick\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n                :loading=\"importingQuestions\"\n              >\n                <i class=\"el-icon-upload2\"></i>\n                {{ importingQuestions ? '正在导入...' : '导入题目' }}\n              </el-button>\n\n              <div class=\"import-options\">\n                <el-checkbox\n                  v-model=\"importOptions.reverse\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后将按题目顺序倒序导入，即最后一题先导入\" placement=\"top\">\n                    <span>按题目顺序倒序导入</span>\n                  </el-tooltip>\n                </el-checkbox>\n\n                <el-checkbox\n                  v-model=\"importOptions.allowDuplicate\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后允许导入重复的题目内容，否则会跳过重复题目\" placement=\"top\">\n                    <span>允许题目重复</span>\n                  </el-tooltip>\n                </el-checkbox>\n              </div>\n\n              <div v-if=\"importingQuestions\" class=\"import-progress\">\n                <el-progress\n                  :percentage=\"importProgress\"\n                  :show-text=\"true\"\n                  :format=\"formatProgress\"\n                  status=\"success\"\n                  :stroke-width=\"6\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <!-- <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button> -->\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载Word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1. 建议使用新版Office或WPS软件编辑题目文件，仅支持上传.docx格式的文件<br>\n          2. 题目数量过多、题目文件过大等情况建议分批导入<br>\n          3. 需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport { listQuestion, delQuestion, getQuestionStatistics, batchImportQuestions, exportQuestionsToWord } from '@/api/biz/question'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      expandedQuestions: [],\n      // 选择状态\n      selectedQuestions: [],\n      isAllSelected: false,\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '',\n      parsedQuestions: [],\n      parseErrors: [],\n      allExpanded: true,\n      isSettingFromBackend: false,\n      lastParsedContent: '', // 记录上次解析的内容，避免重复解析\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importingQuestions: false,\n      importProgress: 0,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听路由变化，当路由参数改变时重新初始化页面\n    '$route'(to, from) {\n      // 只有当路由参数中的bankId发生变化时才重新初始化\n      if (to.query.bankId !== from.query.bankId) {\n        this.initPage()\n      }\n    },\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n        // 如果已经有解析结果且内容没有实质性变化，不重新解析\n        if (this.parsedQuestions.length > 0 && this.lastParsedContent &&\n            this.stripHtmlTagsKeepImages(newVal) === this.stripHtmlTagsKeepImages(this.lastParsedContent)) {\n          return\n        }\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时清空所有内容并初始化编辑器\n          this.clearImportContent()\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数 - 增加延时到2秒，减少卡顿\n    this.debounceParseDocument = this.debounce(this.parseDocument, 2000)\n    // 创建编辑器内容变化的防抖函数 - 延时1.5秒\n    this.debounceEditorContentChange = this.debounce(this.handleEditorContentChangeDebounced, 1500)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n    // 取消所有防抖函数\n    if (this.debounceParseDocument && this.debounceParseDocument.cancel) {\n      this.debounceParseDocument.cancel()\n    }\n    if (this.debounceEditorContentChange && this.debounceEditorContentChange.cancel) {\n      this.debounceEditorContentChange.cancel()\n    }\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n\n      // 重置查询参数，确保分页从第一页开始\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: bankId,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      }\n\n      // 重置其他状态\n      this.questionList = []\n      this.total = 0\n      this.expandedQuestions = []\n      this.selectedQuestions = []\n      this.expandAll = false\n\n      // 重新获取数据\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(() => {\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(() => {\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n\n\n    // 处理批量导题按钮点击\n    handleBatchImportClick() {\n      this.importDrawerVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      // 确认导出\n      this.$confirm(`确认导出题库\"${this.bankName}\"中的所有题目吗？`, '导出确认', {\n        confirmButtonText: '确定导出',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        const loading = this.$loading({\n          lock: true,\n          text: `正在导出题库中的所有题目...`,\n          spinner: 'el-icon-loading',\n          background: 'rgba(0, 0, 0, 0.7)'\n        })\n\n        // 调用导出API - 导出当前题库的所有题目\n        exportQuestionsToWord({\n          bankId: this.bankId,\n          bankName: this.bankName\n        }).then(response => {\n          loading.close()\n\n          // 创建下载链接\n          const blob = new Blob([response], {\n            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n          })\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n          link.download = `${this.bankName}.docx`\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          this.$message.success(`成功导出题库\"${this.bankName}\"`)\n        }).catch(error => {\n          loading.close()\n          console.error('导出失败:', error)\n          this.$message.error('导出失败，请重试')\n        })\n      }).catch(() => {\n        // 用户取消导出\n      })\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除（优化版本）\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      const deleteCount = this.selectedQuestions.length\n      let confirmMessage = `确认删除选中的 ${deleteCount} 道题目吗？`\n\n      if (deleteCount > 20) {\n        confirmMessage += '\\n\\n注意：题目较多，删除可能需要一些时间，请耐心等待。'\n      }\n\n      this.$confirm(confirmMessage, '批量删除', {\n        confirmButtonText: '确定删除',\n        cancelButtonText: '取消',\n        type: 'warning',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.performBatchDelete()\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 执行批量删除\n    async performBatchDelete() {\n      const deleteCount = this.selectedQuestions.length\n      const loading = this.$loading({\n        lock: true,\n        text: `正在删除 ${deleteCount} 道题目，请稍候...`,\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      })\n\n      try {\n        // 使用真正的批量删除API\n        const questionIds = this.selectedQuestions.join(',')\n        const startTime = Date.now()\n\n        await delQuestion(questionIds) // 调用批量删除API\n\n        const endTime = Date.now()\n        const duration = ((endTime - startTime) / 1000).toFixed(1)\n\n        loading.close()\n        this.$message.success(`成功删除 ${deleteCount} 道题目 (耗时 ${duration}s)`)\n\n        // 清理选择状态\n        this.selectedQuestions = []\n        this.isAllSelected = false\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        loading.close()\n        console.error('批量删除失败:', error)\n\n        let errorMessage = '批量删除失败'\n        if (error.response && error.response.data && error.response.data.msg) {\n          errorMessage = error.response.data.msg\n        } else if (error.message) {\n          errorMessage = error.message\n        }\n\n        this.$message.error(errorMessage)\n      }\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        // 收起题目\n        this.expandedQuestions.splice(index, 1)\n        // 如果当前是\"展开所有\"状态，则取消\"展开所有\"状态\n        if (this.expandAll) {\n          this.expandAll = false\n          // 将其他题目添加到expandedQuestions数组中，除了当前要收起的题目\n          this.questionList.forEach(question => {\n            if (question.questionId !== questionId && !this.expandedQuestions.includes(question.questionId)) {\n              this.expandedQuestions.push(question.questionId)\n            }\n          })\n        }\n      } else {\n        // 展开题目\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(() => {\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      // 检查是否有未保存的内容\n      const hasContent = this.documentContent && this.documentContent.trim().length > 0\n      const hasParsedQuestions = this.parsedQuestions && this.parsedQuestions.length > 0\n\n      if (hasContent || hasParsedQuestions) {\n        let message = '关闭后将丢失当前编辑的内容，确认关闭吗？'\n        if (hasParsedQuestions) {\n          message = `当前已解析出 ${this.parsedQuestions.length} 道题目，关闭后将丢失所有内容，确认关闭吗？`\n        }\n\n        this.$confirm(message, '确认关闭', {\n          confirmButtonText: '确定关闭',\n          cancelButtonText: '继续编辑',\n          type: 'warning'\n        }).then(() => {\n          // 清空内容\n          this.clearImportContent()\n          done()\n        }).catch(() => {\n          // 取消关闭，继续编辑\n        })\n      } else {\n        // 没有内容直接关闭\n        done()\n      }\n    },\n\n    // 清空导入内容\n    clearImportContent() {\n      // 清空文档内容\n      this.documentContent = ''\n      this.documentHtmlContent = ''\n\n      // 清空解析结果\n      this.parsedQuestions = []\n      this.parseErrors = []\n\n      // 重置解析状态\n      this.allExpanded = true\n      this.isSettingFromBackend = false\n      this.lastParsedContent = '' // 清空上次解析的内容记录\n\n      // 重置上传状态\n      this.isUploading = false\n      this.isParsing = false\n      this.importingQuestions = false\n      this.importProgress = 0\n\n      // 重置导入选项\n      this.importOptions = {\n        reverse: false,\n        allowDuplicate: false\n      }\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response) {\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n          this.lastParsedContent = response.originalContent // 记录已解析的内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 5000) // 延长到5秒，确保编辑器内容稳定\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError() {\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      // 构建确认信息\n      let confirmMessage = `确认导入 ${this.parsedQuestions.length} 道题目吗？`\n      let optionMessages = []\n\n      if (this.importOptions.reverse) {\n        optionMessages.push('将按倒序导入')\n      }\n      if (this.importOptions.allowDuplicate) {\n        optionMessages.push('允许重复题目')\n      }\n\n      if (optionMessages.length > 0) {\n        confirmMessage += `\\n\\n导入选项：${optionMessages.join('，')}`\n      }\n\n      this.$confirm(confirmMessage, '确认导入', {\n        confirmButtonText: '确定导入',\n        cancelButtonText: '取消',\n        type: 'info',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      this.importingQuestions = true\n      this.importProgress = 0\n\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n          this.$message.info('已按倒序排列题目')\n        }\n\n        // 模拟进度更新\n        this.importProgress = 10\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate,\n          reverse: this.importOptions.reverse\n        }\n\n        this.importProgress = 30\n\n        const response = await batchImportQuestions(importData)\n\n        this.importProgress = 80\n\n        if (response.code === 200) {\n          this.importProgress = 100\n\n          // 显示详细的导入结果\n          const result = response.data || {}\n          const successCount = result.successCount || 0\n          const failCount = result.failCount || 0\n          const skippedCount = result.skippedCount || 0\n\n          // 构建结果消息\n          let resultMessage = `导入完成：成功 ${successCount} 道`\n\n          if (failCount > 0) {\n            resultMessage += `，失败 ${failCount} 道`\n          }\n\n          if (skippedCount > 0) {\n            resultMessage += `，跳过重复 ${skippedCount} 道`\n          }\n\n          resultMessage += ' 题目'\n\n          // 根据结果类型显示不同的消息\n          if (failCount > 0 || skippedCount > 0) {\n            this.$message.warning(resultMessage)\n          } else {\n            this.$message.success(resultMessage)\n          }\n\n          // 如果有错误信息，显示详情\n          if (result.errors && result.errors.length > 0) {\n            console.warn('导入详情:', result.errors)\n\n            // 如果有跳过的题目，可以显示更详细的信息\n            if (skippedCount > 0) {\n              const skippedErrors = result.errors.filter(error => error.includes('重复跳过'))\n              if (skippedErrors.length > 0) {\n                console.info('跳过的重复题目:', skippedErrors)\n              }\n            }\n          }\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n\n        // 清理状态并关闭抽屉\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        console.error('导入题目失败:', error)\n        this.$message.error('导入失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.importingQuestions = false\n        this.importProgress = 0\n      }\n    },\n\n    // 格式化进度显示\n    formatProgress(percentage) {\n      if (percentage === 100) {\n        return '导入完成'\n      } else if (percentage >= 80) {\n        return '正在保存...'\n      } else if (percentage >= 30) {\n        return '正在处理...'\n      } else {\n        return '准备中...'\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              on: {\n                instanceReady: function(evt) {\n                  const editor = evt.editor\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                }\n              }\n            })\n          } catch (error) {\n            this.fallbackToTextarea()\n            return\n          }\n\n          // 监听内容变化 - 使用防抖优化性能\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('key', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('instanceReady', () => {\n              this.editorInitialized = true\n              this.richEditor.setData('')\n            })\n          }\n        })\n\n      } catch (error) {\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 处理编辑器内容变化（防抖后执行）\n    handleEditorContentChangeDebounced() {\n      if (!this.richEditor || !this.editorInitialized) {\n        return\n      }\n\n      try {\n        const rawContent = this.richEditor.getData()\n        const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n        this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n        this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n      } catch (error) {\n        console.warn('编辑器内容处理失败:', error)\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = '' // 确保文本框为空\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化 - 使用防抖优化性能\n        textarea.addEventListener('input', (e) => {\n          // 立即更新内容，但防抖解析\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        this.documentContent = content\n        this.documentHtmlContent = content\n      }\n    },\n\n\n\n    // 防抖函数 - 优化版本，支持取消\n    debounce(func, wait) {\n      let timeout\n      const debounced = function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          timeout = null\n          func.apply(this, args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n\n      // 添加取消方法\n      debounced.cancel = function() {\n        clearTimeout(timeout)\n        timeout = null\n      }\n\n      return debounced\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n        // 记录解析成功的内容，避免重复解析\n        this.lastParsedContent = this.documentContent\n      } catch (error) {\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n        if (lines.length === 0) {\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号和题型标识\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.questionContent = this.removeQuestionType(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      // 严格验证：避免误将题目内容中的字母+符号识别为选项\n      if (!line || line.length > 200) {\n        return false\n      }\n\n      const match = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n      if (match) {\n        const optionKey = match[1].toUpperCase()\n        const optionContent = match[2] ? match[2].trim() : ''\n\n        // 严格验证条件：\n        // 1. 选项字母必须是A-Z单个字母\n        // 2. 选项内容长度合理（1-100字符）\n        // 3. 排除明显的题目内容描述（如包含\"表示\"、\"数据\"等词汇的长句）\n        if (/^[A-Z]$/.test(optionKey) && optionContent.length > 0 && optionContent.length <= 100) {\n          // 排除明显的题目内容描述\n          const excludePatterns = [\n            /表示.*?数据/,     // 排除\"表示...数据\"这类描述\n            /一般用.*?或/,      // 排除\"一般用...或\"这类描述\n            /通常.*?来/,       // 排除\"通常...来\"这类描述\n            /可以.*?进行/,     // 排除\"可以...进行\"这类描述\n            /.*?坐标.*?表示/   // 排除\"坐标...表示\"这类描述\n          ]\n\n          const isDescriptiveText = excludePatterns.some(pattern => pattern.test(optionContent))\n          return !isDescriptiveText\n        }\n      }\n      return false\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          return `<img${before}src=\"${fullSrc}\"${after}>`\n        })\n\n        return processedContent\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')\n          .replace(/<\\/p>/gi, '\\n')\n          .replace(/<p[^>]*>/gi, '\\n')\n          .replace(/<[^>]*>/g, '')\n          .replace(/\\n\\s*\\n/g, '\\n')\n          // 处理HTML实体字符\n          .replace(/&nbsp;/g, ' ')      // 非断行空格\n          .replace(/&amp;/g, '&')       // &符号\n          .replace(/&lt;/g, '<')        // 小于号\n          .replace(/&gt;/g, '>')        // 大于号\n          .replace(/&quot;/g, '\"')      // 双引号\n          .replace(/&#39;/g, \"'\")       // 单引号\n          .replace(/&hellip;/g, '...')  // 省略号\n          .replace(/&mdash;/g, '—')     // 长破折号\n          .replace(/&ndash;/g, '–')     // 短破折号\n          .replace(/&ldquo;/g, '\"')     // 左双引号\n          .replace(/&rdquo;/g, '\"')     // 右双引号\n          .replace(/&lsquo;/g, \"'\")     // 左单引号\n          .replace(/&rsquo;/g, \"'\")     // 右单引号\n          .replace(/\\s+/g, ' ')         // 多个空白字符替换为单个空格\n\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 使用严格的选项行验证逻辑\n          if (this.isOptionLine(line)) {\n            const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n            if (optionMatch) {\n              const optionKey = optionMatch[1].toUpperCase()\n              const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n              if (optionKey && optionContent) {\n                options.push({\n                  optionKey: optionKey,\n                  label: optionKey,\n                  optionContent: optionContent,\n                  content: optionContent\n                })\n              }\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            // 但是要避免误将题目内容中的字母+符号识别为选项\n            // 只有当行长度较短且不包含描述性文字时才尝试解析多选项\n            if (line.length < 50 && !/表示|数据|一般|通常|可以/.test(line)) {\n              const multipleOptionsMatch = line.match(/([A-Z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Z][.:：．、]\\s*[^\\s]+)*)/g)\n              if (multipleOptionsMatch) {\n                // 处理同一行多个选项的情况\n                const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n                for (const singleOption of singleOptions) {\n                  if (!singleOption) continue\n\n                  // 使用严格的选项验证逻辑\n                  if (this.isOptionLine(singleOption)) {\n                    const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                    if (match) {\n                      const optionKey = match[1].toUpperCase()\n                      const optionContent = match[2] ? match[2].trim() : ''\n\n                      if (optionKey && optionContent) {\n                        options.push({\n                          optionKey: optionKey,\n                          label: optionKey,\n                          optionContent: optionContent,\n                          content: optionContent\n                        })\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        // 忽略错误\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n          // 忽略错误\n        }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n          return answerText || ''\n        }\n    },\n\n\n\n\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      // 清理题型标识：移除题目内容开头的[题型]标识\n      content = this.removeQuestionType(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 清理题目内容中的题型标识\n    removeQuestionType(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，清理标签内的题型标识\n        return content.replace(/<p[^>]*>(\\s*\\[.*?题\\]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\[.*?题\\]\\s*)/, '') // 清理开头的题型标识\n                     .replace(/>\\s*\\[.*?题\\]\\s*/g, '>') // 清理标签后的题型标识\n      } else {\n        // 对于纯文本内容，清理开头的题型标识\n        return content.replace(/^\\s*\\[.*?题\\]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        return plainContent\n      } catch (error) {\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 导入选项样式 */\n.import-options {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  margin-top: 15px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.import-options .el-checkbox {\n  margin-right: 0;\n  margin-bottom: 0;\n}\n\n.import-options .el-checkbox__label {\n  font-size: 14px;\n  color: #333;\n  font-weight: 500;\n}\n\n.import-options .el-tooltip {\n  cursor: help;\n}\n\n.import-progress {\n  margin-top: 20px;\n  padding: 15px;\n  background: #fff;\n  border-radius: 6px;\n  border: 1px solid #e1f5fe;\n}\n\n.import-progress .el-progress {\n  margin-bottom: 0;\n}\n\n.import-progress .el-progress__text {\n  font-size: 14px !important;\n  font-weight: 500;\n  color: #409eff;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n</style>\n"]}]}